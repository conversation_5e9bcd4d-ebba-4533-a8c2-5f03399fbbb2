#!/bin/bash
# 脚本用途：根据指定版本更新jar后重新构建Docker镜像并启动固定名称的容器
# 可配置区域
JAR_NAME="new_discovery-0.0.1-SNAPSHOT"
HOST_PORT=9300
HOST_JMX_PORT=7900
APP_NAME="new-discovery"
HOST_DIR="/home/<USER>/apps/${APP_NAME}"
HOST_IP=$(hostname -I | awk '{print $1}')

# 固定配置
CONTAINER_PORT=8080
CONTAINER_JMX_PORT=7900
CONTAINER_LOGS="/home/<USER>/logs"
CONTAINER_CONF="/home/<USER>/conf"
DOCKERFILE_PATH="./Dockerfile"

# 校验JMX配置文件
check_jmx_files() {
    local password_file="${HOST_DIR}/conf/jmxremote.password"
    local access_file="${HOST_DIR}/conf/jmxremote.access"

    if [ ! -d "${HOST_DIR}/conf" ]; then
        echo "错误：JMX配置目录不存在 ${HOST_DIR}/conf"
        exit 1
    fi
    if [ ! -f "${password_file}" ]; then
        echo "错误：未找到JMX密码文件 ${password_file}"
        exit 1
    fi
    if [ ! -f "${access_file}" ]; then
        echo "错误：未找到JMX访问控制文件 ${access_file}"
        exit 1
    fi
    if [ $(stat -c %a "${password_file}") -ne 600 ]; then
        echo "错误：JMX密码文件权限必须为600"
        exit 1
    fi
}

if [ -z "$1" ]; then
    echo "请提供版本号，例如: ./run.sh 1.0.0"
    exit 1
fi


VERSION="$1"

check_jmx_files

# 检查 Dockerfile 是否存在
if [ ! -f "$DOCKERFILE_PATH" ]; then
    echo "错误：未找到 Dockerfile，请确认在正确的目录下执行脚本。"
    exit 1
fi

# 删除原有容器（如果存在）
if [ "$(docker ps -a -q -f name=^/${APP_NAME}$)" ]; then
    echo "检测到已有容器 ${APP_NAME}，正在停止并删除..."
    docker stop ${APP_NAME} && docker rm ${APP_NAME}
fi

# 构建 Docker 镜像，并传入版本构建参数
echo "开始构建 Docker 镜像 ${APP_NAME}:${VERSION} ..."
docker build -t ${APP_NAME}:${VERSION} -f ${DOCKERFILE_PATH} \
  --build-arg VERSION=${VERSION} \
  --build-arg APP_NAME=${JAR_NAME} \
  --build-arg HOST_UID=$(id -u) \
  --build-arg HOST_GID=$(id -g) .


if [ $? -ne 0 ]; then
    echo "错误：Docker 镜像构建失败。"
    exit 1
fi

# 检查宿主机日志目录
if [ ! -d "${HOST_DIR}/logs" ]; then
    echo "创建日志目录：${HOST_DIR}/logs"
    mkdir -p ${HOST_DIR}/logs
    chmod 755 ${HOST_DIR}/logs
fi

# 启动 Docker 容器
echo "启动容器 ${APP_NAME} ..."
docker run -d \
  --name ${APP_NAME} \
  --restart=unless-stopped \
  -p ${HOST_PORT}:${CONTAINER_PORT} \
  -p ${HOST_JMX_PORT}:${CONTAINER_JMX_PORT} \
  -v ${HOST_DIR}/logs:${CONTAINER_LOGS} \
  -v ${HOST_DIR}/conf:${CONTAINER_CONF} \
  -e SCHEDULER_ENABLED=true \
  -e JMX_HOST=${HOST_IP} \
  --memory=4g \
  --cpus=2 \
  ${APP_NAME}:${VERSION}

if [ $? -eq 0 ]; then
    echo "容器 ${APP_NAME}已成功启动，访问端口：${HOST_PORT}，JMX端口：${HOST_JMX_PORT}"
else
    echo "错误：容器启动失败。"
fi
