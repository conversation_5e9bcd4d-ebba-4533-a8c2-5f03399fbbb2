#dockerfile
FROM eclipse-temurin:17-jre-jammy

ARG VERSION=latest
ARG APP_NAME=app
ARG HOST_UID=1000
ARG HOST_GID=1000

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN groupadd -g ${HOST_GID} app && \
    useradd -u ${HOST_UID} -g ${HOST_GID} -m -d /home/<USER>
    mkdir -p /home/<USER>/logs && \
    mkdir -p /home/<USER>/conf && \
    chown app:app /home/<USER>/logs && \
    chown app:app /home/<USER>/conf


WORKDIR /home/<USER>
USER app

# 根据构建参数复制对应版本的 jar 包
COPY --chown=app:app ./target/${APP_NAME}.jar app.jar

RUN chown -R app:app /home/<USER>/logs && \
    chmod 755 /home/<USER>/logs

#HEALTHCHECK --interval=30s --timeout=3s \
#  CMD touch /home/<USER>/logs/healthcheck.test && rm -f /home/<USER>/logs/healthcheck.test

ENV LOGS_DIR=/home/<USER>/logs \
    JAVA_OPTS="-Xmx2g -Xms1g -XX:+UnlockDiagnosticVMOptions \
               -Dcom.sun.management.jmxremote \
               -Dcom.sun.management.jmxremote.port=7900 \
               -Dcom.sun.management.jmxremote.rmi.port=7900 \
               -Dcom.sun.management.jmxremote.authenticate=true \
               -Dcom.sun.management.jmxremote.password.file=/home/<USER>/conf/jmxremote.password \
               -Dcom.sun.management.jmxremote.access.file=/home/<USER>/conf/jmxremote.access \
               -Dcom.sun.management.jmxremote.ssl=false"

ENV JMX_HOST=localhost

ENTRYPOINT exec /bin/sh -c ' \
  exec java ${JAVA_OPTS} \
    -Djava.rmi.server.hostname=${JMX_HOST} \
    -Djava.rmi.server.hostname=${CONTAINER_IP} \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/home/<USER>/logs \
    -Xlog:gc*:file=/home/<USER>/logs/gc.log:time,uptime,level,tags:filecount=5,filesize=10M \
    -Dlogging.file.path=${LOGS_DIR} \
    -jar app.jar'

EXPOSE 8080 7900
