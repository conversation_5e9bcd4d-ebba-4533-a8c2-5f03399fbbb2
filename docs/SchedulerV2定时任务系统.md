# SchedulerV2 定时任务系统

## 概述

SchedulerV2 是一个通用的定时任务调度系统，提供了灵活的任务注册、执行、监控和管理功能。系统支持多种执行器类型、重试机制、失败报警等企业级特性。

## 系统架构

### 核心组件
```
SchedulerService (服务接口层 - CRUD 门面)
    ↓
TaskManager (持久化层 - 任务存储和管理)
    ↓
TaskExecutor (执行层 - 任务执行器)
    ↓
ThreadPool (资源层 - 线程池)
```

### 架构职责
- **SchedulerService**: 提供 CRUD 操作的门面，不存储任何状态
- **TaskManager**: 负责任务的持久化存储、查询和生命周期管理
- **TaskExecutor**: 负责任务的实际执行
- **ThreadPool**: 提供线程资源

### 数据流向
```
业务模块 → SchedulerService → TaskManager → TaskExecutor → 任务执行
```

## 架构设计原则

### 1. 分层职责清晰
- **SchedulerService**: 纯粹的 CRUD 门面，不维护任何内存状态
- **TaskManager**: 作为持久化层，负责任务的存储、查询和生命周期管理
- **TaskExecutor**: 专注于任务执行，不关心任务存储

### 2. 无状态服务
- SchedulerService 不存储任何 taskRunnables 或 taskDefinitions
- 所有状态都由 TaskManager 管理
- 便于水平扩展和集群部署

### 3. 单一职责
- 每个组件只负责自己的核心职责
- SchedulerService 只做 CRUD 操作的委托
- TaskManager 专注于数据管理
- TaskExecutor 专注于执行逻辑

## 核心功能

### 1. 任务注册
- 支持通过 `@ScheduledTask` 注解自动注册
- 支持通过 `SchedulerService` 编程式注册
- 支持动态注册和注销任务
- TaskManager 负责任务的持久化存储

### 2. 任务调度
- 基于 Cron 表达式的时间调度
- 支持多种执行器类型（CPU密集型、IO密集型）
- 智能的线程池管理
- TaskManager 管理任务的生命周期

### 3. 任务监控
- 实时任务状态查询
- 任务执行统计
- 失败报警机制
- 通过 SchedulerService 提供统一的查询接口

### 4. 任务管理
- 手动触发任务
- 动态启用/禁用任务
- 任务配置管理
- SchedulerService 作为 CRUD 操作的门面

## API 接口

### 认证说明

所有 API 接口都需要在请求头中携带固定的 Token 进行认证：

```
X-Scheduler-Token: your-scheduler-token
```

Token 值通过统一的安全配置文件设置：
```yaml
security:
  modules:
    scheduler:
      token:
        keys:
          - "your-scheduler-token"
          - "another-valid-token"
        param-name: "token"
        allow-header-fallback: true
        exclude-paths: []
```

认证由 `SchedulerTokenFilter` 过滤器统一处理，如果 Token 验证失败，将返回：
```json
{
  "code": 4001,
  "message": "Invalid token",
  "data": null,
  "timestamp": 1703123456789
}
```

### 1. 获取调度器状态

```http
GET /api/scheduler/status
Headers: X-Scheduler-Token: your-scheduler-token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalTasks": 15,
    "enabledTasks": 15,
    "disabledTasks": 0,
    "status": "RUNNING",
    "statusDescription": "调度器正在运行",
    "timestamp": 1703123456789
  },
  "timestamp": 1703123456789
}
```

> 注意：调度器在生产环境中始终启用，无需返回 `enabled` 字段。

### 2. 获取所有任务列表

```http
GET /api/scheduler/tasks
Headers: X-Scheduler-Token: your-scheduler-token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalTasks": 15,
    "enabledTasks": 15,
    "disabledTasks": 0,
    "tasks": [
      {
        "taskId": "hrcost-yida-sync-housingSubsidyAppService",
        "name": "人力成本模型-宜搭数据同步-HousingSubsidy",
        "description": "自动同步 HousingSubsidy 数据从宜搭到数据库",
        "cronExpression": "0 0 2 * * ?",
        "taskType": "IO_INTENSIVE",
        "priority": 5,
        "maxRetries": 3,
        "retryInterval": 5000,
        "alertOnFailure": true,
        "executorType": "queue"
      }
    ]
  },
  "timestamp": 1703123456789
}
```

### 3. 获取任务详情

```http
GET /api/scheduler/tasks/{taskId}
Headers: X-Scheduler-Token: your-scheduler-token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "hrcost-yida-sync-housingSubsidyAppService",
    "name": "人力成本模型-宜搭数据同步-HousingSubsidy",
    "description": "自动同步 HousingSubsidy 数据从宜搭到数据库",
    "cronExpression": "0 0 2 * * ?",
    "taskType": "IO_INTENSIVE",
    "priority": 5,
    "maxRetries": 3,
    "retryInterval": 5000,
    "alertOnFailure": true,
    "executorType": "queue"
  },
  "timestamp": 1703123456789
}
```

### 4. 手动触发任务

```http
POST /api/scheduler/tasks/{taskId}/trigger
Headers: X-Scheduler-Token: your-scheduler-token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "hrcost-yida-sync-housingSubsidyAppService",
    "taskName": "人力成本模型-宜搭数据同步-HousingSubsidy",
    "success": true,
    "message": "任务执行成功",
    "startTime": 1703123456789,
    "endTime": 1703123459789,
    "duration": 3000
  },
  "timestamp": 1703123456789
}
```

### 5. 注销任务

```http
DELETE /api/scheduler/tasks/{taskId}
Headers: X-Scheduler-Token: your-scheduler-token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1703123456789
}
```

## 编程式使用

### 1. 注册任务

```java
@Service
public class MyService {
    
    @Autowired
    private SchedulerService schedulerService;
    
    public void registerMyTask() {
        TaskDefinition taskDefinition = TaskDefinition.builder()
                .taskId("my-custom-task")
                .name("我的自定义任务")
                .description("执行自定义业务逻辑")
                .cronExpression("0 0 1 * * ?")  // 每天凌晨1点
                .schedulerTaskType(TaskDefinition.SchedulerTaskType.CPU_INTENSIVE)
                .priority(5)
                .maxRetries(3)
                .retryInterval(5000)
                .alertOnFailure(true)
                .executorType("queue")
                .build();
        
        Runnable task = () -> {
            // 任务执行逻辑
            log.info("执行自定义任务");
        };
        
        schedulerService.registerTask(taskDefinition, task);
    }
}
```

### 2. 注解式注册

```java
@Service
public class MyService {
    
    @ScheduledTask(
            name = "我的定时任务",
            description = "定期执行的业务任务",
            cron = "0 0 2 * * ?",
            maxRetries = 3,
            retryInterval = 5000,
            alertOnFailure = true
    )
    public void myScheduledMethod() {
        // 任务执行逻辑
        log.info("执行定时任务");
    }
}
```

## 配置说明

### 基础配置

```yaml
# 统一的安全配置
security:
  modules:
    scheduler:
      token:
        keys:
          - "your-scheduler-token"
          - "backup-token"
        param-name: "token"
        allow-header-fallback: true
        exclude-paths: []
    yida:
      token:
        keys:
          - "yida-token"
        param-name: "token"
        allow-header-fallback: true
        exclude-paths: []

# SchedulerV2 线程池配置
scheduler:
  thread-pool:
    cpu-core-size: 4
    cpu-max-size: 8
    io-core-size: 8
    io-max-size: 16
    queue-capacity: 100
    keep-alive-seconds: 60
```

### 配置说明

- `keys`: 有效的 Token 列表，支持多个 Token
- `param-name`: URL 参数名称，默认为 "token"
- `allow-header-fallback`: 是否允许从请求头获取 Token，默认为 true
- `exclude-paths`: 排除校验的路径列表

> 注意：调度器在生产环境中始终启用，无需配置 `enabled` 参数。

### 任务配置

```yaml
scheduler:
  tasks:
    # 全局默认配置
    default:
      max-retries: 3
      retry-interval: 5000
      alert-on-failure: true
      
    # 特定任务配置
    my-task:
      enabled: true
      cron: "0 0 1 * * ?"
      max-retries: 5
      priority: 8
```

## 最佳实践

### 1. 任务命名规范
- 使用模块前缀：`{module}-{function}-{detail}`
- 示例：`hrcost-yida-sync-housingSubsidy`

### 2. 执行器选择
- **CPU_INTENSIVE**：计算密集型任务
- **IO_INTENSIVE**：IO密集型任务（数据库、网络）

### 3. 错误处理
- 合理设置重试次数和间隔
- 开启失败报警
- 记录详细的执行日志

### 4. 性能优化
- 避免长时间运行的任务
- 合理设置任务优先级
- 监控线程池使用情况

## 监控运维

### 1. 日志监控
- 任务注册日志
- 任务执行日志
- 错误和异常日志

### 2. 指标监控
- 任务执行成功率
- 任务执行耗时
- 线程池使用率

### 3. 报警机制
- 任务执行失败报警
- 系统异常报警
- 性能指标报警

## 扩展开发

### 1. 自定义执行器
实现 `TaskExecutor` 接口：

```java
@Component
public class MyCustomExecutor implements TaskExecutor {
    
    @Override
    public String getType() {
        return "custom";
    }
    
    @Override
    public void execute(TaskDefinition taskDefinition, Runnable task) {
        // 自定义执行逻辑
    }
}
```

### 2. 自定义报警
实现 `TaskAlertService` 接口：

```java
@Component
public class MyAlertService implements TaskAlertService {
    
    @Override
    public void sendAlert(TaskDefinition taskDefinition, Exception exception) {
        // 自定义报警逻辑
    }
}
```

## 故障排查

### 常见问题
1. **任务未执行**：检查 Cron 表达式格式
2. **任务执行失败**：查看错误日志和重试配置
3. **性能问题**：监控线程池使用情况
4. **内存泄漏**：检查任务是否正确释放资源

### 排查步骤
1. 检查调度器状态：
   ```bash
   curl -H "X-Scheduler-Token: your-token" http://localhost:8080/api/scheduler/status
   ```
2. 查看任务列表：
   ```bash
   curl -H "X-Scheduler-Token: your-token" http://localhost:8080/api/scheduler/tasks
   ```
3. 检查任务详情：
   ```bash
   curl -H "X-Scheduler-Token: your-token" http://localhost:8080/api/scheduler/tasks/{taskId}
   ```
4. 查看应用日志
5. 手动触发任务测试：
   ```bash
   curl -X POST -H "X-Scheduler-Token: your-token" http://localhost:8080/api/scheduler/tasks/{taskId}/trigger
   ```

> 注意：调度器在生产环境中始终运行，所有 API 调用都需要携带正确的 Token。

## 注意事项

1. **线程安全**：确保任务执行逻辑是线程安全的
2. **资源管理**：及时释放数据库连接、文件句柄等资源
3. **异常处理**：妥善处理任务执行中的异常
4. **性能影响**：避免在任务中执行耗时过长的操作
5. **数据一致性**：注意并发执行时的数据一致性问题
