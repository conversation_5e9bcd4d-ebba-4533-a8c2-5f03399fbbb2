# 人力成本模型 (HRCost)

## 概述

人力成本模型是新发现系统中的核心业务模块，负责管理餐饮企业的人力成本相关数据，包括工资标准、奖金制度、社保参保、住房补贴等多个维度的成本核算。

## 模块架构

### 技术架构
```
interfaces (接口层)
    ↓
application (应用层)
    ↓
domain (领域层)
    ↓
infrastructure (基础设施层)
```

### 数据流向
```
宜搭表单 → YidaAdapter → BaseAppService → JpaAdapter → PostgreSQL
```

## 核心实体

### 1. 基础实体
- **BaseEntity**: 所有实体的抽象基类，包含通用字段
- **BaseAppService**: 所有应用服务的抽象基类，提供统一的数据同步功能

### 2. 业务实体

#### **薪酬相关**
- **EmployeeSalaryStandard** (员工工资标准): 不同岗位的基础工资标准
- **DepartmentLeaderSalaryStandard** (部门负责人工资标准): 管理岗位的工资标准
- **LegalHolidaySalary** (法假工资): 法定节假日工资计算标准

#### **奖金绩效**
- **ALevelStoreBonus** (A级门店奖金): A级门店的奖金分配标准
- **AccountingPerformance** (账管绩效): 账务管理岗位的绩效奖金
- **TakeawayPackagingPerformance** (外卖打包绩效): 外卖打包岗位的绩效奖金
- **ValueAssessment** (人均产值考核标准): 基于人均产值的考核奖金

#### **福利保障**
- **HousingSubsidy** (住房补贴): 员工住房补贴标准
- **SocialSecurityCoverage** (社保参保率): 企业社保缴纳标准和参保率

#### **组织管理**
- **PostAllocation** (岗位编制): 不同规模门店的人员配置标准
- **RevenueRange** (营收区间): 门店营收分级标准

## 数据同步机制

### 自动同步
系统通过 `HrcostSchedulerService` 自动注册所有 BaseAppService 子类的定时任务：

- **执行时间**: 每天凌晨2点
- **同步方向**: 宜搭表单 → 数据库
- **重试机制**: 最多3次重试，间隔5秒
- **失败报警**: 自动发送钉钉报警

### 手动同步
通过 schedulerV2 提供的统一 REST API 支持手动触发同步。

人力成本模型的定时任务都注册到 schedulerV2 中，任务ID格式为：`hrcost-yida-sync-{beanName}`

例如：
- `hrcost-yida-sync-housingSubsidyAppService`
- `hrcost-yida-sync-employeeSalaryStandardAppService`

具体的 API 使用方法请参考 schedulerV2 的接口文档。

## 数据库设计

### 表命名规范
- 前缀: `xfx_`
- 命名: 下划线分隔的小写字母
- 示例: `xfx_housing_subsidy`

### 通用字段
所有表都包含以下通用字段：
- `id`: 主键ID (BIGSERIAL)
- `object_id`: 宜搭对象ID (VARCHAR)
- `del_flag`: 逻辑删除标志 (VARCHAR)
- `create_time`: 创建时间 (TIMESTAMP)
- `update_time`: 更新时间 (TIMESTAMP)
- `sync_time`: 同步时间 (TIMESTAMP)

### 业务字段
根据具体业务需求定义，常见类型：
- 金额字段: `Float`
- 文本字段: `VARCHAR(255)`
- 整数字段: `INTEGER`
- 日期字段: `LocalDateTime`

## 配置管理

### 宜搭配置
每个实体类通过 `@FormEntity` 注解配置宜搭表单信息：
```java
@FormEntity(
    value = "FORM-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    appType = "APP_JV4TMC5QSTTSQN72RJ3F",
    sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
```

### 字段映射
通过 `@FormField` 和 `@Column` 注解实现双重映射：
```java
@FormField("numberField_xxxxxxxx")
@Column(name = "salary_standard")
private Float salaryStandard;
```

## 业务流程

### 1. 数据录入
- 业务人员在宜搭表单中录入/修改数据
- 数据实时保存到宜搭平台

### 2. 数据同步
- 定时任务每天凌晨自动同步
- 支持手动触发同步
- 同步过程包含数据对齐、逻辑删除、新增更新

### 3. 数据应用
- 其他系统通过 API 获取人力成本数据
- 支持成本核算、预算分析等业务场景

## 扩展开发

### 添加新实体
1. 创建实体类继承 `BaseEntity`
2. 添加 `@FormEntity` 和字段映射注解
3. 创建 Repository 接口继承 `BaseRepository`
4. 实现 JPA 和 Yida 两个 Adapter
5. 创建 AppService 继承 `BaseAppService`
6. 系统会自动注册定时任务

### 自定义同步逻辑
如需特殊的同步逻辑，可以在具体的 AppService 中重写 `yidaBatchToDb()` 方法。

## 监控运维

### 日志监控
- 任务注册日志: 应用启动时输出
- 执行日志: 记录每次同步的开始、结束、耗时
- 错误日志: 详细记录异常信息和堆栈

### 报警机制
- 任务执行失败自动发送钉钉报警
- 包含失败原因和重试信息
- 支持@指定人员

## 注意事项

1. **数据一致性**: 同步过程会覆盖数据库数据，确保宜搭数据准确性
2. **网络依赖**: 依赖宜搭 API 网络连接，网络异常时任务会失败并重试
3. **并发控制**: 避免同时执行多个同步任务
4. **数据备份**: 重要数据变更前建议备份
5. **权限管理**: 确保宜搭账号有足够的数据访问权限

