-- ========================================
-- 人力成本模型 - PostgreSQL 建表脚本
-- 每个表的DDL、索引、注释放在一起
-- 共15个表，使用 BigDecimal 精度
-- ========================================

-- ========================================
-- 1. A级门店奖金标准表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_a_level_store_bonus (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    general_staff_bonus DECIMAL(10,2),
    reception_cashier_bonus DECIMAL(10,2),
    supervisor_bonus DECIMAL(10,2),
    revenue_range VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_a_level_store_bonus_object_id ON xfx_a_level_store_bonus(object_id);
CREATE INDEX IF NOT EXISTS idx_a_level_store_bonus_del_flag ON xfx_a_level_store_bonus(del_flag);
CREATE INDEX IF NOT EXISTS idx_a_level_store_bonus_revenue_range ON xfx_a_level_store_bonus(revenue_range);

COMMENT ON TABLE xfx_a_level_store_bonus IS '人力成本模型-A级门店奖金标准表单';
COMMENT ON COLUMN xfx_a_level_store_bonus.id IS '主键ID';
COMMENT ON COLUMN xfx_a_level_store_bonus.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_a_level_store_bonus.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_a_level_store_bonus.create_time IS '创建时间';
COMMENT ON COLUMN xfx_a_level_store_bonus.update_time IS '更新时间';
COMMENT ON COLUMN xfx_a_level_store_bonus.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_a_level_store_bonus.general_staff_bonus IS '人力成本模型-服务员/水吧/冷菜/蒸包灶/传菜/切配/打荷/PA/后勤奖金';
COMMENT ON COLUMN xfx_a_level_store_bonus.reception_cashier_bonus IS '人力成本模型-迎宾/收银/炉台奖金';
COMMENT ON COLUMN xfx_a_level_store_bonus.supervisor_bonus IS '人力成本模型-领班/档口主管/面点副主管奖金';
COMMENT ON COLUMN xfx_a_level_store_bonus.revenue_range IS '营收区间';

-- ========================================
-- 2. 账管绩效表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_accounting_performance (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    total DECIMAL(10,2),
    person_count DECIMAL(10,0),
    performance_bonus_base DECIMAL(10,2),
    revenue_range VARCHAR(255),
    brand VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_accounting_performance_object_id ON xfx_accounting_performance(object_id);
CREATE INDEX IF NOT EXISTS idx_accounting_performance_del_flag ON xfx_accounting_performance(del_flag);
CREATE INDEX IF NOT EXISTS idx_accounting_performance_brand ON xfx_accounting_performance(brand);

COMMENT ON TABLE xfx_accounting_performance IS '人力成本模型-账管绩效表单';
COMMENT ON COLUMN xfx_accounting_performance.id IS '主键ID';
COMMENT ON COLUMN xfx_accounting_performance.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_accounting_performance.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_accounting_performance.create_time IS '创建时间';
COMMENT ON COLUMN xfx_accounting_performance.update_time IS '更新时间';
COMMENT ON COLUMN xfx_accounting_performance.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_accounting_performance.total IS '人力成本模型-合计';
COMMENT ON COLUMN xfx_accounting_performance.person_count IS '人力成本模型-人数';
COMMENT ON COLUMN xfx_accounting_performance.performance_bonus_base IS '人力成本模型-账管绩效奖金基数';
COMMENT ON COLUMN xfx_accounting_performance.revenue_range IS '账管营收区间';
COMMENT ON COLUMN xfx_accounting_performance.brand IS '品牌';

-- ========================================
-- 3. 部门负责人工资标准表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_department_leader_salary_standard (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    sous_chef DECIMAL(10,2),
    supervisor DECIMAL(10,2),
    chef_head DECIMAL(10,2),
    front_office_manager DECIMAL(10,2),
    revenue_range VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_department_leader_salary_object_id ON xfx_department_leader_salary_standard(object_id);
CREATE INDEX IF NOT EXISTS idx_department_leader_salary_del_flag ON xfx_department_leader_salary_standard(del_flag);
CREATE INDEX IF NOT EXISTS idx_department_leader_salary_revenue_range ON xfx_department_leader_salary_standard(revenue_range);

COMMENT ON TABLE xfx_department_leader_salary_standard IS '人力成本模型-部门负责人工资标准表单';
COMMENT ON COLUMN xfx_department_leader_salary_standard.id IS '主键ID';
COMMENT ON COLUMN xfx_department_leader_salary_standard.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_department_leader_salary_standard.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_department_leader_salary_standard.create_time IS '创建时间';
COMMENT ON COLUMN xfx_department_leader_salary_standard.update_time IS '更新时间';
COMMENT ON COLUMN xfx_department_leader_salary_standard.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_department_leader_salary_standard.sous_chef IS '人力成本模型-副厨工资';
COMMENT ON COLUMN xfx_department_leader_salary_standard.supervisor IS '人力成本模型-主管工资';
COMMENT ON COLUMN xfx_department_leader_salary_standard.chef_head IS '人力成本模型-厨师长工资';
COMMENT ON COLUMN xfx_department_leader_salary_standard.front_office_manager IS '人力成本模型-前厅经理工资';
COMMENT ON COLUMN xfx_department_leader_salary_standard.revenue_range IS '营收区间';

-- ========================================
-- 4. 员工工资标准表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_employee_salary_standard (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    salary_standard DECIMAL(10,2),
    post VARCHAR(255),
    business_district_level VARCHAR(255),
    brand VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_employee_salary_standard_object_id ON xfx_employee_salary_standard(object_id);
CREATE INDEX IF NOT EXISTS idx_employee_salary_standard_del_flag ON xfx_employee_salary_standard(del_flag);
CREATE INDEX IF NOT EXISTS idx_employee_salary_standard_brand ON xfx_employee_salary_standard(brand);
CREATE INDEX IF NOT EXISTS idx_employee_salary_standard_business_district ON xfx_employee_salary_standard(business_district_level);

COMMENT ON TABLE xfx_employee_salary_standard IS '人力成本模型-员工工资标准表单';
COMMENT ON COLUMN xfx_employee_salary_standard.id IS '主键ID';
COMMENT ON COLUMN xfx_employee_salary_standard.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_employee_salary_standard.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_employee_salary_standard.create_time IS '创建时间';
COMMENT ON COLUMN xfx_employee_salary_standard.update_time IS '更新时间';
COMMENT ON COLUMN xfx_employee_salary_standard.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_employee_salary_standard.salary_standard IS '人力成本模型-工资标准';
COMMENT ON COLUMN xfx_employee_salary_standard.post IS '岗位';
COMMENT ON COLUMN xfx_employee_salary_standard.business_district_level IS '商圈等级';
COMMENT ON COLUMN xfx_employee_salary_standard.brand IS '品牌';

-- ========================================
-- 5. 住房补贴表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_housing_subsidy (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    manager_chef_housing_subsidy DECIMAL(10,2),
    total DECIMAL(10,2),
    dormitory_bed_cost DECIMAL(10,2),
    housing_subsidy_standard DECIMAL(10,2),
    business_district_level VARCHAR(255),
    brand VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_housing_subsidy_object_id ON xfx_housing_subsidy(object_id);
CREATE INDEX IF NOT EXISTS idx_housing_subsidy_del_flag ON xfx_housing_subsidy(del_flag);
CREATE INDEX IF NOT EXISTS idx_housing_subsidy_brand ON xfx_housing_subsidy(brand);
CREATE INDEX IF NOT EXISTS idx_housing_subsidy_business_district ON xfx_housing_subsidy(business_district_level);

COMMENT ON TABLE xfx_housing_subsidy IS '人力成本模型-住房补贴表单';
COMMENT ON COLUMN xfx_housing_subsidy.id IS '主键ID';
COMMENT ON COLUMN xfx_housing_subsidy.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_housing_subsidy.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_housing_subsidy.create_time IS '创建时间';
COMMENT ON COLUMN xfx_housing_subsidy.update_time IS '更新时间';
COMMENT ON COLUMN xfx_housing_subsidy.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_housing_subsidy.manager_chef_housing_subsidy IS '人力成本模型-经理厨师长住房补贴';
COMMENT ON COLUMN xfx_housing_subsidy.total IS '人力成本模型-合计';
COMMENT ON COLUMN xfx_housing_subsidy.dormitory_bed_cost IS '人力成本模型-宿舍租赁床位成本';
COMMENT ON COLUMN xfx_housing_subsidy.housing_subsidy_standard IS '人力成本模型-住房补贴标准';
COMMENT ON COLUMN xfx_housing_subsidy.business_district_level IS '商圈等级';
COMMENT ON COLUMN xfx_housing_subsidy.brand IS '品牌';

-- ========================================
-- 6. 法假工资表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_legal_holiday_salary (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    legal_holiday_salary_total DECIMAL(10,2),
    holiday_days DECIMAL(10,2),
    holiday_salary_coefficient DECIMAL(10,4),
    province VARCHAR(255),
    month TIMESTAMP
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_legal_holiday_salary_object_id ON xfx_legal_holiday_salary(object_id);
CREATE INDEX IF NOT EXISTS idx_legal_holiday_salary_del_flag ON xfx_legal_holiday_salary(del_flag);
CREATE INDEX IF NOT EXISTS idx_legal_holiday_salary_province ON xfx_legal_holiday_salary(province);
CREATE INDEX IF NOT EXISTS idx_legal_holiday_salary_month ON xfx_legal_holiday_salary(month);

COMMENT ON TABLE xfx_legal_holiday_salary IS '人力成本模型-法假工资表单';
COMMENT ON COLUMN xfx_legal_holiday_salary.id IS '主键ID';
COMMENT ON COLUMN xfx_legal_holiday_salary.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_legal_holiday_salary.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_legal_holiday_salary.create_time IS '创建时间';
COMMENT ON COLUMN xfx_legal_holiday_salary.update_time IS '更新时间';
COMMENT ON COLUMN xfx_legal_holiday_salary.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_legal_holiday_salary.legal_holiday_salary_total IS '人力成本模型-法假工资合计';
COMMENT ON COLUMN xfx_legal_holiday_salary.holiday_days IS '人力成本模型-法假天数';
COMMENT ON COLUMN xfx_legal_holiday_salary.holiday_salary_coefficient IS '人力成本模型-法假工资系数';
COMMENT ON COLUMN xfx_legal_holiday_salary.province IS '省份';
COMMENT ON COLUMN xfx_legal_holiday_salary.month IS '月份';

-- ========================================
-- 7. 岗位编制表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_post_allocation (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    allocation_500_plus DECIMAL(10,0),
    allocation_400_500 DECIMAL(10,0),
    allocation_300_400 DECIMAL(10,0),
    allocation_250_300 DECIMAL(10,0),
    allocation_250_less DECIMAL(10,0),
    post_type VARCHAR(255),
    post_name VARCHAR(255),
    revenue DECIMAL(12,2),
    hidden_revenue VARCHAR(255),
    brand VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_post_allocation_object_id ON xfx_post_allocation(object_id);
CREATE INDEX IF NOT EXISTS idx_post_allocation_del_flag ON xfx_post_allocation(del_flag);
CREATE INDEX IF NOT EXISTS idx_post_allocation_brand ON xfx_post_allocation(brand);
CREATE INDEX IF NOT EXISTS idx_post_allocation_post_type ON xfx_post_allocation(post_type);

COMMENT ON TABLE xfx_post_allocation IS '人力成本模型-岗位编制表单';
COMMENT ON COLUMN xfx_post_allocation.id IS '主键ID';
COMMENT ON COLUMN xfx_post_allocation.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_post_allocation.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_post_allocation.create_time IS '创建时间';
COMMENT ON COLUMN xfx_post_allocation.update_time IS '更新时间';
COMMENT ON COLUMN xfx_post_allocation.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_post_allocation.allocation_500_plus IS '人力成本模型-500平以上定编人数';
COMMENT ON COLUMN xfx_post_allocation.allocation_400_500 IS '人力成本模型-400平-500平以内定编人数';
COMMENT ON COLUMN xfx_post_allocation.allocation_300_400 IS '人力成本模型-300平-400平以内定编人数';
COMMENT ON COLUMN xfx_post_allocation.allocation_250_300 IS '人力成本模型-250平-300平以内定编人数';
COMMENT ON COLUMN xfx_post_allocation.allocation_250_less IS '人力成本模型-250平以内定编人数';
COMMENT ON COLUMN xfx_post_allocation.post_type IS '岗位类型';
COMMENT ON COLUMN xfx_post_allocation.post_name IS '岗位';
COMMENT ON COLUMN xfx_post_allocation.revenue IS '人力成本模型-营收';
COMMENT ON COLUMN xfx_post_allocation.hidden_revenue IS '营收（隐藏）';
COMMENT ON COLUMN xfx_post_allocation.brand IS '品牌';

-- ========================================
-- 8. 营收区间表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_revenue_range (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    revenue_range_a_store VARCHAR(255),
    revenue_range_takeaway VARCHAR(255),
    revenue_range_accounting VARCHAR(255),
    revenue_range_description VARCHAR(255),
    revenue DECIMAL(12,2),
    hidden_revenue VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_revenue_range_object_id ON xfx_revenue_range(object_id);
CREATE INDEX IF NOT EXISTS idx_revenue_range_del_flag ON xfx_revenue_range(del_flag);

COMMENT ON TABLE xfx_revenue_range IS '人力成本模型-营收区间表单';
COMMENT ON COLUMN xfx_revenue_range.id IS '主键ID';
COMMENT ON COLUMN xfx_revenue_range.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_revenue_range.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_revenue_range.create_time IS '创建时间';
COMMENT ON COLUMN xfx_revenue_range.update_time IS '更新时间';
COMMENT ON COLUMN xfx_revenue_range.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_revenue_range.revenue_range_a_store IS 'A级门店奖金-营收区间';
COMMENT ON COLUMN xfx_revenue_range.revenue_range_takeaway IS '外卖-岗还原营收区间';
COMMENT ON COLUMN xfx_revenue_range.revenue_range_accounting IS '账管-营收区间';
COMMENT ON COLUMN xfx_revenue_range.revenue_range_description IS '营收区间';
COMMENT ON COLUMN xfx_revenue_range.revenue IS '人力成本模型-营收';
COMMENT ON COLUMN xfx_revenue_range.hidden_revenue IS '营收（隐藏）';

-- ========================================
-- 9. 企业社保及参保率表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_social_security_coverage (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    coverage_rate DECIMAL(8,4),
    full_time_employees DECIMAL(10,0),
    total_insured_employees DECIMAL(10,0),
    total_company_contribution_amount DECIMAL(12,2),
    total_company_contribution_ratio DECIMAL(8,4),
    serious_illness_amount DECIMAL(10,2),
    work_injury_amount_company DECIMAL(10,2),
    work_injury_ratio_company DECIMAL(8,4),
    work_injury_base DECIMAL(10,2),
    unemployment_amount_company DECIMAL(10,2),
    unemployment_ratio_company DECIMAL(8,4),
    unemployment_base DECIMAL(10,2),
    maternity_amount_company DECIMAL(10,2),
    maternity_ratio_company DECIMAL(8,4),
    maternity_base DECIMAL(10,2),
    medical_amount_company DECIMAL(10,2),
    medical_ratio_company DECIMAL(8,4),
    medical_base DECIMAL(10,2),
    pension_amount_company DECIMAL(10,2),
    pension_ratio_company DECIMAL(8,4),
    pension_base DECIMAL(10,2),
    brand VARCHAR(255),
    province VARCHAR(255),
    city VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_social_security_coverage_object_id ON xfx_social_security_coverage(object_id);
CREATE INDEX IF NOT EXISTS idx_social_security_coverage_del_flag ON xfx_social_security_coverage(del_flag);
CREATE INDEX IF NOT EXISTS idx_social_security_coverage_brand ON xfx_social_security_coverage(brand);
CREATE INDEX IF NOT EXISTS idx_social_security_coverage_province ON xfx_social_security_coverage(province);
CREATE INDEX IF NOT EXISTS idx_social_security_coverage_city ON xfx_social_security_coverage(city);

COMMENT ON TABLE xfx_social_security_coverage IS '人力成本模型-企业社保及参保率表单';
COMMENT ON COLUMN xfx_social_security_coverage.id IS '主键ID';
COMMENT ON COLUMN xfx_social_security_coverage.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_social_security_coverage.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_social_security_coverage.create_time IS '创建时间';
COMMENT ON COLUMN xfx_social_security_coverage.update_time IS '更新时间';
COMMENT ON COLUMN xfx_social_security_coverage.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_social_security_coverage.coverage_rate IS '人力成本模型-参保率';
COMMENT ON COLUMN xfx_social_security_coverage.full_time_employees IS '人力成本模型-全职在职人数';
COMMENT ON COLUMN xfx_social_security_coverage.total_insured_employees IS '人力成本模型-社保参保总人数';
COMMENT ON COLUMN xfx_social_security_coverage.total_company_contribution_amount IS '人力成本模型-企业缴纳总金额';
COMMENT ON COLUMN xfx_social_security_coverage.total_company_contribution_ratio IS '人力成本模型-企业缴纳总比例';
COMMENT ON COLUMN xfx_social_security_coverage.serious_illness_amount IS '人力成本模型-大病';
COMMENT ON COLUMN xfx_social_security_coverage.work_injury_amount_company IS '人力成本模型-工伤险金额（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.work_injury_ratio_company IS '人力成本模型-工伤险比例（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.work_injury_base IS '人力成本模型-工伤险基数';
COMMENT ON COLUMN xfx_social_security_coverage.unemployment_amount_company IS '人力成本模型-失业险金额（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.unemployment_ratio_company IS '人力成本模型-失业险比例（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.unemployment_base IS '人力成本模型-失业险基数';
COMMENT ON COLUMN xfx_social_security_coverage.maternity_amount_company IS '人力成本模型-生育险金额（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.maternity_ratio_company IS '人力成本模型-生育险比例（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.maternity_base IS '人力成本模型-生育险基数';
COMMENT ON COLUMN xfx_social_security_coverage.medical_amount_company IS '人力成本模型-医疗险金额（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.medical_ratio_company IS '人力成本模型-医疗险比例（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.medical_base IS '人力成本模型-医疗险基数';
COMMENT ON COLUMN xfx_social_security_coverage.pension_amount_company IS '人力成本模型-养老险金额（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.pension_ratio_company IS '人力成本模型-养老险比例（企业）';
COMMENT ON COLUMN xfx_social_security_coverage.pension_base IS '人力成本模型-养老险基数';
COMMENT ON COLUMN xfx_social_security_coverage.brand IS '品牌';
COMMENT ON COLUMN xfx_social_security_coverage.province IS '省份';
COMMENT ON COLUMN xfx_social_security_coverage.city IS '城市';

-- ========================================
-- 10. 外卖打包绩效表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_takeaway_packaging_performance (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    total DECIMAL(10,2),
    person_count DECIMAL(10,0),
    packaging_bonus_base DECIMAL(10,2),
    takeaway_revenue_range VARCHAR(255),
    restored_revenue_range VARCHAR(255),
    brand VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_takeaway_packaging_performance_object_id ON xfx_takeaway_packaging_performance(object_id);
CREATE INDEX IF NOT EXISTS idx_takeaway_packaging_performance_del_flag ON xfx_takeaway_packaging_performance(del_flag);
CREATE INDEX IF NOT EXISTS idx_takeaway_packaging_performance_brand ON xfx_takeaway_packaging_performance(brand);

COMMENT ON TABLE xfx_takeaway_packaging_performance IS '人力成本模型-外卖打包绩效表单';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.id IS '主键ID';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.create_time IS '创建时间';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.update_time IS '更新时间';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.total IS '人力成本模型-合计';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.person_count IS '人力成本模型-人数';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.packaging_bonus_base IS '人力成本模型-外卖打包岗奖金基数';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.takeaway_revenue_range IS '外卖营收区间';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.restored_revenue_range IS '还原营收区间';
COMMENT ON COLUMN xfx_takeaway_packaging_performance.brand IS '品牌';

-- ========================================
-- 11. 人均产值考核标准表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_value_assessment (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    bonus_xia_sha DECIMAL(10,2),
    bonus_ke_qiao DECIMAL(10,2),
    bonus_hu_die_li DECIMAL(10,2),
    bonus_kao_jiang DECIMAL(10,2),
    bonus_cde DECIMAL(10,2),
    bonus_ab DECIMAL(10,2),
    value_range VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_value_assessment_object_id ON xfx_value_assessment(object_id);
CREATE INDEX IF NOT EXISTS idx_value_assessment_del_flag ON xfx_value_assessment(del_flag);
CREATE INDEX IF NOT EXISTS idx_value_assessment_value_range ON xfx_value_assessment(value_range);

COMMENT ON TABLE xfx_value_assessment IS '人力成本模型-人均产值考核标准表单';
COMMENT ON COLUMN xfx_value_assessment.id IS '主键ID';
COMMENT ON COLUMN xfx_value_assessment.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_value_assessment.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_value_assessment.create_time IS '创建时间';
COMMENT ON COLUMN xfx_value_assessment.update_time IS '更新时间';
COMMENT ON COLUMN xfx_value_assessment.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_value_assessment.bonus_xia_sha IS '人力成本模型-四季风情下沙奖金';
COMMENT ON COLUMN xfx_value_assessment.bonus_ke_qiao IS '人力成本模型-四季风情柯桥奖金';
COMMENT ON COLUMN xfx_value_assessment.bonus_hu_die_li IS '人力成本模型-蝴蝶里奖金';
COMMENT ON COLUMN xfx_value_assessment.bonus_kao_jiang IS '人力成本模型-烤匠奖金';
COMMENT ON COLUMN xfx_value_assessment.bonus_cde IS '人力成本模型-新发现CDE商圈奖金';
COMMENT ON COLUMN xfx_value_assessment.bonus_ab IS '人力成本模型-新发现AB商圈奖金';
COMMENT ON COLUMN xfx_value_assessment.value_range IS '产值区间';

-- ========================================
-- 12. 日薪数据表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_daily_salary_data (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    daily_salary_total DECIMAL(10,2),
    date_range VARCHAR(100),
    store_name VARCHAR(200)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_daily_salary_data_object_id ON xfx_daily_salary_data(object_id);
CREATE INDEX IF NOT EXISTS idx_daily_salary_data_del_flag ON xfx_daily_salary_data(del_flag);
CREATE INDEX IF NOT EXISTS idx_daily_salary_data_store_name ON xfx_daily_salary_data(store_name);
CREATE INDEX IF NOT EXISTS idx_daily_salary_data_date_range ON xfx_daily_salary_data(date_range);
CREATE INDEX IF NOT EXISTS idx_daily_salary_data_sync_time ON xfx_daily_salary_data(sync_time);

COMMENT ON TABLE xfx_daily_salary_data IS '人力成本模型-日薪数据表单';
COMMENT ON COLUMN xfx_daily_salary_data.id IS '主键ID';
COMMENT ON COLUMN xfx_daily_salary_data.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_daily_salary_data.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_daily_salary_data.create_time IS '创建时间';
COMMENT ON COLUMN xfx_daily_salary_data.update_time IS '更新时间';
COMMENT ON COLUMN xfx_daily_salary_data.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_daily_salary_data.daily_salary_total IS '人力成本模型-日薪合计';
COMMENT ON COLUMN xfx_daily_salary_data.date_range IS '日期区间';
COMMENT ON COLUMN xfx_daily_salary_data.store_name IS '门店名称';

-- ========================================
-- 13. 其它费用占比表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_other_cost_ratio (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    other_cost_ratio DECIMAL(8,4),
    month VARCHAR(50),
    shop_name VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_other_cost_ratio_object_id ON xfx_other_cost_ratio(object_id);
CREATE INDEX IF NOT EXISTS idx_other_cost_ratio_del_flag ON xfx_other_cost_ratio(del_flag);
CREATE INDEX IF NOT EXISTS idx_other_cost_ratio_shop_name ON xfx_other_cost_ratio(shop_name);
CREATE INDEX IF NOT EXISTS idx_other_cost_ratio_month ON xfx_other_cost_ratio(month);
CREATE INDEX IF NOT EXISTS idx_other_cost_ratio_shop_month ON xfx_other_cost_ratio(shop_name, month);
CREATE INDEX IF NOT EXISTS idx_other_cost_ratio_sync_time ON xfx_other_cost_ratio(sync_time);

COMMENT ON TABLE xfx_other_cost_ratio IS '人力成本模型-其它费用占比表单';
COMMENT ON COLUMN xfx_other_cost_ratio.id IS '人力成本模型-主键ID';
COMMENT ON COLUMN xfx_other_cost_ratio.object_id IS '人力成本模型-宜搭对象ID';
COMMENT ON COLUMN xfx_other_cost_ratio.del_flag IS '人力成本模型-逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_other_cost_ratio.create_time IS '人力成本模型-创建时间';
COMMENT ON COLUMN xfx_other_cost_ratio.update_time IS '人力成本模型-更新时间';
COMMENT ON COLUMN xfx_other_cost_ratio.sync_time IS '人力成本模型-同步时间';
COMMENT ON COLUMN xfx_other_cost_ratio.other_cost_ratio IS '人力成本模型-其它费用占比';
COMMENT ON COLUMN xfx_other_cost_ratio.month IS '人力成本模型-月份';
COMMENT ON COLUMN xfx_other_cost_ratio.shop_name IS '人力成本模型-门店名称';

-- ========================================
-- 14. 门店abc折后营收表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_store_abc_discount_revenue (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    actual_hr_cost DECIMAL(12,2),
    abc_discount_revenue DECIMAL(12,2),
    month VARCHAR(50),
    store_name VARCHAR(255)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_store_abc_discount_revenue_object_id ON xfx_store_abc_discount_revenue(object_id);
CREATE INDEX IF NOT EXISTS idx_store_abc_discount_revenue_del_flag ON xfx_store_abc_discount_revenue(del_flag);
CREATE INDEX IF NOT EXISTS idx_store_abc_discount_revenue_store_name ON xfx_store_abc_discount_revenue(store_name);
CREATE INDEX IF NOT EXISTS idx_store_abc_discount_revenue_month ON xfx_store_abc_discount_revenue(month);
CREATE INDEX IF NOT EXISTS idx_store_abc_discount_revenue_store_month ON xfx_store_abc_discount_revenue(store_name, month);
CREATE INDEX IF NOT EXISTS idx_store_abc_discount_revenue_sync_time ON xfx_store_abc_discount_revenue(sync_time);

COMMENT ON TABLE xfx_store_abc_discount_revenue IS '人力成本模型-门店abc折后营收表单';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.id IS '人力成本模型-主键ID';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.object_id IS '人力成本模型-宜搭对象ID';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.del_flag IS '人力成本模型-逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.create_time IS '人力成本模型-创建时间';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.update_time IS '人力成本模型-更新时间';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.sync_time IS '人力成本模型-同步时间';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.actual_hr_cost IS '人力成本模型-实际人力资源费用';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.abc_discount_revenue IS '人力成本模型-abc折后营收';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.month IS '人力成本模型-月份';
COMMENT ON COLUMN xfx_store_abc_discount_revenue.store_name IS '人力成本模型-门店名称';

-- ========================================
-- 15. 排班指引建议人数表单
-- ========================================
CREATE TABLE IF NOT EXISTS xfx_scheduling_guide (
    id BIGSERIAL PRIMARY KEY,
    object_id VARCHAR(255),
    del_flag VARCHAR(1) DEFAULT '0',
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    shop_id VARCHAR(64),
    store_name VARCHAR(255),
    date TIMESTAMP,
    lunch_front_staff_count DECIMAL(10,2),
    lunch_kitchen_staff_count DECIMAL(10,2),
    dinner_front_staff_count DECIMAL(10,2),
    dinner_kitchen_staff_count DECIMAL(10,2),
    supper_front_staff_count DECIMAL(10,2),
    supper_kitchen_staff_count DECIMAL(10,2)
);

CREATE UNIQUE INDEX IF NOT EXISTS uk_scheduling_guide_object_id ON xfx_scheduling_guide(object_id);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_del_flag ON xfx_scheduling_guide(del_flag);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_shop_id ON xfx_scheduling_guide(shop_id);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_store_name ON xfx_scheduling_guide(store_name);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_date ON xfx_scheduling_guide(date);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_shop_date ON xfx_scheduling_guide(shop_id, date);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_store_date ON xfx_scheduling_guide(store_name, date);
CREATE INDEX IF NOT EXISTS idx_scheduling_guide_sync_time ON xfx_scheduling_guide(sync_time);

COMMENT ON TABLE xfx_scheduling_guide IS '人力成本模型-排班指引建议人数表单';
COMMENT ON COLUMN xfx_scheduling_guide.id IS '主键ID';
COMMENT ON COLUMN xfx_scheduling_guide.object_id IS '宜搭对象ID';
COMMENT ON COLUMN xfx_scheduling_guide.del_flag IS '逻辑删除标志，0表示未删除，1表示已删除';
COMMENT ON COLUMN xfx_scheduling_guide.create_time IS '创建时间';
COMMENT ON COLUMN xfx_scheduling_guide.update_time IS '更新时间';
COMMENT ON COLUMN xfx_scheduling_guide.sync_time IS '同步时间';
COMMENT ON COLUMN xfx_scheduling_guide.shop_id IS '门店ID';
COMMENT ON COLUMN xfx_scheduling_guide.store_name IS '门店名称';
COMMENT ON COLUMN xfx_scheduling_guide.date IS '日期';
COMMENT ON COLUMN xfx_scheduling_guide.lunch_front_staff_count IS '排班指引建议人数_午市前厅';
COMMENT ON COLUMN xfx_scheduling_guide.lunch_kitchen_staff_count IS '排班指引建议人数_午市厨房';
COMMENT ON COLUMN xfx_scheduling_guide.dinner_front_staff_count IS '排班指引建议人数_晚市前厅';
COMMENT ON COLUMN xfx_scheduling_guide.dinner_kitchen_staff_count IS '排班指引建议人数_晚市厨房';
COMMENT ON COLUMN xfx_scheduling_guide.supper_front_staff_count IS '排班指引建议人数_夜宵前厅';
COMMENT ON COLUMN xfx_scheduling_guide.supper_kitchen_staff_count IS '排班指引建议人数_夜宵厨房';

-- ========================================
-- 脚本执行完成
-- ========================================
