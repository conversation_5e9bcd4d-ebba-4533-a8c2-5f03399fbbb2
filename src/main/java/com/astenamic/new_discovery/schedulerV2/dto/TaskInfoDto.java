package com.astenamic.new_discovery.schedulerV2.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfoDto {
    
    /** 任务ID */
    private String taskId;
    
    /** 任务名称 */
    private String name;
    
    /** 任务描述 */
    private String description;
    
    /** Cron表达式 */
    private String cronExpression;
    
    /** 任务类型 */
    private String taskType;
    
    /** 优先级 */
    private Integer priority;
    
    /** 最大重试次数 */
    private Integer maxRetries;
    
    /** 重试间隔(毫秒) */
    private Long retryInterval;
    
    /** 是否开启失败报警 */
    private Boolean alertOnFailure;
    
    /** 执行器类型 */
    private String executorType;
}
