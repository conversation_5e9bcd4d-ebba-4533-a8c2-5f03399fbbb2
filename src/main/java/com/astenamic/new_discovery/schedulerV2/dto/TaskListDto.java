package com.astenamic.new_discovery.schedulerV2.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务列表DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskListDto {
    
    /** 任务总数 */
    private Integer totalTasks;
    
    /** 启用的任务数 */
    private Integer enabledTasks;
    
    /** 禁用的任务数 */
    private Integer disabledTasks;
    
    /** 任务列表 */
    private List<TaskInfoDto> tasks;
}
