package com.astenamic.new_discovery.schedulerV2.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 调度器状态DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SchedulerStatusDto {

    /** 任务总数 */
    private Integer totalTasks;

    /** 启用的任务数 */
    private Integer enabledTasks;

    /** 禁用的任务数 */
    private Integer disabledTasks;

    /** 系统状态 */
    private String status;

    /** 状态描述 */
    private String statusDescription;

    /** 当前时间戳 */
    private Long timestamp;
}
