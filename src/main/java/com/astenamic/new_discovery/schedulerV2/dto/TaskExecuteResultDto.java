package com.astenamic.new_discovery.schedulerV2.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务执行结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskExecuteResultDto {
    
    /** 任务ID */
    private String taskId;
    
    /** 任务名称 */
    private String taskName;
    
    /** 执行是否成功 */
    private Boolean success;
    
    /** 执行消息 */
    private String message;
    
    /** 执行开始时间 */
    private Long startTime;
    
    /** 执行结束时间 */
    private Long endTime;
    
    /** 执行耗时(毫秒) */
    private Long duration;
}
