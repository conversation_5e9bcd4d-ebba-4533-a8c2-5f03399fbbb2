package com.astenamic.new_discovery.schedulerV2.alert.impl;

import com.astenamic.new_discovery.dingtalk.bot.DingTalkRobotService;
import com.astenamic.new_discovery.schedulerV2.alert.TaskAlertService;
import com.astenamic.new_discovery.schedulerV2.config.SchedulerProperties;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于钉钉机器人的任务报警服务
 */

@AllArgsConstructor
public class DingTalkTaskAlertService implements TaskAlertService {
    private static final Logger logger = LoggerFactory.getLogger(DingTalkTaskAlertService.class);
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 记录任务上次报警时间，避免频繁报警
    private final Map<String, Long> lastAlertTimeMap = new ConcurrentHashMap<>();

    private final DingTalkRobotService dingTalkRobotService;

    private final SchedulerProperties properties;

    @Override
    public void sendTaskFailureAlert(TaskDefinition taskDefinition, Exception exception, int retryCount) {
        String taskId = taskDefinition.getTaskId();
        long now = System.currentTimeMillis();
        long interval = properties.getAlert().getFailureAlertInterval() * 1000; // 转换为毫秒

        // 检查是否需要报警（防止频繁报警）
        Long lastAlertTime = lastAlertTimeMap.get(taskId);
        if (lastAlertTime != null && (now - lastAlertTime) < interval) {
            logger.info("任务 {} 报警间隔内，暂不重复发送报警", taskDefinition.getName());
            return;
        }

        // 记录本次报警时间
        lastAlertTimeMap.put(taskId, now);

        String time = LocalDateTime.now().format(DATETIME_FORMATTER);

        StringBuilder message = new StringBuilder();
        message.append("### 【定时任务执行失败报警】\n\n");
        message.append("- **任务名称**：").append(taskDefinition.getName()).append("\n");
        message.append("- **任务描述**：").append(taskDefinition.getDescription()).append("\n");
        message.append("- **cron表达式**：").append(taskDefinition.getCronExpression()).append("\n");
        message.append("- **失败时间**：").append(time).append("\n");
        message.append("- **重试次数**：").append(retryCount).append("/").append(taskDefinition.getMaxRetries()).append("\n");
        message.append("- **异常信息**：").append(exception.getMessage()).append("\n");

        try {
            logger.info("发送任务失败报警: {}", taskDefinition.getName());
            dingTalkRobotService.sendMarkdownMessage(
                    properties.getAlert().getMonitorKeyword(),
                    "定时任务执行失败报警",
                    message.toString(),
                    null,
                    properties.getAlert().isAtAll()
            );
        } catch (Exception e) {
            logger.error("发送钉钉报警失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void sendSystemAlert(String message) {
        String time = LocalDateTime.now().format(DATETIME_FORMATTER);

        StringBuilder alertMessage = new StringBuilder();
        alertMessage.append("### 【").append(properties.getAlert().getSystemName()).append("】\n\n");
        alertMessage.append("- **报警时间**：").append(time).append("\n");
        alertMessage.append("- **报警内容**：").append(message).append("\n");

        try {
            logger.info("发送系统报警: {}", message);
            dingTalkRobotService.sendTextMessage(
                    properties.getAlert().getMonitorKeyword(),
                    alertMessage.toString(),
                    null,
                    properties.getAlert().isAtAll()
            );
        } catch (Exception e) {
            logger.error("发送钉钉报警失败: {}", e.getMessage(), e);
        }
    }
}