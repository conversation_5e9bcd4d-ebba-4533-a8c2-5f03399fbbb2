package com.astenamic.new_discovery.schedulerV2.alert;

import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;

/**
 * 定时任务报警服务接口
 */
public interface TaskAlertService {

    /**
     * 发送任务失败报警
     * @param taskDefinition 任务定义
     * @param exception 异常信息
     * @param retryCount 已重试次数
     */
    void sendTaskFailureAlert(TaskDefinition taskDefinition, Exception exception, int retryCount);
    
    /**
     * 发送系统异常报警
     * @param message 报警消息
     */
    void sendSystemAlert(String message);
} 