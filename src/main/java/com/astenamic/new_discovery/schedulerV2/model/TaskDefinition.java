package com.astenamic.new_discovery.schedulerV2.model;


import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskDefinition {
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务描述
     */
    private String description;
    /**
     * cron表达式
     */
    private String cronExpression;
    /**
     * 任务类型
     */
    private SchedulerTaskType schedulerTaskType;
    /**
     * 任务优先级(1-10)
     */
    private int priority;
    /**
     * 最大重试次数
     */
    private int maxRetries;
    /**
     * 重试间隔(毫秒)
     */
    private long retryInterval;
    /**
     * 失败是否报警
     */
    private boolean alertOnFailure;

    /**
     * 任务执行器类型
     */
    private String executorType;

    public enum SchedulerTaskType {
        IO_INTENSIVE,
        CPU_INTENSIVE
    }
}
