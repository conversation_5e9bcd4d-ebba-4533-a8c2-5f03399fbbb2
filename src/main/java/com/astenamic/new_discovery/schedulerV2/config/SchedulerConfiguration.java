package com.astenamic.new_discovery.schedulerV2.config;

import com.astenamic.new_discovery.dingtalk.bot.DingTalkRobotService;
import com.astenamic.new_discovery.schedulerV2.alert.impl.DingTalkTaskAlertService;
import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.executor.impl.QueueTaskExecutor;
import com.astenamic.new_discovery.schedulerV2.executor.impl.ThreadTaskExecutor;
import com.astenamic.new_discovery.schedulerV2.manager.TaskManager;
import com.astenamic.new_discovery.schedulerV2.manager.impl.DefaultTaskManager;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SchedulerConfiguration {

    @Resource
    private DingTalkRobotService dingTalkRobotService;


    @Bean
    public SchedulerProperties schedulerProperties() {
        return new SchedulerProperties();
    }

    @Bean
    public DingTalkTaskAlertService dingTalkTaskAlertService(SchedulerProperties schedulerProperties) {
        return new DingTalkTaskAlertService(dingTalkRobotService, schedulerProperties);
    }

    @Bean
    public TaskExecutor queueTaskExecutor(DingTalkTaskAlertService dingTalkTaskAlertService) {
        return new QueueTaskExecutor(dingTalkTaskAlertService);
    }

    @Bean
    public TaskExecutor threadTaskExecutor(DingTalkTaskAlertService dingTalkTaskAlertService) {
        return new ThreadTaskExecutor(dingTalkTaskAlertService);
    }

    @Bean
    public TaskManager defaultTaskManager(List<TaskExecutor> executors) {
        return new DefaultTaskManager(executors);
    }

}
