package com.astenamic.new_discovery.schedulerV2.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务系统配置属性
 */
@Data
@ConfigurationProperties(prefix = "scheduler")
public class SchedulerProperties {

    /**
     * 是否启用定时任务系统
     */
    private boolean enabled = false;

    /**
     * 报警配置
     */
    private Alert alert = new Alert();


    /**
     * 报警相关配置
     */
    @Data
    public static class Alert {
        /**
         * 钉钉机器人自定义关键词
         */
        private String monitorKeyword = "monitor";
        /**
         * 是否@所有人
         */
        private boolean atAll = true;
        /**
         * 系统名称
         */
        private String systemName = "定时任务系统";
        /**
         * 任务失败报警间隔（秒）
         * 同一任务在指定时间内只报警一次
         */
        private long failureAlertInterval = 300;
    }
}