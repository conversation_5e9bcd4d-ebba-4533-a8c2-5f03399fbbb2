package com.astenamic.new_discovery.schedulerV2.manager;

import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务管理器接口
 * 负责管理、存储和获取任务信息
 */
public interface TaskManager {

    /**
     * 注册任务
     * @param taskDefinition 任务定义
     * @param task 要执行的任务
     */
    void registerTask(TaskDefinition taskDefinition, Runnable task);

    /**
     * 获取指定时间需要执行的任务
     * @param date 指定时间
     * @return 任务定义和执行逻辑的映射
     */
    Map<TaskDefinition, Runnable> getTasksDueAtTime(LocalDateTime date);

    /**
     * 获取执行器
     * @param taskDefinition 任务定义
     * @return 任务执行器
     */
    TaskExecutor getExecutor(TaskDefinition taskDefinition);

    /**
     * 注销任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean unregisterTask(String taskId);

    /**
     * 手动触发任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean triggerTask(String taskId);

    /**
     * 获取任务定义
     * @param taskId 任务ID
     * @return 任务定义
     */
    TaskDefinition getTaskDefinition(String taskId);

    /**
     * 获取所有任务定义
     * @return 任务定义列表
     */
    List<TaskDefinition> getAllTaskDefinitions();

    /**
     * 检查任务是否存在
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean taskExists(String taskId);

}

