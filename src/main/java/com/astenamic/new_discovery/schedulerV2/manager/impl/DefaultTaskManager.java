package com.astenamic.new_discovery.schedulerV2.manager.impl;

import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.manager.TaskManager;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务管理器实现类
 * 负责存储任务定义、执行逻辑和执行器映射关系
 */
public class DefaultTaskManager implements TaskManager {

    private static final Logger logger = LoggerFactory.getLogger(DefaultTaskManager.class);

    private static final String DEFAULT_EXECUTOR_TYPE = "queue";

    private final Map<String, TaskExecutor> executors = new ConcurrentHashMap<>();

    private final Map<String, Pair<TaskDefinition, Runnable>> tasks = new ConcurrentHashMap<>();


    public DefaultTaskManager(List<TaskExecutor> executors) {
        if (executors != null) {
            for (TaskExecutor executor : executors) {
                this.executors.put(executor.getType(), executor);
            }
        }
    }


    /**
     * 获取指定时间应该执行的任务
     */
    @Override
    public Map<TaskDefinition, Runnable> getTasksDueAtTime(LocalDateTime dateTime) {
        logger.debug("获取时间 {} 需要执行的任务", dateTime);
        Map<TaskDefinition, Runnable> result = new HashMap<>();
        // 检查所有任务
        for (Map.Entry<String, Pair<TaskDefinition, Runnable>> entry : tasks.entrySet()) {
            String taskId = entry.getKey();
            TaskDefinition taskDefinition = entry.getValue().getFirst();
            try {
                if (shouldExecuteAt(taskDefinition, dateTime)) {
                    Runnable task = entry.getValue().getSecond();
                    result.put(taskDefinition, task);
                }
            } catch (Exception e) {
                logger.error("检查任务 [{}] 执行时间时出错: {}", taskDefinition.getName(), e.getMessage(), e);
            }
        }
        return result;
    }

    /**
     * 检查任务是否应该在指定时间执行
     */
    private boolean shouldExecuteAt(TaskDefinition taskDefinition, LocalDateTime dateTime) {
        try {
            CronExpression cronExpression = CronExpression.parse(taskDefinition.getCronExpression());
            LocalDateTime triggerTime = dateTime.withSecond(0).withNano(0);
            LocalDateTime nextExecution = cronExpression.next(triggerTime.minusSeconds(1));
            return triggerTime.equals(nextExecution);
        } catch (Exception e) {
            logger.error("解析Cron表达式失败: {}, 表达式: {}", e.getMessage(), taskDefinition.getCronExpression());
            return false;
        }
    }


    /**
     * 注册任务
     */
    @Override
    public void registerTask(TaskDefinition taskDefinition, Runnable task) {
        Assert.notNull(taskDefinition, "任务定义不能为空");
        Assert.notNull(task, "任务执行逻辑不能为空");
        if (StringUtils.isBlank(taskDefinition.getCronExpression())) {
            logger.error("无法注册任务 {}: 缺少cron表达式", taskDefinition.getName());
            return;
        }
        logger.info("注册任务: {}, cron: {}", taskDefinition.getName(), taskDefinition.getCronExpression());
        tasks.put(taskDefinition.getTaskId(), Pair.of(taskDefinition, task));
    }

    /**
     * 获取执行器
     */
    @Override
    public TaskExecutor getExecutor(TaskDefinition taskDefinition) {
        return executors.getOrDefault(taskDefinition.getExecutorType(), executors.get(DEFAULT_EXECUTOR_TYPE));
    }

    /**
     * 注销任务
     */
    @Override
    public boolean unregisterTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            logger.warn("任务ID不能为空");
            return false;
        }

        Pair<TaskDefinition, Runnable> removed = tasks.remove(taskId);
        if (removed != null) {
            logger.info("成功注销任务: {} - {}", taskId, removed.getFirst().getName());
            return true;
        } else {
            logger.warn("尝试注销不存在的任务: {}", taskId);
            return false;
        }
    }

    /**
     * 手动触发任务
     */
    @Override
    public boolean triggerTask(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            logger.warn("任务ID不能为空");
            return false;
        }

        Pair<TaskDefinition, Runnable> taskPair = tasks.get(taskId);
        if (taskPair == null) {
            logger.warn("尝试触发不存在的任务: {}", taskId);
            return false;
        }

        try {
            TaskDefinition taskDefinition = taskPair.getFirst();
            Runnable task = taskPair.getSecond();
            TaskExecutor executor = getExecutor(taskDefinition);

            logger.info("手动触发任务: {} - {}", taskId, taskDefinition.getName());
            executor.execute(taskDefinition, task);
            return true;

        } catch (Exception e) {
            logger.error("手动触发任务失败: {} - {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取任务定义
     */
    @Override
    public TaskDefinition getTaskDefinition(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return null;
        }

        Pair<TaskDefinition, Runnable> taskPair = tasks.get(taskId);
        return taskPair != null ? taskPair.getFirst() : null;
    }

    /**
     * 获取所有任务定义
     */
    @Override
    public List<TaskDefinition> getAllTaskDefinitions() {
        return tasks.values().stream()
                .map(Pair::getFirst)
                .toList();
    }

    /**
     * 检查任务是否存在
     */
    @Override
    public boolean taskExists(String taskId) {
        return StringUtils.isNotBlank(taskId) && tasks.containsKey(taskId);
    }
}