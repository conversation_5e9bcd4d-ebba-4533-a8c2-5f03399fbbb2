package com.astenamic.new_discovery.schedulerV2.service;

import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;

import java.util.List;

/**
 * 定时任务调度服务接口
 */
public interface SchedulerService {

    /**
     * 注册定时任务
     * @param taskDefinition 任务定义
     * @param task 任务执行逻辑
     * @return 任务ID
     */
    String registerTask(TaskDefinition taskDefinition, Runnable task);

    /**
     * 注销定时任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean unregisterTask(String taskId);

    /**
     * 手动触发任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean triggerTask(String taskId);

    /**
     * 获取所有已注册的任务
     * @return 任务定义列表
     */
    List<TaskDefinition> getAllTasks();

    /**
     * 根据任务ID获取任务定义
     * @param taskId 任务ID
     * @return 任务定义
     */
    TaskDefinition getTaskById(String taskId);

    /**
     * 检查任务是否存在
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean taskExists(String taskId);

    /**
     * 获取任务统计信息
     * @return 任务统计
     */
    TaskStatistics getTaskStatistics();

    /**
     * 任务统计信息
     */
    class TaskStatistics {
        private final int totalTasks;
        private final int enabledTasks;
        private final int disabledTasks;

        public TaskStatistics(int totalTasks, int enabledTasks, int disabledTasks) {
            this.totalTasks = totalTasks;
            this.enabledTasks = enabledTasks;
            this.disabledTasks = disabledTasks;
        }

        public int getTotalTasks() { return totalTasks; }
        public int getEnabledTasks() { return enabledTasks; }
        public int getDisabledTasks() { return disabledTasks; }
    }
}
