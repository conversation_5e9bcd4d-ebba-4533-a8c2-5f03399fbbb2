package com.astenamic.new_discovery.schedulerV2.service.impl;

import com.astenamic.new_discovery.schedulerV2.manager.TaskManager;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import com.astenamic.new_discovery.schedulerV2.service.SchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定时任务调度服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchedulerServiceImpl implements SchedulerService {

    private final TaskManager taskManager;

    @Override
    public String registerTask(TaskDefinition taskDefinition, Runnable task) {
        if (taskDefinition == null || task == null) {
            throw new IllegalArgumentException("任务定义和任务执行逻辑不能为空");
        }

        String taskId = taskDefinition.getTaskId();
        if (taskExists(taskId)) {
            log.warn("任务已存在，将覆盖原任务: {}", taskId);
        }

        try {
            // 委托给 TaskManager 进行持久化
            taskManager.registerTask(taskDefinition, task);

            log.info("成功注册定时任务: {} - {}", taskId, taskDefinition.getName());
            return taskId;

        } catch (Exception e) {
            log.error("注册定时任务失败: {} - {}", taskId, taskDefinition.getName(), e);
            throw new RuntimeException("注册定时任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean unregisterTask(String taskId) {
        if (!taskExists(taskId)) {
            log.warn("尝试注销不存在的任务: {}", taskId);
            return false;
        }

        try {
            // 委托给 TaskManager 进行删除
            TaskDefinition taskDefinition = taskManager.getTaskDefinition(taskId);
            boolean success = taskManager.unregisterTask(taskId);

            if (success) {
                log.info("成功注销定时任务: {} - {}", taskId,
                        taskDefinition != null ? taskDefinition.getName() : "未知");
            }
            return success;

        } catch (Exception e) {
            log.error("注销定时任务失败: {}", taskId, e);
            return false;
        }
    }

    @Override
    public boolean triggerTask(String taskId) {
        if (!taskExists(taskId)) {
            log.warn("尝试触发不存在的任务: {}", taskId);
            return false;
        }

        try {
            // 委托给 TaskManager 进行任务触发
            boolean success = taskManager.triggerTask(taskId);

            if (success) {
                TaskDefinition taskDefinition = taskManager.getTaskDefinition(taskId);
                log.info("成功触发定时任务: {} - {}", taskId,
                        taskDefinition != null ? taskDefinition.getName() : "未知");
            }
            return success;

        } catch (Exception e) {
            log.error("触发定时任务失败: {}", taskId, e);
            return false;
        }
    }

    @Override
    public List<TaskDefinition> getAllTasks() {
        return taskManager.getAllTaskDefinitions();
    }

    @Override
    public TaskDefinition getTaskById(String taskId) {
        return taskManager.getTaskDefinition(taskId);
    }

    @Override
    public boolean taskExists(String taskId) {
        return taskManager.taskExists(taskId);
    }

    @Override
    public TaskStatistics getTaskStatistics() {
        List<TaskDefinition> allTasks = taskManager.getAllTaskDefinitions();
        int totalTasks = allTasks.size();
        // 这里简化处理，假设所有任务都是启用的
        // 实际可以根据TaskDefinition中的状态字段来统计
        int enabledTasks = totalTasks;
        int disabledTasks = 0;

        return new TaskStatistics(totalTasks, enabledTasks, disabledTasks);
    }
}
