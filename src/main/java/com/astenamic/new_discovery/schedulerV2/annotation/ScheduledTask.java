package com.astenamic.new_discovery.schedulerV2.annotation;

import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;

import java.lang.annotation.*;

/**
 * 定时任务注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(ScheduledTasks.class)
public @interface ScheduledTask {
    /**
     * 任务名称
     */
    String name();

    /**
     * 任务描述
     */
    String description() default "";

    /**
     * cron表达式
     */
    String cron();

    /**
     * 任务类型
     */
    TaskDefinition.SchedulerTaskType taskType() default TaskDefinition.SchedulerTaskType.IO_INTENSIVE;

    /**
     * 任务优先级(1-10)
     */
    int priority() default 5;

    /**
     * 最大重试次数
     */
    int maxRetries() default 6;

    /**
     * 重试间隔(毫秒)
     */
    long retryInterval() default 1000;

    /**
     * 失败是否报警
     */
    boolean alertOnFailure() default true;

    /**
     * 任务执行器类型
     */
    String executorType() default "queue";
}