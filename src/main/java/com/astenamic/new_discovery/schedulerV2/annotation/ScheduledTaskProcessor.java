package com.astenamic.new_discovery.schedulerV2.annotation;

import com.astenamic.new_discovery.schedulerV2.manager.TaskManager;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.UUID;

/**
 * 定时任务注解处理器
 */
@Component
public class ScheduledTaskProcessor implements ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {
    private static final Logger logger = LoggerFactory.getLogger(ScheduledTaskProcessor.class);

    private ApplicationContext applicationContext;

    // 防止多次执行的标记
    private boolean initialized = false;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (initialized || event.getApplicationContext().getParent() != null) {
            return;
        }
        initialized = true;
        scanAndRegisterTasks();
    }

    /**
     * 扫描并注册带有@ScheduledTask注解的方法
     */
    private void scanAndRegisterTasks() {
        logger.info("开始扫描系统中的@ScheduledTask注解...");

        try {
            TaskManager taskManager = applicationContext.getBean(TaskManager.class);

            int taskCount = 0;

            String[] beanNames = applicationContext.getBeanDefinitionNames();
            for (String beanName : beanNames) {
                Object bean = applicationContext.getBean(beanName);
                Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);
                // 扫描Bean中带有@ScheduledTask注解的方法
                Method[] methods = targetClass.getDeclaredMethods();
                for (Method method : methods) {
                    ScheduledTask singleAnnotation = method.getAnnotation(ScheduledTask.class);
                    ScheduledTasks containerAnnotation = method.getAnnotation(ScheduledTasks.class);

                    if (singleAnnotation != null) {
                        registerScheduledTask(bean, method, singleAnnotation, taskManager);
                        taskCount++;
                    }

                    if (containerAnnotation != null) {
                        for (ScheduledTask annotation : containerAnnotation.value()) {
                            registerScheduledTask(bean, method, annotation, taskManager);
                            taskCount++;
                        }
                    }
                }
            }

            logger.info("完成定时任务扫描和注册，共注册{}个任务", taskCount);

        } catch (Exception e) {
            logger.error("扫描和注册定时任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 注册单个定时任务
     */
    private void registerScheduledTask(Object bean, Method method, ScheduledTask annotation, TaskManager taskManager) {
        // 创建任务定义
        TaskDefinition taskDefinition = TaskDefinition.builder()
                .taskId(UUID.randomUUID().toString())
                .name(annotation.name())
                .description(annotation.description())
                .cronExpression(annotation.cron())
                .schedulerTaskType(annotation.taskType())
                .priority(annotation.priority())
                .maxRetries(annotation.maxRetries())
                .retryInterval(annotation.retryInterval())
                .alertOnFailure(annotation.alertOnFailure())
                .executorType(annotation.executorType())
                .build();

        logger.info("注册定时任务: {}, 方法: {}.{}, cron: {}",
                annotation.name(), bean.getClass().getSimpleName(),
                method.getName(), annotation.cron());

        Runnable task = () -> {
            ReflectionUtils.makeAccessible(method);
            ReflectionUtils.invokeMethod(method, bean);
        };
        // 注册任务
        taskManager.registerTask(taskDefinition, task);
    }
}