package com.astenamic.new_discovery.schedulerV2.executor.impl;

import com.astenamic.new_discovery.schedulerV2.alert.TaskAlertService;
import com.astenamic.new_discovery.schedulerV2.executor.TaskExecutor;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;

import java.util.concurrent.*;

@AllArgsConstructor
public class QueueTaskExecutor implements TaskExecutor {

    private static final Logger logger = LoggerFactory.getLogger(QueueTaskExecutor.class);

    private static final String EXECUTOR_TYPE = "queue";

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    private final TaskAlertService alertService;


    @PreDestroy
    public void shutdown() {
        if (!scheduler.isShutdown()) {
            logger.info("关闭队列任务执行器");
            scheduler.shutdown();
            try {
                // 等待已提交任务完成
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    logger.warn("队列任务执行器未能在5秒内完成所有任务，将强制关闭");
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("等待队列任务执行器关闭时被中断", e);
                scheduler.shutdownNow();
            }
        }
    }

    @Override
    public void execute(TaskDefinition taskDefinition, Runnable task) {
        logger.info("使用队列执行器执行任务: {}", taskDefinition.getName());
        try {
            scheduler.execute(() -> {
                logger.info("队列任务开始执行: {}", taskDefinition.getName());
                int retryCount = 0;
                boolean success = false;
                Exception lastException = null;
                while (retryCount < taskDefinition.getMaxRetries() && !success) {
                    try {
                        task.run();
                        success = true;
                        logger.info("任务 {} 执行成功", taskDefinition.getName());
                    } catch (Exception e) {
                        lastException = e;
                        retryCount++;
                        logger.error("任务 {} 执行失败 (尝试 {}/{}): {}",
                                taskDefinition.getName(), retryCount,
                                taskDefinition.getMaxRetries(), e.getMessage(), e);
                        if (retryCount < taskDefinition.getMaxRetries()) {
                            try {
                                Thread.sleep(taskDefinition.getRetryInterval());
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                logger.error("任务 {} 执行被中断", taskDefinition.getName());
                                if (taskDefinition.isAlertOnFailure() && alertService != null) {
                                    alertService.sendTaskFailureAlert(taskDefinition, ie, retryCount);
                                }
                                break;
                            }
                        } else if (taskDefinition.isAlertOnFailure() && alertService != null) {
                            logger.error("任务 {} 执行失败，已达到最大重试次数，发送报警", taskDefinition.getName());
                            alertService.sendTaskFailureAlert(taskDefinition, lastException, retryCount);
                        }
                    }
                }
                logger.info("队列任务执行完成: {}", taskDefinition.getName());
            });
        } catch (Exception e) {
            logger.error("提交任务到队列执行器失败: {}", e.getMessage(), e);
            if (taskDefinition.isAlertOnFailure() && alertService != null) {
                alertService.sendSystemAlert("提交任务" + taskDefinition.getName() + "到队列执行器失败: " + e.getMessage());
            }
        }
    }

    @Override
    public String getType() {
        return EXECUTOR_TYPE;
    }
}