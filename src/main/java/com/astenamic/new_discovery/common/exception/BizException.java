package com.astenamic.new_discovery.common.exception;

import com.astenamic.new_discovery.common.modal.Result;
import lombok.Getter;

@Getter
public class BizException extends RuntimeException {

    private final Result.ErrorCode error;

    public BizException(Result.ErrorCode error) {
        super(error.message);
        this.error = error;
    }

    public BizException(String message) {
        super(message);
        this.error = null;
    }

    public BizException(Result.ErrorCode error, String message) {
        super(error.message + ":" + message);
        this.error = error;
    }


}