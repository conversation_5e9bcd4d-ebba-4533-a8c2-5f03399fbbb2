package com.astenamic.new_discovery.common.modal;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.Instant;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record Result<T>
        (int code,
         String message,
         T data,
         long timestamp
        ) {

    public static <T> Result<T> ok(T data) {
        return new Result<>(200, "success", data, Instant.now().toEpochMilli());
    }

    public static <T> Result<T> ok() {
        return ok(null);
    }

    public static <T> Result<T> fail(ErrorCode ec) {
        return new Result<>(ec.code, ec.message, null, Instant.now().toEpochMilli());
    }

    public static <T> Result<T> fail(ErrorCode ec, String message) {
        return new Result<>(ec.code, ec.message + ":" + message, null, Instant.now().toEpochMilli());
    }

    public static <T> Result<T> fail(String message) {
        return new Result<>(999, message, null, Instant.now().toEpochMilli());
    }

    //1xxx 鉴权 | 2xxx 请求 | 3xxx 业务态 | 4xxx 持久层 | 5xxx 系统, 999 其他
    public enum ErrorCode {

        INVALID_TOKEN(1401, "Invalid token"),

        SERVER_ERROR(5500, "服务器异常"),
        YIDA_SESSION_ERROR(5501, "YiDa Session异常"),
        TASK_ERROR(5502, "任务执行异常"),

        REQUEST_ERROR(2601, "请求失败"),
        VALIDATION_FAILED(2600, "参数校验失败"),
        FLOW_NOT_FOUND(2604, "流程实例不存在"),
        TASK_NOT_FOUND(2605, "任务不存在"),
        FORM_NOT_FOUND(2606, "表单实例不存在"),
        ;

        public final int code;
        public final String message;

        ErrorCode(int code, String msg) {
            this.code = code;
            this.message = msg;
        }

    }
}