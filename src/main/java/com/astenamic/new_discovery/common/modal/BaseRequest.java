package com.astenamic.new_discovery.common.modal;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 所有接口请求 DTO 的父类：
 *   1. 统一携带时间戳（毫秒）
 *   2. 提供 validated() 供特殊逻辑二次校验
 */
@Getter
@Setter
@ToString
public abstract class BaseRequest {

    /** 客户端时间戳（毫秒） */
    @NotNull(message = "timestamp 不能为空")
    private Long timestamp;

    /**
     * 基础校验：客户端时间戳不得偏离服务器 ±10 分钟
     * 子类可以 @Override 追加业务字段验证
     */
    public void validated() {
        long now = System.currentTimeMillis();
        if (timestamp == null || Math.abs(now - timestamp) > 10 * 60_000) {
            throw new IllegalArgumentException("非法时间戳");
        }
    }

}

