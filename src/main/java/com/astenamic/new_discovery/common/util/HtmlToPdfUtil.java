package com.astenamic.new_discovery.common.util;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.utils.PdfMerger;
import com.itextpdf.layout.font.FontProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * HTML转PDF工具类
 */
@Slf4j
public class HtmlToPdfUtil {
    // 静态字体提供者，只加载一次
    private static final FontProvider FONT_PROVIDER;

    static {
        // 在类加载时初始化字体提供者
        FONT_PROVIDER = new DefaultFontProvider(true, true, true);

        // 加载微软雅黑字体（TTF格式）
        try {
            var inputStream = HtmlToPdfUtil.class.getClassLoader().getResourceAsStream("fonts/msyh.ttf");
            if (inputStream != null) {
                byte[] fontBytes = inputStream.readAllBytes();
                FontProgram fontProgram = FontProgramFactory.createFont(fontBytes);
                FONT_PROVIDER.addFont(fontProgram);
                log.info("成功加载微软雅黑字体: fonts/msyh.ttf");
                inputStream.close();
            } else {
                log.warn("微软雅黑字体文件不存在: fonts/msyh.ttf，请将微软雅黑TTF格式字体文件放到 src/main/resources/fonts/ 目录下");
            }
        } catch (Exception e) {
            log.error("微软雅黑字体加载失败: {}，请确保使用TTF格式的字体文件", e.getMessage());
        }
    }

    /**
     * 将HTML字符串转换为PDF字节数组
     *
     * @param htmlContent HTML内容
     * @return PDF字节数组
     * @throws IOException 转换异常
     */
    public static byte[] convertHtmlToPdf(String htmlContent) throws IOException {
        return convertHtmlToPdf(htmlContent, PageSize.A4);
    }

    /**
     * 将HTML字符串转换为PDF字节数组（指定页面大小）
     *
     * @param htmlContent HTML内容
     * @param pageSize 页面大小
     * @return PDF字节数组
     * @throws IOException 转换异常
     */
    public static byte[] convertHtmlToPdf(String htmlContent, PageSize pageSize) throws IOException {
        log.info("开始HTML转PDF转换，HTML长度: {}", htmlContent != null ? htmlContent.length() : 0);

        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 创建PDF写入器
            PdfWriter writer = new PdfWriter(outputStream);

            // 创建PDF文档
            PdfDocument pdfDocument = new PdfDocument(writer);
            pdfDocument.setDefaultPageSize(pageSize);

            // 设置转换属性
            ConverterProperties converterProperties = new ConverterProperties();

            // 使用静态字体提供者
            converterProperties.setFontProvider(FONT_PROVIDER);

            // 执行HTML到PDF的转换
            HtmlConverter.convertToPdf(htmlContent, pdfDocument, converterProperties);

            // 关闭PDF文档
            pdfDocument.close();

            byte[] pdfBytes = outputStream.toByteArray();
            log.info("HTML转PDF转换完成，PDF大小: {} bytes", pdfBytes.length);

            return pdfBytes;

        } catch (Exception e) {
            log.error("HTML转PDF转换失败: {}", e.getMessage(), e);
            throw new IOException("HTML转PDF转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将HTML字符串转换为PDF字节数组（带自定义CSS）
     *
     * @param htmlContent HTML内容
     * @param customCss 自定义CSS样式
     * @return PDF字节数组
     * @throws IOException 转换异常
     */
    public static byte[] convertHtmlToPdfWithCss(String htmlContent, String customCss) throws IOException {
        return convertHtmlToPdfWithCss(htmlContent, customCss, PageSize.A4);
    }

    /**
     * 将HTML字符串转换为PDF字节数组（带自定义CSS和页面大小）
     *
     * @param htmlContent HTML内容
     * @param customCss 自定义CSS样式
     * @param pageSize 页面大小
     * @return PDF字节数组
     * @throws IOException 转换异常
     */
    public static byte[] convertHtmlToPdfWithCss(String htmlContent, String customCss, PageSize pageSize) throws IOException {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }

        // 将CSS样式嵌入到HTML中
        String htmlWithCss = wrapHtmlWithCss(htmlContent, customCss);

        return convertHtmlToPdf(htmlWithCss, pageSize);
    }



    /**
     * 将CSS样式包装到HTML中
     *
     * @param htmlContent HTML内容
     * @param css CSS样式
     * @return 包装后的HTML
     */
    private static String wrapHtmlWithCss(String htmlContent, String css) {
        if (css == null || css.trim().isEmpty()) {
            return htmlContent;
        }

        // 如果HTML已经是完整的文档，直接在head中添加style
        if (htmlContent.toLowerCase().contains("<html")) {
            return htmlContent.replaceFirst("(?i)<head[^>]*>", 
                "<head><style>" + css + "</style>");
        }

        // 如果HTML是片段，包装成完整文档
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                %s
                </style>
            </head>
            <body>
                %s
            </body>
            </html>
            """.formatted(css, htmlContent);
    }

    /**
     * 合并多个PDF文件
     *
     * @param pdfBytesList PDF字节数组列表
     * @return 合并后的PDF字节数组
     * @throws IOException 合并异常
     */
    public static byte[] mergePdfs(List<byte[]> pdfBytesList) throws IOException {
        if (pdfBytesList == null || pdfBytesList.isEmpty()) {
            throw new IllegalArgumentException("PDF列表不能为空");
        }

        if (pdfBytesList.size() == 1) {
            return pdfBytesList.get(0);
        }

        log.info("开始合并PDF文件，文件数量: {}", pdfBytesList.size());

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 创建输出PDF文档
            PdfWriter writer = new PdfWriter(outputStream);
            PdfDocument outputPdf = new PdfDocument(writer);

            // 创建PDF合并器
            PdfMerger merger = new PdfMerger(outputPdf);

            // 逐个合并PDF文件
            for (int i = 0; i < pdfBytesList.size(); i++) {
                byte[] pdfBytes = pdfBytesList.get(i);
                if (pdfBytes == null || pdfBytes.length == 0) {
                    log.warn("跳过空的PDF文件，索引: {}", i);
                    continue;
                }

                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes)) {
                    PdfReader reader = new PdfReader(inputStream);
                    PdfDocument inputPdf = new PdfDocument(reader);

                    // 合并所有页面
                    merger.merge(inputPdf, 1, inputPdf.getNumberOfPages());

                    inputPdf.close();
                    log.debug("成功合并第 {} 个PDF文件", i + 1);
                } catch (Exception e) {
                    log.error("合并第 {} 个PDF文件失败: {}", i + 1, e.getMessage());
                    throw new IOException("合并PDF文件失败: " + e.getMessage(), e);
                }
            }

            outputPdf.close();

            byte[] mergedPdfBytes = outputStream.toByteArray();
            log.info("PDF合并完成，合并后大小: {} bytes", mergedPdfBytes.length);

            return mergedPdfBytes;

        } catch (Exception e) {
            log.error("PDF合并过程中出现异常: {}", e.getMessage(), e);
            throw new IOException("PDF合并失败: " + e.getMessage(), e);
        }
    }

}
