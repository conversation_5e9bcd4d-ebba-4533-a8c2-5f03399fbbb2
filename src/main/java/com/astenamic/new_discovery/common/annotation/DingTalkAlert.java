package com.astenamic.new_discovery.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DingTalkAlert {
    /**
     * 使用哪个机器人发送消息，默认使用 default 机器人
     */
    String botKey() default "default";

    /**
     * 告警标题
     */
    String title() default "系统异常通知";

    /**
     * 是否@所有人
     */
    boolean atAll() default false;

    /**
     * 需要@的手机号列表
     */
    String[] atMobiles() default {};
}