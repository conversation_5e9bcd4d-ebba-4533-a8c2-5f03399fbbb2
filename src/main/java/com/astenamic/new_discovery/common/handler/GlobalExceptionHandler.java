package com.astenamic.new_discovery.common.handler;


import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BizException.class)
    public Result<Void> handleBiz(BizException ex) {
        log.error("ex", ex);
        return ex.getError() == null ? Result.fail(ex.getMessage()) :
                Result.fail(ex.getError());
    }

    @ExceptionHandler({MethodArgumentNotValidException.class,
            ConstraintViolationException.class})
    public Result<Void> handleValidate(Exception ex) {
        log.error("ex", ex);
        return Result.fail(Result.ErrorCode.VALIDATION_FAILED);
    }

    @ExceptionHandler(Exception.class)
    public Result<Void> handleOther(Exception ex) {
        log.error("ex", ex);
        return Result.fail(Result.ErrorCode.SERVER_ERROR);
    }
}
