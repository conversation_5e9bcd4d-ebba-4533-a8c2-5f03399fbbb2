package com.astenamic.new_discovery.common.aspect;

import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.dingtalk.bot.DingTalkRobotService;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Aspect
@Component
public class DingTalkAlertAspect {

    @Autowired
    private DingTalkRobotService dingTalkRobotService;

    @Pointcut("@annotation(com.astenamic.new_discovery.common.annotation.DingTalkAlert)")
    public void dingTalkAlertPointcut() {
    }

    @Around("dingTalkAlertPointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        try {
            // 执行目标方法
            return point.proceed();
        } catch (Throwable e) {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            DingTalkAlert alert = method.getAnnotation(DingTalkAlert.class);
            // 发送钉钉告警
            sendExceptionAlert(point, method, alert, e);
            // 重新抛出异常
            throw e;
        }
    }

    private void sendExceptionAlert(ProceedingJoinPoint point, Method method, DingTalkAlert alert, Throwable e) {
        try {
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            String requestInfo = "";
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                requestInfo = String.format("请求URL: %s\n\n请求方式: %s\n\n",
                        request.getRequestURL().toString(),
                        request.getMethod());
            }

            // 构建错误信息
            String className = point.getTarget().getClass().getName();
            String methodName = method.getName();
            String params = Arrays.toString(point.getArgs());

            // 构建Markdown消息
            String markdownContent = "### " + alert.title() + "\n\n" +
                    "**时间：** " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n\n" +
                    "**类名：** " + className + "\n\n" +
                    "**方法：** " + methodName + "\n\n" +
                    "**参数：** " + params + "\n\n" +
                    requestInfo +
                    "**异常类型：** " + e.getClass().getName() + "\n\n" +
                    "**异常信息：** " + e.getMessage() + "\n\n" +
                    "**异常堆栈：** \n\n```\n" +
                    getStackTrace(e) + "\n```";

            // 转换@手机号列表
            List<String> atMobiles = Arrays.asList(alert.atMobiles());

            // 发送钉钉消息
            dingTalkRobotService.sendMarkdownMessage(
                    alert.botKey(),
                    alert.title(),
                    markdownContent,
                    atMobiles,
                    alert.atAll()
            );
        } catch (Exception ex) {
            // 确保告警发送失败不影响原始异常抛出
            ex.printStackTrace();
        }
    }

    private String getStackTrace(Throwable e) {
        // 获取堆栈前10行
        return Arrays.stream(e.getStackTrace())
                .limit(10)
                .map(StackTraceElement::toString)
                .collect(Collectors.joining("\n"));
    }
}
