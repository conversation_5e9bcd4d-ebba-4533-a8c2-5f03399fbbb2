package com.astenamic.new_discovery.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * REST 客户端配置
 */
@Configuration
public class RestClientConfig {

    private final ObjectMapper objectMapper;

    public RestClientConfig(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .additionalMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper))
                .setConnectTimeout(Duration.ofMillis(60*60*1000))  // 连接超时
                .setReadTimeout(Duration.ofMillis(60*60*1000))    // 读取超时
                .build();
    }
}
