package com.astenamic.new_discovery.common.config;

import com.fasterxml.jackson.core.StreamReadConstraints;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * JSON 序列化配置
 */
@Configuration
public class JsonConfig {

    @Bean("DateTimeYMDHMSFormatter")
    public DateTimeFormatter dateTimeYMDHMSFormatter(){
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    @Bean("DateTimeYMFormatter")
    public DateTimeFormatter dateTimeYMFormatter(){
        return DateTimeFormatter.ofPattern("yyyy-MM");
    }

    @Bean("DateTimeYMDFormatter")
    public DateTimeFormatter dateTimeYMDFormatter(){
        return DateTimeFormatter.ofPattern("yyyy-MM-dd");
    }

    @Bean
    public ObjectMapper objectMapper(@Qualifier("DateTimeYMDHMSFormatter") DateTimeFormatter dateTimeFormatter) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.getFactory().setStreamReadConstraints(
                StreamReadConstraints.builder().maxStringLength(30000000).build()
        );

        // 注册 JavaTimeModule 并设置 LocalDateTime 序列化格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 禁用 FAIL_ON_EMPTY_BEANS
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        return mapper;
    }
}
