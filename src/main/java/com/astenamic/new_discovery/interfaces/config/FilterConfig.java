package com.astenamic.new_discovery.interfaces.config;

import com.astenamic.new_discovery.interfaces.rest.security.CorsFilter;
import com.astenamic.new_discovery.interfaces.rest.security.SchedulerTokenFilter;
import com.astenamic.new_discovery.interfaces.rest.security.YidaConnectorTokenFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 统一的过滤器配置
 * 负责注册所有过滤器（CORS、安全等）
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class FilterConfig {

    private final SecurityProperties securityProperties;

    /**
     * 注册 CORS 过滤器
     */
    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        FilterRegistrationBean<CorsFilter> registration = new FilterRegistrationBean<>();

        registration.setFilter(new CorsFilter());
        registration.addUrlPatterns("/*");
        registration.setName("corsFilter");
        registration.setOrder(0); // CORS 过滤器应该最先执行

        log.info("注册 CORS 过滤器: /*");
        return registration;
    }

    /**
     * 注册 Yida Token 校验过滤器
     */
    @Bean
    public FilterRegistrationBean<YidaConnectorTokenFilter> yidaTokenFilter() {
        FilterRegistrationBean<YidaConnectorTokenFilter> registration = new FilterRegistrationBean<>();

        registration.setFilter(new YidaConnectorTokenFilter(securityProperties));
        registration.addUrlPatterns("/yida/*");
        registration.setName("yidaTokenFilter");
        registration.setOrder(10);

        log.info("注册 Yida Token 过滤器: /yida/*");
        return registration;
    }

    /**
     * 注册 SchedulerV2 Token 校验过滤器
     */
    @Bean
    public FilterRegistrationBean<SchedulerTokenFilter> schedulerTokenFilter() {
        FilterRegistrationBean<SchedulerTokenFilter> registration = new FilterRegistrationBean<>();

        registration.setFilter(new SchedulerTokenFilter(securityProperties));
        registration.addUrlPatterns("/api/scheduler/*");
        registration.setName("schedulerTokenFilter");
        registration.setOrder(20);

        log.info("注册 Scheduler Token 过滤器: /api/scheduler/*");
        return registration;
    }
}
