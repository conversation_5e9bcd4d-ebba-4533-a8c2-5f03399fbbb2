package com.astenamic.new_discovery.interfaces.config;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
public class WebMvcPathPrefixConfig implements WebMvcConfigurer {

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix("/yida/connector",
                HandlerTypePredicate.forAnnotation(YidaConnector.class));
    }

}