package com.astenamic.new_discovery.interfaces.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 统一的安全配置属性
 * 管理所有模块的安全配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "security")
public class SecurityProperties {

    /** 各模块的安全配置 */
    private Map<String, ModuleConfig> modules = new HashMap<>();

    /**
     * 获取指定模块的配置
     */
    public ModuleConfig getModuleConfig(String moduleName) {
        return modules.getOrDefault(moduleName, new ModuleConfig());
    }

    /**
     * 获取 Yida 模块配置
     */
    public ModuleConfig getYidaConfig() {
        return getModuleConfig("yida");
    }

    /**
     * 获取 Scheduler 模块配置
     */
    public ModuleConfig getSchedulerConfig() {
        return getModuleConfig("scheduler");
    }

    /**
     * 单个模块的安全配置
     */
    @Data
    public static class ModuleConfig {
        
        /** Token 配置 */
        private Token token = new Token();

        /** 其他安全配置可以在这里扩展 */
        // private OAuth oauth = new OAuth();
        // private Jwt jwt = new Jwt();

        @Data
        public static class Token {
            /** 有效的 Token 列表 */
            private Set<String> keys = Set.of();
            
            /** URL 参数名称 */
            private String paramName = "token";
            
            /** 是否允许从请求头获取 Token */
            private boolean allowHeaderFallback = true;
            
            /** 排除校验的路径列表 */
            private List<String> excludePaths = List.of();
        }
    }
}
