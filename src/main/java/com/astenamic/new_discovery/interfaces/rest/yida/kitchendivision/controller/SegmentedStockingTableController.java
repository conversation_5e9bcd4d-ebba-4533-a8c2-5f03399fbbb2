package com.astenamic.new_discovery.interfaces.rest.yida.kitchendivision.controller;

import com.astenamic.new_discovery.biz.kitchendivision.application.service.SegmentedStockingTableService;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.kitchendivision.dto.PdfGenerateDto;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import com.astenamic.new_discovery.util.TimeUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 分段备货表控制器
 */
@Slf4j
@RestController
@YidaConnector("/api/v1/kitchen-division/segmented-stocking")
@RequiredArgsConstructor
public class SegmentedStockingTableController {

    private final SegmentedStockingTableService segmentedStockingTableService;

    /**
     * 生成分段备货表PDF
     */
    @GetMapping("/generate-pdf")
    public void generatePdf(@RequestParam String sysShopId, @RequestParam Long targetDate, HttpServletResponse response) {
        try {
            LocalDateTime dateTime = TimeUtils.ofEpochMilli(targetDate);
            PdfGenerateDto result = segmentedStockingTableService.generatePdf(sysShopId, dateTime);

            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" +
                    URLEncoder.encode(result.getFileName(), StandardCharsets.UTF_8));
            response.setHeader("Content-Length", String.valueOf(result.getPdfBytes().length));
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            response.getOutputStream().write(result.getPdfBytes());
            response.flushBuffer();

            log.info("PDF输出成功，门店: {}, 文件: {}, 大小: {} bytes", result.getShopName(), result.getFileName(), result.getPdfBytes().length);

        } catch (Exception e) {
            log.error("生成PDF失败", e);
            // 返回错误的JSON响应
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                Result<String> errorResult = Result.fail(Result.ErrorCode.SERVER_ERROR, "生成PDF失败: " + e.getMessage());
                response.getWriter().write(com.alibaba.fastjson2.JSON.toJSONString(errorResult));
                response.flushBuffer();
            } catch (IOException ioException) {
                log.error("返回错误响应失败", ioException);
            }
        }
    }
}
