package com.astenamic.new_discovery.interfaces.rest.yida;


import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@RestController
@RequestMapping
public @interface YidaConnector {

    /**
     * 等价于 {@link RequestMapping#path} / {@link RequestMapping#value}，
     * 用于在固定前缀后追加子路径：
     * <pre>
     *   @YidaConnector("/order")
     *   → /yida/connector/order
     * </pre>
     */
    @AliasFor(annotation = RequestMapping.class, attribute = "path")
    String[] value() default {};

    /** 同 RequestMapping.method() */
    @AliasFor(annotation = RequestMapping.class, attribute = "method")
    org.springframework.web.bind.annotation.RequestMethod[] method() default {};
}