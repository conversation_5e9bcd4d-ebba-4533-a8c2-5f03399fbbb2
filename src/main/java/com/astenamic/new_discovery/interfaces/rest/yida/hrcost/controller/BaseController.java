package com.astenamic.new_discovery.interfaces.rest.yida.hrcost.controller;


import com.astenamic.new_discovery.biz.hrcost.application.service.BaseAppService;
import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.util.ThreadPoolUtils;

public abstract class BaseController<T extends BaseEntity> {

    protected BaseAppService<T> baseAppService;

    public BaseController(BaseAppService<T> baseAppService) {
        this.baseAppService = baseAppService;
    }

    public Result<Void> syncToYida(String objectId) {
        try {
            return baseAppService.yidaToDb(objectId) ? Result.ok() : Result.fail("同步失败！");
        } catch (Exception e) {
            return Result.fail("同步失败！" + e.getMessage());
        }
    }

    public Result<Void> syncToYida() {
        try {
            ThreadPoolUtils.getIoThreadPool().execute(() -> {
                baseAppService.yidaBatchToDb();
            });
            return Result.ok();
        } catch (Exception e) {
            return Result.fail("同步失败！" + e.getMessage());
        }
    }
}
