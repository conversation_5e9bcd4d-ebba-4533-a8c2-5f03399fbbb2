package com.astenamic.new_discovery.interfaces.rest.yida.warning.dto;

import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class Param {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime date;

    public void validate() {
        if (date == null) {
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED, "date is null");
        }
    }

}
