package com.astenamic.new_discovery.interfaces.rest.scheduler;

import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.schedulerV2.dto.SchedulerStatusDto;
import com.astenamic.new_discovery.schedulerV2.dto.TaskExecuteResultDto;
import com.astenamic.new_discovery.schedulerV2.dto.TaskInfoDto;
import com.astenamic.new_discovery.schedulerV2.dto.TaskListDto;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import com.astenamic.new_discovery.schedulerV2.service.SchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务调度控制器
 * Token 校验由 SchedulerTokenFilter 统一处理
 */
@Slf4j
@RestController
@RequestMapping("/api/scheduler")
@RequiredArgsConstructor
public class SchedulerController {

    private final SchedulerService schedulerService;

    /**
     * 获取调度器状态
     */
    @GetMapping("/status")
    public Result<SchedulerStatusDto> getStatus() {
        try {
            SchedulerService.TaskStatistics statistics = schedulerService.getTaskStatistics();

            SchedulerStatusDto statusDto = new SchedulerStatusDto(
                    statistics.getTotalTasks(),
                    statistics.getEnabledTasks(),
                    statistics.getDisabledTasks(),
                    "RUNNING",
                    "调度器正在运行",
                    System.currentTimeMillis()
            );

            return Result.ok(statusDto);

        } catch (Exception e) {
            log.error("获取调度器状态失败", e);
            return Result.fail(Result.ErrorCode.SERVER_ERROR, "获取调度器状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有任务列表
     */
    @GetMapping("/tasks")
    public Result<TaskListDto> getAllTasks() {
        try {
            List<TaskDefinition> taskDefinitions = schedulerService.getAllTasks();
            SchedulerService.TaskStatistics statistics = schedulerService.getTaskStatistics();
            
            List<TaskInfoDto> taskInfos = taskDefinitions.stream()
                    .map(this::convertToTaskInfoDto)
                    .collect(Collectors.toList());
            
            TaskListDto taskListDto = new TaskListDto(
                    statistics.getTotalTasks(),
                    statistics.getEnabledTasks(),
                    statistics.getDisabledTasks(),
                    taskInfos
            );
            
            return Result.ok(taskListDto);
            
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return Result.fail(Result.ErrorCode.SERVER_ERROR, "获取任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务ID获取任务详情
     */
    @GetMapping("/tasks/{taskId}")
    public Result<TaskInfoDto> getTaskById(@PathVariable String taskId) {
        try {
            TaskDefinition taskDefinition = schedulerService.getTaskById(taskId);
            
            if (taskDefinition == null) {
                return Result.fail(Result.ErrorCode.TASK_NOT_FOUND, "任务不存在: " + taskId);
            }
            
            TaskInfoDto taskInfoDto = convertToTaskInfoDto(taskDefinition);
            return Result.ok(taskInfoDto);
            
        } catch (Exception e) {
            log.error("获取任务详情失败: {}", taskId, e);
            return Result.fail(Result.ErrorCode.SERVER_ERROR, "获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发任务
     */
    @PostMapping("/tasks/{taskId}/trigger")
    public Result<TaskExecuteResultDto> triggerTask(@PathVariable String taskId) {
        try {
            log.info("收到手动触发任务请求: {}", taskId);
            
            if (!schedulerService.taskExists(taskId)) {
                return Result.fail(Result.ErrorCode.TASK_NOT_FOUND, "任务不存在: " + taskId);
            }
            
            long startTime = System.currentTimeMillis();
            boolean success = schedulerService.triggerTask(taskId);
            long endTime = System.currentTimeMillis();
            
            TaskDefinition taskDefinition = schedulerService.getTaskById(taskId);
            String taskName = taskDefinition != null ? taskDefinition.getName() : taskId;
            
            TaskExecuteResultDto resultDto = new TaskExecuteResultDto(
                    taskId,
                    taskName,
                    success,
                    success ? "任务执行成功" : "任务执行失败",
                    startTime,
                    endTime,
                    endTime - startTime
            );
            
            if (success) {
                return Result.ok(resultDto);
            } else {
                return Result.fail(Result.ErrorCode.TASK_ERROR, "任务执行失败");
            }
            
        } catch (Exception e) {
            log.error("手动触发任务失败: {}", taskId, e);
            return Result.fail(Result.ErrorCode.TASK_ERROR, "任务执行异常: " + e.getMessage());
        }
    }

    /**
     * 注销任务
     */
    @DeleteMapping("/tasks/{taskId}")
    public Result<Void> unregisterTask(@PathVariable String taskId) {
        try {
            log.info("收到注销任务请求: {}", taskId);
            
            if (!schedulerService.taskExists(taskId)) {
                return Result.fail(Result.ErrorCode.TASK_NOT_FOUND, "任务不存在: " + taskId);
            }
            
            boolean success = schedulerService.unregisterTask(taskId);
            
            if (success) {
                return Result.ok();
            } else {
                return Result.fail(Result.ErrorCode.SERVER_ERROR, "注销任务失败");
            }
            
        } catch (Exception e) {
            log.error("注销任务失败: {}", taskId, e);
            return Result.fail(Result.ErrorCode.SERVER_ERROR, "注销任务失败: " + e.getMessage());
        }
    }

    /**
     * 转换TaskDefinition为TaskInfoDto
     */
    private TaskInfoDto convertToTaskInfoDto(TaskDefinition taskDefinition) {
        return new TaskInfoDto(
                taskDefinition.getTaskId(),
                taskDefinition.getName(),
                taskDefinition.getDescription(),
                taskDefinition.getCronExpression(),
                taskDefinition.getSchedulerTaskType() != null ? 
                        taskDefinition.getSchedulerTaskType().name() : null,
                taskDefinition.getPriority(),
                taskDefinition.getMaxRetries(),
                taskDefinition.getRetryInterval(),
                taskDefinition.isAlertOnFailure(),
                taskDefinition.getExecutorType()
        );
    }
}
