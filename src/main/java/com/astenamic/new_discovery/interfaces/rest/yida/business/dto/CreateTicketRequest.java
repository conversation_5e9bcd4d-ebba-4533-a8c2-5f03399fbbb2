package com.astenamic.new_discovery.interfaces.rest.yida.business.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CreateTicketRequest {

    @NotBlank(message = "userId is null")
    private String userId;

    @NotBlank(message = "workId is null")
    private String workId;

    private List<Params> params = new ArrayList<>();

    @Data
    public static class Params {
        private String paramKey;
        private String value;
    }

}
