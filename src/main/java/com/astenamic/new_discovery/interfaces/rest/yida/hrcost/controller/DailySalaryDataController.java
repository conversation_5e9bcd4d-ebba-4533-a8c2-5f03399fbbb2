package com.astenamic.new_discovery.interfaces.rest.yida.hrcost.controller;

import com.astenamic.new_discovery.biz.hrcost.application.service.DailySalaryDataAppService;
import com.astenamic.new_discovery.biz.hrcost.domain.entity.DailySalaryData;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日薪数据控制器
 * 人力成本模型 - 日薪数据管理
 * 对应宜搭表单：FORM-CFB9683494F3410AB2E9B4198A29F9B8OBG9
 */
@RestController
@YidaConnector("/hrcost/daily-salary-data")
public class DailySalaryDataController extends BaseController<DailySalaryData> {

    public DailySalaryDataController(DailySalaryDataAppService appService) {
        super(appService);
    }

    @GetMapping("/sync-to-yida/{objectId}")
    public Result<Void> syncToYida(@PathVariable String objectId) {
        return super.syncToYida(objectId);
    }

    @GetMapping("/sync-to-yida")
    public Result<Void> syncToYida() {
        return super.syncToYida();
    }
}
