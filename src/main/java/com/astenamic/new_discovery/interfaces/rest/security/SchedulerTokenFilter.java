package com.astenamic.new_discovery.interfaces.rest.security;

import com.astenamic.new_discovery.interfaces.config.SecurityProperties;

/**
 * SchedulerV2 API Token 校验过滤器
 */
public class SchedulerTokenFilter extends AbstractTokenFilter {

    public SchedulerTokenFilter(SecurityProperties properties) {
        super(properties.getSchedulerConfig().getToken());
    }

    @Override
    protected boolean isPathMatched(String requestURI) {
        return requestURI.startsWith("/api/scheduler");
    }

}
