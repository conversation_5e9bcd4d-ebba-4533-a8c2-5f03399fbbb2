package com.astenamic.new_discovery.interfaces.rest.yida.purchase.controller;


import com.astenamic.new_discovery.biz.purchase.application.service.NewProductIntroductionHandlerService;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 新品引入流程
 */
@YidaConnector("/need")
@AllArgsConstructor
public class NewProductIntroductionController {

    private final NewProductIntroductionHandlerService handlerService;


    /**
     * 新品引入流程完成后触发定价流程推进
     */
    @GetMapping("/pricing/{requirementCode}")
    public Result<Void> handlePricing(@PathVariable("requirementCode") String requirementCode) {
        if (StringUtils.isEmpty(requirementCode)) {
            return Result.fail("需求单号不能为空!");
        }
        handlerService.handleProductPricing(requirementCode);
        return Result.ok();
    }

    /**
     * 同步寻源认证
     */
    @GetMapping("/sync-source-auth-item/{requirementCode}")
    public Result<Void> syncSourceAuthItem(@PathVariable("requirementCode") String requirementCode) {
        if (StringUtils.isEmpty(requirementCode)) {
            return Result.fail("需求单号不能为空!");
        }
        handlerService.syncSourceAuthItem(requirementCode);
        return Result.ok();
    }

    /**
     * 同步新供应商信息
     */
    @GetMapping("/sync-supplier-info/{requirementCode}")
    public Result<Void> syncSupplierInfo(@PathVariable("requirementCode") String requirementCode) {
        if (StringUtils.isEmpty(requirementCode)) {
            return Result.fail("需求单号不能为空!");
        }
        handlerService.syncNewSupplierInfo(requirementCode);
        return Result.ok();
    }

}
