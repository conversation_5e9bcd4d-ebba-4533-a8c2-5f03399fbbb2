package com.astenamic.new_discovery.interfaces.rest.yida.hrcost.controller;

import com.astenamic.new_discovery.biz.hrcost.application.service.OtherCostRatioAppService;
import com.astenamic.new_discovery.biz.hrcost.domain.entity.OtherCostRatio;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人力成本模型 - 其它费用占比控制器
 * 对应宜搭表单：FORM-4C87A236AA7E45F68E6478D64D7D1BABHE20
 */
@RestController
@YidaConnector("/hrcost/other-cost-ratio")
public class OtherCostRatioController extends BaseController<OtherCostRatio> {

    public OtherCostRatioController(OtherCostRatioAppService appService) {
        super(appService);
    }

    @GetMapping("/sync-to-yida/{objectId}")
    public Result<Void> syncToYida(@PathVariable String objectId) {
        return super.syncToYida(objectId);
    }

    @GetMapping("/sync-to-yida")
    public Result<Void> syncToYida() {
        return super.syncToYida();
    }
}
