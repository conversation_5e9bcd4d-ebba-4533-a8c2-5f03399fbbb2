package com.astenamic.new_discovery.interfaces.rest.yida.hrcost.controller;

import com.astenamic.new_discovery.biz.hrcost.application.service.StoreAbcDiscountRevenueAppService;
import com.astenamic.new_discovery.biz.hrcost.domain.entity.StoreAbcDiscountRevenue;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人力成本模型 - 门店abc折后营收表控制器
 * 对应宜搭表单：FORM-B2BBCAB5C45D41C29F2982C327D64DF2BIMI
 */
@RestController
@YidaConnector("/hrcost/store-abc-discount-revenue")
public class StoreAbcDiscountRevenueController extends BaseController<StoreAbcDiscountRevenue> {

    public StoreAbcDiscountRevenueController(StoreAbcDiscountRevenueAppService appService) {
        super(appService);
    }

    @GetMapping("/sync-to-yida/{objectId}")
    public Result<Void> syncToYida(@PathVariable String objectId) {
        return super.syncToYida(objectId);
    }

    @GetMapping("/sync-to-yida")
    public Result<Void> syncToYida() {
        return super.syncToYida();
    }
}
