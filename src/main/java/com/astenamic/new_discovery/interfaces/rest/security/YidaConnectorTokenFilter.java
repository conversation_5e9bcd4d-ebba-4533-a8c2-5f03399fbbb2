package com.astenamic.new_discovery.interfaces.rest.security;

import com.astenamic.new_discovery.interfaces.config.SecurityProperties;

/**
 * Yida 连接器 Token 校验过滤器
 */
public class YidaConnectorTokenFilter extends AbstractTokenFilter {

    public YidaConnectorTokenFilter(SecurityProperties properties) {
        super(properties.getYidaConfig().getToken());
    }

    @Override
    protected boolean isPathMatched(String requestURI) {
        return requestURI.startsWith("/yida/connector");
    }

}
