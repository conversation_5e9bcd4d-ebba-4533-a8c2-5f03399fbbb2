package com.astenamic.new_discovery.interfaces.rest.wlift.controller;


import com.astenamic.new_discovery.biz.wlift.entity.ConsumeRecord;
import com.astenamic.new_discovery.biz.wlift.service.ConsumeRecordService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/wlift")
@AllArgsConstructor
public class WliftController {

    private final ConsumeRecordService consumeRecordService;

    /**
     * 同步会员消费记录
     *
     * @param param
     */
    @GetMapping("/query/consume/list")
    public List<ConsumeRecord> syncConsumeRecord(@RequestBody Param param) {
        String dateStr = param.date;
        LocalDateTime target = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return consumeRecordService.syncConsumeRecord(target);
    }


    @Data
    private static class Param {
        private String date;
    }
}
