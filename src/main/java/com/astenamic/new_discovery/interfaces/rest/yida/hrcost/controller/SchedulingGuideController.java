package com.astenamic.new_discovery.interfaces.rest.yida.hrcost.controller;

import com.astenamic.new_discovery.biz.hrcost.application.service.SchedulingGuideAppService;
import com.astenamic.new_discovery.biz.hrcost.domain.entity.SchedulingGuide;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
@YidaConnector("/hrcost/scheduling-guide")
public class SchedulingGuideController extends BaseController<SchedulingGuide> {

    public SchedulingGuideController(SchedulingGuideAppService appService) {
        super(appService);
    }

    @GetMapping("/sync-to-yida/{objectId}")
    public Result<Void> syncToYida(@PathVariable String objectId) {
        return super.syncToYida(objectId);
    }

    @GetMapping("/sync-to-yida")
    public Result<Void> syncToYida() {
        return super.syncToYida();
    }
}
