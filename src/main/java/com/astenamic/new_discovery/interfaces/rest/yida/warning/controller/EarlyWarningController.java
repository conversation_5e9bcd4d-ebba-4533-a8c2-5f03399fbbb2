package com.astenamic.new_discovery.interfaces.rest.yida.warning.controller;

import com.astenamic.new_discovery.biz.flow.warning.service.AbnormalPriceService;
import com.astenamic.new_discovery.biz.flow.warning.service.base.EarlyWarningService;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.warning.dto.Param;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 预警流程表单
 */
@RestController
@RequestMapping("/biz/warning")
@AllArgsConstructor
public class EarlyWarningController {

    private final EarlyWarningService foodBackWarningService;

    private final EarlyWarningService takeoutDiscountService;

    private final EarlyWarningService employeeMealService;

    private final AbnormalPriceService abnormalPriceService;

    /**
     * 退菜类型预警
     *
     * @param param
     */
    @GetMapping("/food/type")
    public Result<Void> foodType(@RequestBody Param param) {
        param.validate();
        foodBackWarningService.foodBackWarning(param.getDate());
        return Result.ok();
    }

    /**
     * 外卖代金卷预警
     *
     * @param param
     */
    @GetMapping("/takeout/discount")
    public Result<Void> takeoutDiscount(@RequestBody Param param) {
        param.validate();
        takeoutDiscountService.takeoutDiscountWarning(param.getDate());
        return Result.ok();
    }

    /**
     * 员工餐预警
     */
    @GetMapping("/employee/meal")
    public Result<Void> employeeMeal(@RequestBody Param param) {
        param.validate();
        employeeMealService.employeeMealWarning(param.getDate());
        return Result.ok();
    }

    /**
     * 采购成本异常波动预警
     */
    @GetMapping("/abnormal/price")
    public Result<Void> abnormalPrice(@RequestBody Param param) {
        param.validate();
        abnormalPriceService.detectAbnormalPriceFluctuation(param.getDate());
        return Result.ok();
    }

}
