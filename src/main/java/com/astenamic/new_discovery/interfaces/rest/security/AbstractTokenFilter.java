package com.astenamic.new_discovery.interfaces.rest.security;

import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.config.SecurityProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;

import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * 抽象 Token 过滤器基类
 * 提供统一的 Token 校验逻辑
 */
public abstract class AbstractTokenFilter extends OncePerRequestFilter {

    private static final ObjectMapper JSON = JsonMapper.builder().build();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    protected final Set<String> validKeys;
    protected final String paramName;
    protected final boolean allowHeaderFallback;
    protected final List<String> excludePaths;

    public AbstractTokenFilter(SecurityProperties.ModuleConfig.Token tokenConfig) {
        this.validKeys = tokenConfig.getKeys();
        this.paramName = tokenConfig.getParamName();
        this.allowHeaderFallback = tokenConfig.isAllowHeaderFallback();
        this.excludePaths = tokenConfig.getExcludePaths();
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        
        // 检查路径是否匹配当前过滤器
        if (!isPathMatched(requestURI)) {
            return true;
        }

        // 检查是否有 @PublicEndpoint 注解
        Object handler = request.getAttribute(HandlerMapping.BEST_MATCHING_HANDLER_ATTRIBUTE);
        if (handler instanceof HandlerMethod handlerMethod) {
            if (handlerMethod.getMethodAnnotation(PublicEndpoint.class) != null ||
                handlerMethod.getBeanType().isAnnotationPresent(PublicEndpoint.class)) {
                return true;
            }
        }

        // 检查是否在排除路径中
        return excludePaths.stream()
                .anyMatch(excludePath -> pathMatcher.match(excludePath, requestURI));
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain)
            throws IOException, ServletException {

        String token = extractToken(request);

        if (isValidToken(token)) {
            filterChain.doFilter(request, response);
            return;
        }

        // Token 校验失败，返回错误响应
        sendErrorResponse(response);
    }

    /**
     * 判断请求路径是否匹配当前过滤器
     * 子类需要实现此方法来定义过滤范围
     */
    protected abstract boolean isPathMatched(String requestURI);

    /**
     * 获取过滤器的请求头名称
     * 子类可以重写此方法来自定义请求头名称
     */
    protected String getHeaderName() {
        return "X-Token";
    }

    /**
     * 提取 Token
     */
    private String extractToken(HttpServletRequest request) {
        // 优先从 URL 参数获取
        String token = request.getParameter(paramName);
        
        // 如果允许，从请求头获取
        if (!StringUtils.hasText(token) && allowHeaderFallback) {
            token = request.getHeader(getHeaderName());
        }
        
        return token;
    }

    /**
     * 验证 Token 是否有效
     */
    private boolean isValidToken(String token) {
        return StringUtils.hasText(token) && validKeys.contains(token);
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        Result<Void> errorResult = Result.fail(Result.ErrorCode.INVALID_TOKEN);
        JSON.writeValue(response.getWriter(), errorResult);
    }
}
