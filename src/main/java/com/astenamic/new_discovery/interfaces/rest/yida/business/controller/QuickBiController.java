package com.astenamic.new_discovery.interfaces.rest.yida.business.controller;


import com.astenamic.new_discovery.biz.business.application.service.QuickBiService;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import com.astenamic.new_discovery.interfaces.rest.yida.business.dto.CreateTicketRequest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * QuickBI控制器
 */
@Slf4j
@RestController("businessQuickBiController")
@YidaConnector("/api/v1/business/quickbi")
@RequiredArgsConstructor
public class QuickBiController {

    private final QuickBiService quickBiService;


    /**
     * 获取QuickBI票据
     *
     * @return 包含票据的异步结果
     */
    @PostMapping("/create/ticket")
    public CompletableFuture<Result<String>> getTicketWithMultiParamsAsync(@Valid @RequestBody CreateTicketRequest request) {
        return quickBiService.getTicketAsync(request);
    }


}