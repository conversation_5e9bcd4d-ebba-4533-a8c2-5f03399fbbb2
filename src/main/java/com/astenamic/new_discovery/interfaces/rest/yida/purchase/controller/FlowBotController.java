package com.astenamic.new_discovery.interfaces.rest.yida.purchase.controller;


import com.astenamic.new_discovery.biz.purchase.application.service.FlowBotService;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.NewProductIntroductionFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.interfaces.rest.yida.YidaConnector;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 流程机器人控制器
 */
@YidaConnector("/bot")
@AllArgsConstructor
public class FlowBotController {

    private final FlowBotService<NewProductIntroductionFlow> newProductFlowBotServiceImpl;

    private final FlowBotService<ProductPricingFlow> productPricingFlowBotServiceImpl;

    /**
     * 新货品引入流程机器人
     *
     * @param flowCode 流程Code
     */
    @GetMapping("/newProductFlow/{flowCode}")
    public Result<Void> newProductFlow(@PathVariable("flowCode") String flowCode) {
        if (StringUtils.isBlank(flowCode)) {
            return Result.fail("流程Code不能为空");
        }
        newProductFlowBotServiceImpl.executeTask(flowCode);
        return Result.ok();
    }


    /**
     * 定价流程机器人
     *
     * @param flowCode 流程Code
     */
    @GetMapping("/productPricingFlow/{flowCode}")
    public Result<Void> productPricingFlow(@PathVariable("flowCode") String flowCode) {
        if (StringUtils.isBlank(flowCode)) {
            return Result.fail("流程Code不能为空");
        }
        productPricingFlowBotServiceImpl.executeTask(flowCode);
        return Result.ok();
    }
}
