package com.astenamic.new_discovery.form;
import com.astenamic.new_discovery.form.manage.FormManager;
import lombok.Getter;
import lombok.SneakyThrows;

import java.lang.reflect.Field;

public class FormBaseTypeField extends FormProperty {
    @Getter
    private final Field field;
    public FormBaseTypeField(Field field, String code) {
        super(code, field.getName());
        this.field = field;
        this.field.setAccessible(true);
    }

    @Override
    @SneakyThrows()
    public Object getValue(Object object, FormManager formManager) {
        return this.field.get(object);
    }
}
