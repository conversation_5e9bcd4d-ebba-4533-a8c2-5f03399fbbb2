package com.astenamic.new_discovery.form;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.manage.FormManager;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.List;

public class FormEmbeddedObjectList extends FormBaseTypeField {
    @Getter
    private final Class<?> inferType;

    public FormEmbeddedObjectList(Field field, String code, Class<?> inferType) {
        super(field, code);
        this.inferType = inferType;
    }

    @Override
    public Object getValue(Object object, FormManager formManager) {

        Object val = super.getValue(object, formManager);

        if (val == null || !List.class.isAssignableFrom(val.getClass())) {
            return null;
        }

        boolean is = formManager.isFormEntity(this.inferType);

        return is ? ((List<?>) val)
                .stream()
                .map(o -> formManager.serialize(o, (Class<Object>) this.inferType))
                .map(FormJsonData::getData)
                .toList()
                :
                val;
    }
}
