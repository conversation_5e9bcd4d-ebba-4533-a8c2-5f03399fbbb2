package com.astenamic.new_discovery.form.configuration;

import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.form.scan.FormEntityScanner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FormConfiguration {

    @Bean
    public FormEntityScanner createFormEntityScan(){
        return new FormEntityScanner("*");
    }

    @Bean
    public FormManager createFormManager(FormEntityScanner scanner){
        return new FormManager(scanner).init();
    }
}
