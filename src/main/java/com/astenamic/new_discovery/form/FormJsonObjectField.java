package com.astenamic.new_discovery.form;

import com.astenamic.new_discovery.form.manage.FormManager;

import java.lang.reflect.Field;

public class FormJsonObjectField extends FormBaseTypeField {
    private final Class<?> type;

    public FormJsonObjectField(Field field, String code) {
        super(field, code);
        this.type = field.getType();
    }

    @Override
    public Object getValue(Object object, FormManager formManager) {
        return super.getValue(object, formManager);
    }
}
