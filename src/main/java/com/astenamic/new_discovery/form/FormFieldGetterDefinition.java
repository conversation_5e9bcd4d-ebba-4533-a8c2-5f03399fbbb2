package com.astenamic.new_discovery.form;
import com.astenamic.new_discovery.form.manage.FormManager;
import lombok.SneakyThrows;

import java.lang.reflect.Method;

public class FormFieldGetterDefinition extends FormProperty {
    private final Method method;
    public FormFieldGetterDefinition(Method method, String code) {
        super(code, method.getName());
        this.method = method;
        this.method.setAccessible(true);
    }

    @Override
    @SneakyThrows()
    public Object getValue(Object object, FormManager formManager) {
        try {
            return method.invoke(object);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
