package com.astenamic.new_discovery.form;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class FormJsonDataList {

    private String formCode;
    private JSONArray data;

    @Override
    public String toString(){
        return this.data.toJSONString();
    }

    public List<String> toStringList(){
        return this.data.stream().map(JSONObject::toJSONString).toList();
    }

    public List<JSONObject> toJsonObjectList(){
        return this.data.stream().map(JSONObject::from).toList();
    }

}
