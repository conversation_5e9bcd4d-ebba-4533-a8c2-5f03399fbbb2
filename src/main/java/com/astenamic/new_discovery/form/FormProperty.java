package com.astenamic.new_discovery.form;

import com.astenamic.new_discovery.form.manage.FormManager;
import lombok.Getter;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public abstract class FormProperty {
    @Getter
    private final String code;
    private final String name;
    public FormProperty(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName(){
        return this.name;
    }

    public abstract Object getValue(Object object, FormManager formManager);

    public static boolean isDate(Field field) {
        Class<?> type = field.getType();
        return Date.class.isAssignableFrom(type) || LocalDateTime.class.isAssignableFrom(type);
    }

    public static boolean isDateList(Field field) {
        if (!List.class.isAssignableFrom(field.getType())) {
            return false;
        }

        // 检查泛型参数是否为LocalDateTime或Date
        if (field.getGenericType() instanceof java.lang.reflect.ParameterizedType parameterizedType) {
            java.lang.reflect.Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                java.lang.reflect.Type actualType = actualTypeArguments[0];
                if (actualType instanceof Class<?> clazz) {
                    return Date.class.isAssignableFrom(clazz) || LocalDateTime.class.isAssignableFrom(clazz);
                }
            }
        }

        return false;
    }

    public static boolean isBaseType(Field field) {

        Class<?> type = field.getType();

        return String.class.isAssignableFrom(type) ||
                Number.class.isAssignableFrom(type) ||
                Boolean.class.isAssignableFrom(type);
    }

    public static boolean isArray(Field field) {

        Class<?> type = field.getType();

        return List.class.isAssignableFrom(type);
    }

    public static boolean isObject(Field field) {

        Class<?> type = field.getType();

        return Object.class.isAssignableFrom(type) && !List.class.isAssignableFrom(type);
    }
}
