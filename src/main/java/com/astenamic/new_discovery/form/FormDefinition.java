package com.astenamic.new_discovery.form;

import lombok.Data;

import java.util.List;

@Data
public class FormDefinition {
    private String code;
    private String appType;

    private String sysToken;
    private List<? extends FormProperty> formFields;

    public FormDefinition(String code, String appType, String sysToken, List<? extends FormProperty> formFields) {
        this.code = code;
        this.formFields = formFields;
        this.appType = appType;
        this.sysToken = sysToken;
    }

    public String getCode() {
        return code;
    }
}
