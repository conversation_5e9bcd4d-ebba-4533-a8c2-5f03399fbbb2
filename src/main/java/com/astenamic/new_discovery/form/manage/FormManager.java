package com.astenamic.new_discovery.form.manage;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.form.*;
import com.astenamic.new_discovery.form.scan.FormEntityScanner;
import com.astenamic.new_discovery.yida.modal.YidaComponent;
import lombok.SneakyThrows;


import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

public class FormManager {
    private final FormEntityScanner formEntityScanner;
    private Map<Class<?>, FormDefinition> fds;
    private final static Map<Class<?>, List<Field>> clsFieldCache = new HashMap<>();

    public FormManager(FormEntityScanner formEntityScanner) {
        this.formEntityScanner = formEntityScanner;
    }

    public FormManager init() {

        if (this.formEntityScanner == null) {
            throw new IllegalStateException("未配置表单扫描器");
        }

        this.fds = this.formEntityScanner.scan();

        return this;
    }

    public String getFormCodeByClass(Class<?> tClass) {
        FormDefinition definition = this.getFormDefinition(tClass);
        return definition.getCode();
    }

    public <T> List<T> deserialize(List<JSONObject> jsons, Class<T> tClass) {

        FormDefinition definition = this.getFormDefinition(tClass);

        List<T> data = new ArrayList<>();

        for (JSONObject json : jsons) {
            T d = deserialize(json, tClass, definition);

            if (d == null) {
                continue;
            }

            data.add(d);
        }

        return data;
    }

    public void deserialize(JSONObject json, FormDefinition definition) {

        String code;
        Object val;

        for (FormProperty p : definition.getFormFields()) {

            code = p.getCode();


            if (code.contains("association")) {
                code = code + "_id";
            }

            if ((code.contains("employee") || code.contains("departmentSelectField")) && !code.contains("_id")) {
                code = code + "_id";
            }

            if (!json.containsKey(code)) continue;

            val = json.remove(code);

            if(code.contains("association")){
                JSONArray objects = JSON.parseArray((String) JSON.parse(String.valueOf(val)));
                json.put(p.getName(), objects);
                continue;
            }

            if (val == null) {
                continue;
            }

            if (p instanceof FormEmbeddedObjectList) {

                Class<?> type = ((FormEmbeddedObjectList) p).getInferType();

                if (!YidaComponent.class.isAssignableFrom(type)) {

                    if (!List.class.isAssignableFrom(val.getClass())) {
                        throw new IllegalArgumentException("无法返序列化类型：" + val.getClass().getName());
                    }

                    FormDefinition innerF = this.getFormDefinition(type);

                    List<JSONObject> jarr = new ArrayList<>();

                    for (Object o : (List<Object>) val) {

                        if (!Map.class.isAssignableFrom(o.getClass())) {
                            throw new IllegalArgumentException("无法返序列化值：" + o.getClass().getName());
                        }

                        JSONObject j = new JSONObject((Map<String, Object>) o);

                        this.deserialize(j, innerF);

                        jarr.add(j);

                    }

                    val = jarr;
                }
            }

            json.put(p.getName(), val);
        }
    }


    public <T> T deserialize(JSONObject json, Class<T> tClass, FormDefinition definition) {

        this.deserialize(json, definition);

        return json.to(tClass);
    }

    public <T extends Object> FormJsonData serialize(T d, Class<T> tClass) {

        FormDefinition definition = this.getFormDefinition(tClass);

        JSONObject jsonObject = this.serialize(d, definition);

        return new FormJsonData(definition.getCode(), jsonObject);
    }

    public <T> JSONObject serialize(T d, FormDefinition definition) {

        JSONObject json = new JSONObject();

        for (FormProperty p : definition.getFormFields()) {

            Object val = p.getValue(d, this);

            if (val == null) {
                continue;
            }

            json.put(p.getCode(), val);
        }

        return json;
    }

    public <T> FormJsonDataList serialize(List<T> data, Class<T> tClass) {

        FormDefinition definition = this.getFormDefinition(tClass);

        JSONArray jsonArray = new JSONArray();

        for (T d : data) {

            JSONObject json = serialize(d, definition);

            if (json.isEmpty()) {
                continue;
            }

            jsonArray.add(json);
        }

        FormJsonDataList formJsonDataList = new FormJsonDataList();
        formJsonDataList.setFormCode(definition.getCode());
        formJsonDataList.setData(jsonArray);

        return formJsonDataList;
    }

    public boolean isFormEntity(Class<?> tClass) {
        return this.fds.get(tClass) != null;
    }

    public FormDefinition getFormDefinition(Class<?> tClass) {

        FormDefinition definition = this.fds.get(tClass);

        if (definition == null) {
            throw new IllegalArgumentException("未注册表单: " + tClass.getName());
        }

        return definition;
    }

    @SneakyThrows
    public <T> T compareObjectFieldAndUpdate(T target, T newOne) {

        Class<?> targetClass = target.getClass();

        List<Field> fields = getAllFields(targetClass);

        boolean updated = false;

        Map<Field, Object> updatedFields = new HashMap<>();

        for (Field field : fields) {
            field.setAccessible(true);
            // 获取字段的旧值和新值
            Object oldValue = field.get(target);
            Object newValue = field.get(newOne);

            // 对比旧值和新值
            if (oldValue == null && newValue != null || (oldValue != null && !oldValue.equals(newValue))) {
                updatedFields.put(field, newValue);
            }
        }

        if (updatedFields.size() > 0) {

            for (Map.Entry<Field, Object> entry : updatedFields.entrySet()) {
                Field f = entry.getKey();
                Object v = entry.getValue();
                f.set(target, v);
            }

            return target;
        }

        return null;

    }

    public static List<Field> getAllFields(Class<?> clazz) {

        List<Field> fields = clsFieldCache.get(clazz);

        if (fields != null && fields.size() > 0) {
            return fields;
        }

        fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) { // 递归获取父类的属性

            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));

            clazz = clazz.getSuperclass(); // 获取父类
        }

        clsFieldCache.put(clazz, fields);

        return fields;
    }

    public static List<Method> getAllMethod(Class<?> clazz) {

        List<Method> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) { // 递归获取父类的方法

            fields.addAll(Arrays.asList(clazz.getDeclaredMethods()));

            clazz = clazz.getSuperclass(); // 获取父类
        }

        return fields;
    }

}
