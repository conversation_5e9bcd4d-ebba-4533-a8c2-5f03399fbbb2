package com.astenamic.new_discovery.form;

import com.astenamic.new_discovery.form.manage.FormManager;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FormDateList<PERSON>ield extends FormBaseTypeField {

    private final DateTimeFormatter formatter;

    public FormDateListField(Field field, String code, DateTimeFormatter formatter) {
        super(field, code);
        this.formatter = formatter;
    }

    @Override
    public Object getValue(Object object, FormManager formManager) {

        Object value = super.getValue(object, formManager);

        if (value == null) {
            return null;
        }

        if (!(value instanceof List)) {
            return null;
        }

        List<?> list = (List<?>) value;
        List<Object> result = new ArrayList<>();

        for (Object item : list) {
            LocalDateTime time = null;

            if (item instanceof Date date) {
                time = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            }

            if (item instanceof LocalDateTime date) {
                time = date;
            }

            if (time != null) {
                if (this.formatter == null) {
                    result.add(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                } else {
                    result.add(time.format(this.formatter));
                }
            }
        }

        return result;
    }
}
