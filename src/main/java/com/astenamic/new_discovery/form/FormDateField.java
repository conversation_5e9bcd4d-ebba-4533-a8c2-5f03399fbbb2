package com.astenamic.new_discovery.form;

import com.astenamic.new_discovery.form.manage.FormManager;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class Form<PERSON>ate<PERSON>ield extends FormBaseTypeField {
    private final DateTimeFormatter formatter;

    public FormDateField(Field field, String code, DateTimeFormatter formatter) {
        super(field, code);
        this.formatter = formatter;
    }

    @Override
    public Object getValue(Object object, FormManager formManager) {

        Object value = super.getValue(object, formManager);

        LocalDateTime time = null;

        if (value instanceof Date date) {
            time = (date).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }

        if (value instanceof LocalDateTime date) {
            time = date;
        }

        if(time == null){
            return  null;
        }

        if(this.formatter == null){
            return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }else {
            return time.format(this.formatter);
        }

    }
}
