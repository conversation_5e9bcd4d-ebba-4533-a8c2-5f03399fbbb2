package com.astenamic.new_discovery.form.scan;


import com.astenamic.new_discovery.form.*;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.yida.modal.YidaComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class FormEntityScanner {
    private final String scanPath;

    public FormEntityScanner(String scanPath) {
        this.scanPath = StringUtils.isBlank(scanPath) ? "*" : scanPath;
    }

    public Map<Class<?>, FormDefinition> scan() {

        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        // 设置过滤条件：只包括继承了 BizObject 接口的类
        scanner.addIncludeFilter(new AnnotationTypeFilter(FormEntity.class));

        Set<BeanDefinition> bds = scanner.findCandidateComponents(this.scanPath);

        if (bds.isEmpty()) {
            return null;
        }

        Set<Class<?>> formClasses = new HashSet<>();

        try {
            String clsName;

            for (BeanDefinition bd : bds) {

                clsName = bd.getBeanClassName();

                Class<?> c = Class.forName(clsName);

                formClasses.add(c);
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }

        if (formClasses.isEmpty()) {
            return null;
        }

        return this.resolve(formClasses);
    }


    private Map<Class<?>, FormDefinition> resolve(Collection<Class<?>> formClasses) {

        Map<Class<?>, FormDefinition> formDefinitionMap = new HashMap<>();

        for (Class<?> fc : formClasses) {

            Map<Class<?>, FormDefinition> m = this.resolve(fc, formDefinitionMap);

            if (m != null) {
                formDefinitionMap.putAll(m);
            }
        }

        return formDefinitionMap;
    }

    private Map<Class<?>, FormDefinition> resolve(Class<?> fc, Map<Class<?>, FormDefinition> defs) {

        if (fc == Object.class) {
            return null;
        }

        FormEntity formEntity = fc.getAnnotation(FormEntity.class);

        if (formEntity == null) {
            return null;
        }

        String formCode = formEntity.value();

        if (StringUtils.isBlank(formCode)) {
            return null;
        }

        String appTypeCode = formEntity.appType();

        if (StringUtils.isBlank(appTypeCode)) {
            appTypeCode = null;
        }

        String sysToken = formEntity.sysToken();

        if (StringUtils.isBlank(sysToken)) {
            sysToken = null;
        }

        List<Field> fields = FormManager.getAllFields(fc);
        List<Method> methods = FormManager.getAllMethod(fc);

        List<FormBaseTypeField> fs = extractSchemaField(fields, defs);
        List<FormFieldGetterDefinition> fgs = extractSchemaGetter(methods);

        if (fs.isEmpty() && fgs.isEmpty()) {
            return null;
        }

        List<FormProperty> schemaProperties = new ArrayList<>(fs);
        schemaProperties.addAll(fgs);

        return Map.of(fc, new FormDefinition(formCode, appTypeCode, sysToken, schemaProperties));
    }

    private List<FormFieldGetterDefinition> extractSchemaGetter(List<Method> methods) {

        List<FormFieldGetterDefinition> schemaGetters = new ArrayList<>();

        FormField formField;

        String fcode;

        for (Method m : methods) {

            formField = m.getAnnotation(FormField.class);

            if (formField == null) {
                continue;
            }

            fcode = formField.value();

            if (StringUtils.isBlank(fcode)) {
                continue;
            }

            schemaGetters.add(new FormFieldGetterDefinition(m, fcode));

        }

        return schemaGetters;
    }

    private List<FormBaseTypeField> extractSchemaField(List<Field> fields, Map<Class<?>, FormDefinition> defs) {

        List<FormBaseTypeField> schemaFields = new ArrayList<>();

        FormField formField;

        String fcode;

        for (Field f : fields) {

            formField = f.getAnnotation(FormField.class);

            if (formField == null) {
                continue;
            }

            fcode = formField.value();

            if (StringUtils.isBlank(fcode)) {
                continue;
            }

            FormBaseTypeField fd;

            if (FormProperty.isDate(f)) {

                String pattern = formField.pattern();

                DateTimeFormatter formatter = null;

                if (StringUtils.isNotBlank(pattern)) {
                    formatter = DateTimeFormatter.ofPattern(pattern);
                }

                fd = new FormDateField(f, fcode, formatter);

            } else if (FormProperty.isBaseType(f)) {

                fd = new FormBaseTypeField(f, fcode);

            } else if (FormProperty.isDateList(f)) {

                String pattern = formField.pattern();

                DateTimeFormatter formatter = null;

                if (StringUtils.isNotBlank(pattern)) {
                    formatter = DateTimeFormatter.ofPattern(pattern);
                }

                fd = new FormDateListField(f, fcode, formatter);

            } else if (FormProperty.isArray(f)) {

                Class<?> genericType = (Class<?>) ((ParameterizedType) f.getGenericType()).getActualTypeArguments()[0];

                if (String.class.isAssignableFrom(genericType) || Number.class.isAssignableFrom(genericType)) {
                    fd = new FormBaseTypeField(f, fcode);
                } else {

                    if (!YidaComponent.class.isAssignableFrom(genericType)) {
                        Map<Class<?>, FormDefinition> def = this.resolve(genericType, defs);

                        if (def == null) {
                            throw new IllegalArgumentException("内联子表[" + genericType.getSimpleName() + "]需添加注解FormEntity");
                        }

                        defs.putAll(def);
                    }

                    fd = new FormEmbeddedObjectList(f, fcode, genericType);

                }

            } else if (FormProperty.isObject(f)) {

                Map<Class<?>, FormDefinition> def = this.resolve(f.getType(), defs);
                // 说明不是外联子表
                if (def == null) {

                    fd = new FormBaseTypeField(f, fcode);

                } else {
                    throw new IllegalArgumentException("暂时不支持外联子表");
                }
            } else {
                throw new IllegalArgumentException("未知属性类型：" + f.getType().getName());
            }

            schemaFields.add(fd);

        }

        return schemaFields;
    }


}
