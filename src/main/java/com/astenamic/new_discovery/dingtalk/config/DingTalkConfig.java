package com.astenamic.new_discovery.dingtalk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Data
@Component
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {

    private Map<String, BotConfig> bots = new HashMap<>();

    @Data
    public static class BotConfig {
        private String accessToken;
        private String secret;
    }

    // 获取指定机器人配置
    public BotConfig getBotConfig(String botKey) {
        return bots.get(botKey);
    }
}