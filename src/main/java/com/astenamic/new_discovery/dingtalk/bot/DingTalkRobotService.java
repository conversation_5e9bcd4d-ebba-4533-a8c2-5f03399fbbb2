package com.astenamic.new_discovery.dingtalk.bot;

import com.astenamic.new_discovery.dingtalk.config.DingTalkConfig;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

@Service
public class DingTalkRobotService {

    private static final String DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=";

    private final DingTalkConfig dingTalkConfig;

    public DingTalkRobotService(DingTalkConfig dingTalkConfig) {
        this.dingTalkConfig = dingTalkConfig;
    }

    /**
     * 发送文本消息
     *
     * @param botKey    机器人标识（对应 application.yml 中的 key）
     * @param content   消息内容
     * @param atMobiles 需要 @ 的手机号列表（可为 null 或空列表）
     * @param isAtAll   是否 @ 所有人
     */
    public void sendTextMessage(String botKey, String content, List<String> atMobiles, boolean isAtAll) {
        DefaultDingTalkClient client = createClient(botKey);

        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(content);
        request.setText(text);

        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(atMobiles);
        at.setIsAtAll(isAtAll);
        request.setAt(at);

        try {
            OapiRobotSendResponse response = client.execute(request);
            if (response.getErrcode() != 0) {
                throw new RuntimeException("钉钉消息发送失败：" + response.getErrmsg());
            }
        } catch (Exception e) {
            throw new RuntimeException("发送钉钉文本消息异常", e);
        }
    }

    /**
     * 发送 Markdown 消息
     *
     * @param botKey      机器人标识（对应 application.yml 中的 key）
     * @param title       消息标题
     * @param textContent 消息正文，支持 Markdown 格式
     * @param atMobiles   需要 @ 的手机号列表（可为 null 或空列表）
     * @param isAtAll     是否 @ 所有人
     */
    public void sendMarkdownMessage(String botKey, String title, String textContent, List<String> atMobiles, boolean isAtAll) {
        DefaultDingTalkClient client = createClient(botKey);

        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(title);
        markdown.setText(textContent);
        request.setMarkdown(markdown);

        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(atMobiles);
        at.setIsAtAll(isAtAll);
        request.setAt(at);

        try {
            OapiRobotSendResponse response = client.execute(request);
            if (response.getErrcode() != 0) {
                throw new RuntimeException("钉钉消息发送失败：" + response.getErrmsg());
            }
        } catch (Exception e) {
            throw new RuntimeException("发送钉钉 Markdown 消息异常", e);
        }
    }

    private DefaultDingTalkClient createClient(String botKey) {
        DingTalkConfig.BotConfig botConfig = dingTalkConfig.getBotConfig(botKey);
        if (botConfig == null) {
            throw new IllegalArgumentException("不存在的机器人配置： " + botKey);
        }
        String url = buildUrl(botConfig);
        return new DefaultDingTalkClient(url);
    }

    private String buildUrl(DingTalkConfig.BotConfig botConfig) {
        String url = DINGTALK_URL + botConfig.getAccessToken();
        if (botConfig.getSecret() != null && !botConfig.getSecret().trim().isEmpty()) {
            long timestamp = System.currentTimeMillis();
            String sign = generateSign(timestamp, botConfig.getSecret());
            url += "&timestamp=" + timestamp + "&sign=" + sign;
        }
        return url;
    }

    private String generateSign(long timestamp, String secret) {
        try {
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return URLEncoder.encode(Base64.getEncoder().encodeToString(signData), "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }
}