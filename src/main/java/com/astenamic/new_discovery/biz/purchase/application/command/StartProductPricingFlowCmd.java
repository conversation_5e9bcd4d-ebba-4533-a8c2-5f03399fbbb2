package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.StartProductPricingFlowReq;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductItem;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPriceCache;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.DispatchOutSummaryDTO;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.StockSummaryDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 开始产品定价流程命令
 * 1. 根据产品分类或定价信息，启动定价流程
 * 2. 如果产品没有分类但有定价信息，则将其归为“其他”分类
 * 3. 更新产品定价状态为“已定价”
 */
@Component
@RequiredArgsConstructor
public class StartProductPricingFlowCmd implements
        PricingCommand<StartProductPricingFlowReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(StartProductPricingFlowCmd.class);

    private final ProductPricingContext ctx;

    @Override
    public Void execute(StartProductPricingFlowReq req) {

        List<String> exCats = List.of("其他", "新发现、烤匠餐具评审","蝴蝶里、四季风情餐具评审", "调味品招标");

        /* ---------- 分类方式一：有分类 ---------- */
        List<String> cats = ctx.getProductPriceRepository().findCategoryWithinNext30Days();
        for (String cat : cats) {
            if (StringUtils.isBlank(cat) || exCats.contains(cat)) continue;
            List<ProductInfo> infos = ctx.getProductInfoService().getProductInfoByCategory(cat).stream()
                    .filter(i -> !"否".equals(i.getIsFrozen()))
                    .toList();
            if (infos.isEmpty()) {
                logger.warn("没有找到分类下的产品信息, category={}", cat);
                continue;
            }
            List<String> nos = infos.stream().map(ProductInfo::getSno).toList();
            List<ProductPrice> prices = ctx.getProductPriceRepository().findByGoodSnoIn(nos);
            startFlow(cat, infos, prices);
        }

        /* ---------- 分类方式二：无分类但有定价 ---------- */
//        List<ProductPrice> prices = ctx.getProductPriceRepository().findRecentWithin30Days();
//        if (!prices.isEmpty()) {
//            List<ProductInfo> infos = ctx.getProductInfoService().getProductInfoByCategory("")
//                    .stream()
//                    .filter(i -> prices.stream().map(ProductPrice::getGoodSno)
//                            .anyMatch(s -> s.equals(i.getSno()))
//                            && StringUtils.isBlank(i.getGoodPricingCategory()))
//                    .toList();
//            if (!infos.isEmpty()) startFlow("", infos, prices);
//        }
        return null;
    }

    public void startFlow(String cat) {
        List<ProductInfo> infos = ctx.getProductInfoService().getProductInfoByCategory(cat).stream()
                .filter(i -> !"否".equals(i.getIsFrozen()))
                .toList();
        List<String> nos = infos.stream().map(ProductInfo::getSno).toList();
        List<ProductPrice> prices = ctx.getProductPriceRepository().findByGoodSnoIn(nos);
        startFlow(cat, infos, prices);
    }


    /**
     * 启动产品定价流程
     *
     * @param category 产品分类
     * @param infos    产品信息列表
     * @param prices   产品定价列表
     */
    private void startFlow(String category,
                           List<ProductInfo> infos,
                           List<ProductPrice> prices) {

        List<ProductItem> items = new ArrayList<>();
        for (ProductInfo info : infos) {
            List<ProductPrice> pp = prices.stream()
                    .filter(p -> p.getGoodSno().equals(info.getSno()))
                    .toList();
            ProductItem pi = new ProductItem();
            if (!pp.isEmpty()) {
                ProductPrice sel = pp.stream()
                        .filter(p -> p.getAtime() != null).max(Comparator
                                .comparing(ProductPrice::getEnddate))                                          // 取第 1 条
                        .orElse(null);
                if (sel == null) {
                    logger.warn("没有找到定价信息, productCode={}", info.getSno());
                    continue;
                }
                if (StringUtils.isNotBlank(sel.getLastyprice())) {
                    pi.setLastYearAvgPrice(Float.parseFloat(sel.getLastyprice()));
                }
                pi.setCurrentPrice(sel.getUprice());
                pi.setEffectiveDate(sel.getStartdate().atStartOfDay().plusDays(1));
                pi.setEndDate(sel.getEnddate().atStartOfDay().plusDays(1));
                if (StringUtils.isNotBlank(sel.getApplyamount())) {
                    pi.setLastMonthPurchaseQuantity(Float.parseFloat(sel.getApplyamount()));
                }
            }
            pi.setOrderUnit(info.getApplyunit_name());
            pi.setItemSubcategory(info.getGoodtype_name());
            pi.setItemCategory(info.getFgoodtype_name());
            pi.setItemCode(info.getSno());
            pi.setItemSpec(info.getStd());
            pi.setItemName(info.getName());

            try {
                /* 月用量：允许 qty 为 null；单位统一保留两位小数 */
                BigDecimal monthly = Optional
                        .ofNullable(ctx.getDailyDispatchProductSummaryRepository()
                                .findMaterialDispatchSummary(info.getSno()))
                        .map(DispatchOutSummaryDTO::getQty)
                        .map(q -> q.setScale(2, RoundingMode.HALF_UP))
                        .orElse(BigDecimal.ZERO);
                pi.setMonthlyUsage(monthly.floatValue());
            } catch (Exception ex) {
                logger.warn("查询月用量/库存异常, productCode={}: {}", info.getSno(), ex.getMessage());
                pi.setMonthlyUsage(0f);
            }

            try {
                /* 库存：允许整条记录或 stock 字段为 null */
                Float stock = Optional
                        .ofNullable(ctx.getMaterialStockRepository()
                                .findStockSummaryByMaterial(info.getSno()))
                        .map(StockSummaryDTO::getStock)
                        .orElse(0f);
                pi.setStock(stock);
            } catch (Exception e) {
                logger.error("处理产品信息异常, productCode={}: {}", info.getSno(), e.getMessage());
                pi.setStock(0f);
            }
            items.add(pi);
        }

        ProductPricingFlow flow = new ProductPricingFlow();
        flow.setApplicationDate(LocalDateTime.now());
        flow.setPricingType(PricingType.CENT_PROCUREMENT.getDesc());
        flow.setProductTable(items);
        flow.setProductCategory(category);
        flow.setViewButtonText("否");

        String instanceId = ctx.getYiDaSession().processSave(flow, ProductPricingFlow.class, "",
                ctx.getYidaConfigProperties().getYidaBot().getFlowBot());

        /* 更新缓存/状态 */
        List<ProductPriceCache> caches = new ArrayList<>();
        long ts = System.currentTimeMillis();
        prices.forEach(p -> {
            p.setEnabled("2");
            ProductPriceCache c = new ProductPriceCache();
            BeanUtils.copyProperties(p, c);
            c.setId(null);
            c.setOid(p.getId());
            c.setPricingDate(ts);
            c.setInstanceId(instanceId);
            caches.add(c);
        });
        ctx.getProductPriceRepository().saveAll(prices);
        ctx.getProductPriceCacheRepository().saveAll(caches);
    }
}
