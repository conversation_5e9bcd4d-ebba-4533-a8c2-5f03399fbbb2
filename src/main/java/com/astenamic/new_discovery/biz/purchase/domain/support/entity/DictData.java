package com.astenamic.new_discovery.biz.purchase.domain.support.entity;


import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity("tableField_manjqy6x")
public class DictData {

    /**
     * 字典编码
     */
    @FormField("textField_manjqy71")
    private String dictCode;

    /**
     * 字典名称
     */
    @FormField("textField_manjqy72")
    private String dictLabel;

    /**
     * 字典值
     */
    @FormField("textField_manjqy73")
    private String dictValue;


    /**
     * 状态
     */
    @FormField("radioField_manjqy74")
    private String status;

}
