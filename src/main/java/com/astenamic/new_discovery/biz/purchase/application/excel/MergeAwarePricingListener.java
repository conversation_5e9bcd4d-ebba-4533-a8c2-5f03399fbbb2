package com.astenamic.new_discovery.biz.purchase.application.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.Head;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateDTO;
import lombok.RequiredArgsConstructor;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 监听器：仅针对“价格开始日期”“价格结束日期”列，
 * 若该列存在纵向合并，则以首格为准回填整块。
 */
@RequiredArgsConstructor
public class MergeAwarePricingListener
        extends AnalysisEventListener<PricingReviewTemplateDTO> {

    /**
     * 表头占用行数
     */
    private final int headRows;

    /**
     * 判定 footer 的关键字（出现即视为底部区起点，如 "说明"）
     */
    private final String footerFlag;

    /* ---------------- 运行时状态 ---------------- */
    private final List<PricingReviewTemplateDTO> cache = new ArrayList<>();
    private final Map<Integer, List<int[]>> verticalMerges = new HashMap<>();
    private Map<Integer, Field> colFieldMap;     // 列索引 → DTO 字段
    private final Set<Integer> targetCols = new HashSet<>(); // 仅处理的列索引
    private int footerStart = -1;                // <0 未遇到 footer

    /* ---------------- 行数据回调 ---------------- */
    @Override
    public void invoke(PricingReviewTemplateDTO row, AnalysisContext ctx) {

        /* 首行进来时初始化列映射 & 目标列集合 */
        if (colFieldMap == null) {
            colFieldMap = new HashMap<>();
            Map<Integer, Head> headMap =
                    ctx.readSheetHolder()
                            .getExcelReadHeadProperty()
                            .getHeadMap();

            headMap.forEach((col, head) -> {
                Field f = head.getField();       // DTO 未映射时返回 null
                if (f != null) {
                    f.setAccessible(true);
                    colFieldMap.put(col, f);

                    /* 收集“价格开始日期”“价格结束日期”两列索引 */
                    String name = f.getName();
                    if ("priceStartDate".equals(name) || "priceEndDate".equals(name) || "supplierCode".equals(name) || "supplierName".equals(name) || "secondQuotation".equals(name)) {
                        targetCols.add(col);
                    }
                }
            });
        }

        /* 判定 footer 起点（遇到“说明”即进入 footer 区） */
        if (footerStart < 0 &&
                (footerFlag.equals(row.getObjectId()) || footerFlag.equals(row.getProductName()))) {
            footerStart = cache.size();          // 当前行及以后都是 footer
        }

        cache.add(row);
    }

    /* ---------------- 合并信息回调 ---------------- */
    @Override
    public void extra(CellExtra extra, AnalysisContext ctx) {
        if (extra.getType() != CellExtraTypeEnum.MERGE) return;

        boolean vertical = extra.getFirstColumnIndex() == extra.getLastColumnIndex();
        boolean inBody = extra.getLastRowIndex() >= headRows &&
                (footerStart < 0 || extra.getFirstRowIndex() < footerStart + headRows);
        boolean wanted = targetCols.contains(extra.getFirstColumnIndex());

        if (!(vertical && inBody && wanted)) return;

        verticalMerges
                .computeIfAbsent(extra.getFirstColumnIndex(), k -> new ArrayList<>())
                .add(new int[]{extra.getFirstRowIndex(), extra.getLastRowIndex()});
    }

    /* ---------------- 全部读取完毕：回填 ---------------- */
    @Override
    public void doAfterAllAnalysed(AnalysisContext ctx) {

        int bodyEndCacheIdx = (footerStart < 0 ? cache.size() : footerStart) - 1;

        verticalMerges.forEach((col, ranges) -> ranges.forEach(r -> {

            int startRow = Math.max(r[0], headRows);               // 落到正文
            int endRow = Math.min(r[1], bodyEndCacheIdx + headRows);
            if (startRow >= endRow) return;                        // 单行合并，无需回填

            int start = startRow - headRows;                       // cache 索引
            int end = endRow - headRows;

            String official = getVal(cache.get(start), col);       // 首格
            for (int i = start + 1; i <= end; i++) {               // 逐行回填
                setVal(cache.get(i), col, official);
            }
        }));
    }

    /* ---------------- 反射读写工具 ---------------- */
    private String getVal(PricingReviewTemplateDTO dto, int col) {
        try {
            Field f = colFieldMap.get(col);
            if (f == null) return null;
            Object v = f.get(dto);
            return v == null ? null : v.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private void setVal(PricingReviewTemplateDTO dto, int col, String v) {
        if (v == null || v.isBlank()) return;
        try {
            Field f = colFieldMap.get(col);
            if (f == null) return;
            Class<?> t = f.getType();
            if (t == String.class) f.set(dto, v);
            else if (t == Float.class) f.set(dto, Float.valueOf(v));
        } catch (Exception ignored) {
        }
    }

    /* ---------------- 提供给业务层的数据 ---------------- */
    public List<PricingReviewTemplateDTO> getData() {
        return footerStart < 0 ? cache : cache.subList(0, footerStart);
    }
}
