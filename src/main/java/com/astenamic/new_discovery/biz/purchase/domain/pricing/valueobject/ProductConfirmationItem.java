package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货品确认表-行信息
 */
@Data
@FormEntity("tableField_maan4f6f")
public  class ProductConfirmationItem {
    /**
     * 去年同期平均单价
     */
    @FormField("numberField_maan4f6e")
    private Float lastYearAvgPrice;

    /**
     * 当前执行价格
     */
    @FormField("numberField_maan4f6d")
    private Float currentPrice;

    /**
     * 定价生效时间
     */
    @FormField("dateField_maan4f6a")
    private LocalDateTime effectiveDate;

    /**
     * 定价结束时间
     */
    @FormField("dateField_maan4f6b")
    private LocalDateTime endDate;

    /**
     * 上月采购数量
     */
    @FormField("numberField_maan4f6c")
    private Float lastMonthPurchaseQuantity;

    /**
     * 订货单位
     */
    @FormField("selectField_maan4f69")
    private String orderUnit;

    /**
     * 货品小类
     */
    @FormField("selectField_maeogsla")
    private String itemSubcategory;

    /**
     * 货品大类
     */
    @FormField("selectField_maeogsl8")
    private String itemCategory;

    /**
     * 货品编码
     */
    @FormField("textField_maan4f66")
    private String itemCode;

    /**
     * 货品规格
     */
    @FormField("textField_maan4f65")
    private String itemSpec;

    /**
     * 货品名称
     */
    @FormField("textField_maan4f64")
    private String itemName;

    /**
     * 月用量
     */
    @FormField("numberField_mbh9o4ep")
    private Float monthlyUsage;

    /**
     * 当前库存
     */
    @FormField("numberField_mbh9o4eo")
    private Float stock;
}
