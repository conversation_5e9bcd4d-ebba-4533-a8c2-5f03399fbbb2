package com.astenamic.new_discovery.biz.purchase.domain.newproduct.service;


import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SourceAuthentication;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 寻源认证流程服务
 */
@Slf4j
@Service
public class SourceAuthenticationService extends AbstractFormService<SourceAuthentication> {

    public SourceAuthenticationService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(SourceAuthentication.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据需求单号查询所有寻源认证表单
     *
     * @param requirementCode 需求单号
     * @return 寻源认证列表
     */
    public List<SourceAuthentication> getByRequirementCode(String requirementCode) {
        SearchConditions cond = SearchCondition.builder()
                .textEq("textField_m9jm98tr", requirementCode, "+")
                .get();
        return super.getFormsByCond(cond);
    }
}

