package com.astenamic.new_discovery.biz.purchase.domain.support.enums;

import lombok.Getter;

/**
 * 供应商类型枚举
 */
@Getter
public enum SupplierType {

    /** 1 – 外部供应商 */
    EXTERNAL(1, "外部供应商"),

    /** 2 – 内部供应商 */
    INTERNAL(2, "内部供应商");

    // ===== 字段 =====
    private final int code;
    private final String desc;

    SupplierType(int code, String desc) {
        this.code  = code;
        this.desc  = desc;
    }

    // ===== 根据 code 反查 =====
    public static SupplierType of(int code) {
        for (SupplierType t : values()) {
            if (t.code == code) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知供应商类型 code = " + code);
    }

    // ===== 根据 desc 反查 =====
    public static SupplierType ofDesc(String desc) {
        for (SupplierType t : values()) {
            if (t.desc.equals(desc)) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知供应商类型 desc = " + desc);
    }
}
