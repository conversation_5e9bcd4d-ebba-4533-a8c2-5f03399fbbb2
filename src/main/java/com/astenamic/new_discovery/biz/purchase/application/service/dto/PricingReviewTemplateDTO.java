package com.astenamic.new_discovery.biz.purchase.application.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 导出定价评审表 DTO
 */

@Data
public class PricingReviewTemplateDTO {

    @ExcelProperty("id")
    private String objectId;

    @ExcelProperty("货品名称")
    private String productName;

    private String productCode;

    @ExcelProperty("月用量")
    private Float monthlyUsage;

    @ExcelProperty("库存")
    private Float stock;

    @ExcelProperty("当前价格")
    private Float historicalPurchasePrice;

    @ExcelProperty("当前价格到期时间")
    private String lastEndDate;

    @ExcelProperty("预计月采购额")
    private Float estimatedMonthlyPurchase;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("供应商编码")
    private String supplierCode;

    @ExcelProperty("供应商联系方式")
    private String supplierContact;

    @ExcelProperty("厂号及规格")
    private String factorySpec;

    @ExcelProperty("样品测试表")
    private String sampleTestForms;

    @ExcelProperty("初次报价")
    private Float price;

    @ExcelProperty("二次报价")
    private Float secondQuotation;

    @ExcelProperty("发票类型")
    private String invoiceType;

    @ExcelProperty("付款方式")
    private String paymentMethod;

    @ExcelProperty("本次采购与历史价格比的降低金额")
    private Float reductionAmount;

    @ExcelProperty("最终定价")
    private Float finalPrice;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("本次叫货量")
    private String currentCallQuantity;

    @ExcelProperty("是否启用")
    private String enabled;

    @ExcelProperty("执行门店")
    private String executeStore;

    @ExcelProperty("价格开始日期")
    private String priceStartDate;

    @ExcelProperty("价格结束日期")
    private String priceEndDate;


    public Float parseFloat(Map<Integer, String> row, int idx) {
        String v = row.get(idx);
        if (v != null && !v.trim().isEmpty()) {
            try {
                return Float.valueOf(v.trim());
            } catch (Exception ignored) {
            }
        }
        return null;
    }
}
