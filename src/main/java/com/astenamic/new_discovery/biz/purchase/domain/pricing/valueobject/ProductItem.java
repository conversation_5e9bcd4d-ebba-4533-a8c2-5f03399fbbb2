package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货品表-行信息
 */
@Data
@FormEntity("tableField_m52hzd3h")
public  class ProductItem {
    /**
     * 去年同期平均单价
     */
    @FormField("numberField_m9kn3ybm")
    private Float lastYearAvgPrice;

    /**
     * 当前执行价格
     */
    @FormField("numberField_m9kn3ybg")
    private Float currentPrice;

    /**
     * 定价生效时间
     */
    @FormField("dateField_m9yxto45")
    private LocalDateTime effectiveDate;

    /**
     * 定价结束时间
     */
    @FormField("dateField_m9yxto46")
    private LocalDateTime endDate;

    /**
     * 上月采购数量
     */
    @FormField("numberField_m9kn3ybc")
    private Float lastMonthPurchaseQuantity;

    /**
     * 订货单位
     */
    @FormField("selectField_ma1yzszv")
    private String orderUnit;

    /**
     * 货品小类
     */
    @FormField("selectField_maeogsl6")
    private String itemSubcategory;

    /**
     * 货品大类
     */
    @FormField("selectField_maeogsl4")
    private String itemCategory;

    /**
     * 货品编码
     */
    @FormField("textField_m9kn3yb0")
    private String itemCode;

    /**
     * 货品规格
     */
    @FormField("textField_m9kn3yaz")
    private String itemSpec;

    /**
     * 货品名称
     */
    @FormField("textField_m9kn3yay")
    private String itemName;

    /**
     * 月用量
     */
    @FormField("numberField_mbh9o4ek")
    private Float monthlyUsage;

    /**
     * 当前库存
     */
    @FormField("numberField_mbh9o4el")
    private Float stock;
}
