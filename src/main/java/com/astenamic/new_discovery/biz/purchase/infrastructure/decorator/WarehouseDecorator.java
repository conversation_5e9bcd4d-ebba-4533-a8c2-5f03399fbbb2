package com.astenamic.new_discovery.biz.purchase.infrastructure.decorator;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ShopInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.service.ShopInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class WarehouseDecorator{

    private final ShopInfoService shopInfoService;

    public List<PricingReview> decorate(List<PricingReview> reviews) {
        List<ShopInfo> warehouses = shopInfoService.getWarehouses();
        return reviews.stream()
                .flatMap(r -> warehouses.stream().map(w -> r.withExecution(w.getSno(), w.getName())))
                .toList();
    }

}
