package com.astenamic.new_discovery.biz.purchase.application.command;


import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.StartQuotationReq;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SourceAuthentication;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductSupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.SupplierQuoteItem;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPriceCache;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierInfo;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.form.FormDefinition;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component
@RequiredArgsConstructor
public class StartQuotationFlowCmd implements PricingCommand<StartQuotationReq, Void> {

    private static final Logger log = LoggerFactory.getLogger(StartSupplierQuotationCmd.class);

    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "开始货品供应商报价流程", atMobiles = {"19353308172"})
    public Void execute(StartQuotationReq req) {

        String pricingOrderNumber = req.pricingOrderNumber();

        /* 1. 主流程、产品确认单 */
        ProductPricingFlow flow = ctx.getProductPricingFlowService()
                .getByPricingOrderNumber(pricingOrderNumber);
        if (flow == null) throw new BizException("未找到定价流程");

        List<ProductConfirmationItem> confirmations = ctx.getProductPricingFlowService()
                .queryProductConfirmationList(flow.getInstanceId());

        if (confirmations.isEmpty())
            throw new BizException("定价流程中没有产品确认单：" + pricingOrderNumber);

        List<ProductSupplierQuotationFlow> flows = new ArrayList<>();

        if (StringUtils.equals(flow.getPricingType(), PricingType.NEW_PRODUCT.getDesc())) {
            log.info("新品定价流程");
            List<SourceAuthentication> authentications = ctx.getSourceAuthService().getByRequirementCode(flow.getRequirementCode())
                    .stream()
                    .filter(a -> "审批通过".equals(a.getStatus()))
                    .toList();
            Map<List<String>, List<SourceAuthentication>> grouped = authentications.stream()
                    .collect(Collectors.groupingBy(SourceAuthentication::getSourcingAgents));

            ProductConfirmationItem item = confirmations.get(0);
            for (Map.Entry<List<String>, List<SourceAuthentication>> entry : grouped.entrySet()) {
                List<String> sourcingAgents = entry.getKey();
                if (sourcingAgents == null || sourcingAgents.isEmpty()) {
                    log.warn("分组的寻源员为空，数据异常");
                    continue;
                }
                log.info("寻源员：{}", sourcingAgents);
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    List<SourceAuthentication> v = entry.getValue();
                    ProductSupplierQuotationFlow quotationFlow = new ProductSupplierQuotationFlow();
                    quotationFlow.setPricingFlowNumber(pricingOrderNumber);
                    quotationFlow.setProductCode(item.getItemCode());
                    quotationFlow.setProductName(item.getItemName());
                    quotationFlow.setProductSpec(item.getItemSpec());
                    quotationFlow.setOrderUnit(item.getOrderUnit());
                    quotationFlow.setSourcingAgents(sourcingAgents.get(0));
                    List<SupplierQuoteItem> quoteItems = v.stream().map(auth -> {
                        SupplierQuoteItem quoteItem = new SupplierQuoteItem();
                        SupplierInfo supplier = StringUtils.isBlank(auth.getSupplierCode()) ? null : ctx.getSupplierInfoService().getSupplierBySno(auth.getSupplierCode());
                        if (supplier != null) {
                            YidaAssociation supplierAssociation = buildAssociation(SupplierInfo.class, supplier.getObjectId(), supplier.getName(), supplier.getSno());
                            quoteItem.setSupplierInfo(List.of(supplierAssociation));
                            quoteItem.setSupplierName(supplier.getName());
                            quoteItem.setSupplierCode(supplier.getSno());
                            quoteItem.setSupplierContact(supplier.getMobile());
                            quoteItem.setInvoiceType(supplier.getInvoicetype());
                            quoteItem.setSettlementMethod(supplier.getAccountdatetype());
                        } else {
                            quoteItem.setSupplierName(auth.getNewSupplierName());
                            quoteItem.setSupplierCode(auth.getSupplierCode());
                            quoteItem.setSupplierContact(auth.getSupplierContact());
                            quoteItem.setInvoiceType(auth.getInvoiceType());
                            quoteItem.setSettlementMethod(auth.getSettlementMethod());
                        }
                        return quoteItem;
                    }).toList();
                    quotationFlow.setSupplierQuoteTable(quoteItems);
                    flows.add(quotationFlow);
                }
            }
        } else if (StringUtils.equals(flow.getPricingType(), PricingType.CENT_PROCUREMENT.getDesc())) {
            log.info("周期性集采定价流程");
            /* 2. 查询本次定价的供应商 */
            Map<String, List<ProductPriceCache>> goodSnoMap = Optional.ofNullable(
                            ctx.getProductPriceCacheRepository().findByInstanceId(flow.getInstanceId()))
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(p -> StringUtils.isNotBlank(p.getSupplierSno()) && StringUtils.isNotBlank(p.getGoodSno()) && p.getEnddate() != null)
                    .collect(Collectors.toMap(
                            p -> p.getGoodSno() + "_" + p.getSupplierSno(),
                            p -> p,
                            (p1, p2) -> p1.getEnddate().isAfter(p2.getEnddate()) ? p1 : p2
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.groupingBy(
                            p -> Optional.ofNullable(p.getGoodSno()).orElse("")
                    ));

            /* 3. 构建货品供应商报价单列表 */
            flows = confirmations.stream()
                    .map(item -> {
                        ProductSupplierQuotationFlow quotationFlow = new ProductSupplierQuotationFlow();
                        quotationFlow.setPricingFlowNumber(pricingOrderNumber);
                        quotationFlow.setProductCode(item.getItemCode());
                        quotationFlow.setProductName(item.getItemName());
                        quotationFlow.setProductSpec(item.getItemSpec());
                        quotationFlow.setOrderUnit(item.getOrderUnit());
                        if (flow.getSourcingAgents() != null && !flow.getSourcingAgents().isEmpty()) {
                            quotationFlow.setSourcingAgents(flow.getSourcingAgents().get(0));
                        }

                        List<SupplierQuoteItem> supplierQuoteItems = goodSnoMap.getOrDefault(item.getItemCode(), Collections.emptyList())
                                .stream()
                                .map(cache -> {
                                    SupplierQuoteItem quoteItem = new SupplierQuoteItem();
                                    try {
                                        SupplierInfo supplier = ctx.getSupplierInfoService().getSupplierBySno(cache.getSupplierSno());
                                        if (supplier != null) {
                                            YidaAssociation supplierAssociation = buildAssociation(SupplierInfo.class, supplier.getObjectId(), supplier.getName(), supplier.getSno());
                                            quoteItem.setSupplierInfo(List.of(supplierAssociation));
                                            quoteItem.setSupplierName(supplier.getName());
                                            quoteItem.setSupplierCode(supplier.getSno());
                                            quoteItem.setSupplierContact(supplier.getMobile());
                                            quoteItem.setInvoiceType(supplier.getInvoicetype());
                                            quoteItem.setSettlementMethod(supplier.getAccountdatetype());
                                        } else {
                                            quoteItem.setSupplierName(cache.getSupplierName());
                                            quoteItem.setSupplierCode(cache.getSupplierSno());
                                        }
                                        if (cache.getEnddate() != null) {
                                            quoteItem.setPriceStartDate(cache.getEnddate().atStartOfDay().plusDays(1));
                                        }
                                    } catch (Exception e) {
                                        log.error("组装报价行异常，供应商编码：{}", cache.getSupplierSno(), e);
                                        throw new BizException("组装报价行异常，供应商编码：" + cache.getSupplierSno());
                                    }
                                    return quoteItem;
                                })
                                .collect(Collectors.toList());

                        quotationFlow.setSupplierQuoteTable(supplierQuoteItems);
                        return quotationFlow;
                    })
                    .toList();
        } else {
            log.info("非新品或集采定价流程，暂不支持");
            throw new BizException("非新品或集采定价流程，暂不支持");
        }
        ctx.getProductSupplierQuotationFlowService().batchLaunchProcess(pricingOrderNumber, flows);
        return null;
    }

    private YidaAssociation buildAssociation(Class<?> entityClass, String instanceId, String title, String subTitle) {
        FormDefinition fd = ctx.getFormManager().getFormDefinition(entityClass);
        YidaAssociation association = new YidaAssociation();
        association.setAppType(fd.getAppType());
        association.setFormUuid(fd.getCode());
        association.setInstanceId(instanceId);
        association.setTitle(title);
        association.setSubTitle(subTitle);
        return association;

    }

}
