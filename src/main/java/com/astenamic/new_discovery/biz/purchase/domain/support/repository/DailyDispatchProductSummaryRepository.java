package com.astenamic.new_discovery.biz.purchase.domain.support.repository;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.DailyDispatchProductSummary;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.DispatchOutSummaryDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DailyDispatchProductSummaryRepository extends JpaRepository<DailyDispatchProductSummary, Long> {

    /**
     * 按配送中心编码、物料编码和单位统计配送数量
     *
     * @param companyCode 配送中心编码
     * @param materialNo  物料编码
     * @return 投影视图 DispatchOutSummaryDTO
     */
    @Query(value = """
                SELECT
                  slscode AS sls_code,
                  lgcode  AS lg_code,
                  dh_unit,
                  SUM(dh_qty) AS qty
                FROM public.xfx_lgt_dispatchout
                WHERE slscode = :companyCode
                  AND lgcode  = :materialNo
                  AND review_date >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY slscode, lgcode, dh_unit
            """, nativeQuery = true)
    DispatchOutSummaryDTO findDispatchOutDetail(
            @Param("companyCode") String companyCode,
            @Param("materialNo") String materialNo
    );

    /**
     * 仅按物料编码和单位统计 30 天内配送数量
     *
     * @param materialNo 物料编码
     * @return DispatchOutSummaryDTO
     */
    @Query(value = """
            SELECT
              lgcode AS lg_code,
              dh_unit,
              SUM(dh_qty) AS qty
            FROM public.xfx_lgt_dispatchout
            WHERE lgcode = :materialNo
              AND review_date >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY lgcode, dh_unit
            """, nativeQuery = true)
    DispatchOutSummaryDTO findMaterialDispatchSummary(
            @Param("materialNo") String materialNo);
}
