package com.astenamic.new_discovery.biz.purchase.application.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class StringToFloatConverter implements Converter<Float> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Float.class;
    }
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Float convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String val = cellData.getStringValue();
        if (val == null || val.trim().isEmpty() || "null".equalsIgnoreCase(val.trim())) {
            return null; // 或 0f，看你业务需求
        }
        try {
            return Float.valueOf(val);
        } catch (Exception e) {
            return null; // 或抛异常，看你需要
        }
    }
}

