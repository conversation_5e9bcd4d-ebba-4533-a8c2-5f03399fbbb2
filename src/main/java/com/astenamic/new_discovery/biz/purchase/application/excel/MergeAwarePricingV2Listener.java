package com.astenamic.new_discovery.biz.purchase.application.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.Head;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateDTO;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateV2DTO;
import lombok.RequiredArgsConstructor;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 监听器：仅针对“价格开始日期”“价格结束日期”列，
 * 若该列存在纵向合并，则以首格为准回填整块。
 */
@RequiredArgsConstructor
public class MergeAwarePricingV2Listener
        extends AnalysisEventListener<PricingReviewTemplateV2DTO.InputData> {

    /**
     * 表头占用行数
     */
    private final int headRows;

    /* ---------------- 运行时状态 ---------------- */
    private final List<PricingReviewTemplateV2DTO.InputData> cache = new ArrayList<>();

    private final Map<Integer, List<int[]>> verticalMerges = new HashMap<>();

    private Map<Integer, Field> colFieldMap;     // 列索引 → DTO 字段

    private final Set<Integer> targetCols = new HashSet<>(); // 仅处理的列索引

    /* ---------------- 行数据回调 ---------------- */
    @Override
    public void invoke(PricingReviewTemplateV2DTO.InputData row, AnalysisContext ctx) {

        /* 首行进来时初始化列映射 & 目标列集合 */
        if (colFieldMap == null) {
            colFieldMap = new HashMap<>();
            Map<Integer, Head> headMap =
                    ctx.readSheetHolder()
                            .getExcelReadHeadProperty()
                            .getHeadMap();

            headMap.forEach((col, head) -> {
                Field f = head.getField();
                if (f != null) {
                    f.setAccessible(true);
                    colFieldMap.put(col, f);

                    String name = f.getName();
                    if ("inputSupplierCode".equals(name) || "inputPriceStartDate".equals(name) || "inputPriceEndDate".equals(name)) {
                        targetCols.add(col);
                    }
                }
            });
        }
        cache.add(row);
    }

    /* ---------------- 合并信息回调 ---------------- */
    @Override
    public void extra(CellExtra extra, AnalysisContext ctx) {

        if (extra.getType() != CellExtraTypeEnum.MERGE) return;

        boolean vertical = extra.getFirstColumnIndex() == extra.getLastColumnIndex();
        boolean inBody = extra.getFirstRowIndex() >= headRows && extra.getLastRowIndex() >= headRows;
        boolean wanted = targetCols.contains(extra.getFirstColumnIndex());

        if (!(vertical && inBody && wanted)) return;

        verticalMerges
                .computeIfAbsent(extra.getFirstColumnIndex(), k -> new ArrayList<>())
                .add(new int[]{extra.getFirstRowIndex(), extra.getLastRowIndex()});
    }

    /* ---------------- 全部读取完毕：回填 ---------------- */
    @Override
    public void doAfterAllAnalysed(AnalysisContext ctx) {

        int bodyEndCacheIdx = cache.size() - 1;

        verticalMerges.forEach((col, ranges) -> ranges.forEach(r -> {

            int startRow = Math.max(r[0], headRows);               // 落到正文
            int endRow = Math.min(r[1], bodyEndCacheIdx + headRows);
            if (startRow >= endRow) return;                        // 单行合并，无需回填

            int start = startRow - headRows;                       // cache 索引
            int end = endRow - headRows;

            String official = getVal(cache.get(start), col);       // 首格
            for (int i = start + 1; i <= end; i++) {               // 逐行回填
                setVal(cache.get(i), col, official);
            }
        }));
    }

    /* ---------------- 反射读写工具 ---------------- */
    private String getVal(PricingReviewTemplateV2DTO.InputData dto, int col) {
        try {
            Field f = colFieldMap.get(col);
            if (f == null) return null;
            Object v = f.get(dto);
            return v == null ? null : v.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private void setVal(PricingReviewTemplateV2DTO.InputData dto, int col, String v) {
        try {
            Field f = colFieldMap.get(col);
            if (f == null) return;
            Class<?> t = f.getType();
            if (t == String.class) f.set(dto, v);
            else if (t == Float.class) f.set(dto, Float.valueOf(v));
        } catch (Exception ignored) {
        }
    }

    /* ---------------- 提供给业务层的数据 ---------------- */
    public List<PricingReviewTemplateV2DTO.InputData> getData() {
        return cache;
    }
}
