package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-87721344F91F40A4B5CCF2BCDB0DDAD5SQCT", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class DictType extends YidaObject {

    @FormField("textField_manjqy6v")
    private String dictName;

    @FormField("textField_manjqy6s")
    private String dictType;

    @FormField("radioField_manjqy6w")
    private String status;

    @FormField("textField_mankq2fp")
    private String remark;

    @FormField("tableField_manjqy6x")
    private List<DictData> dictDataList;


    public DictData getByDictValue(String dictValue) {
        if (dictDataList == null || dictValue == null) {
            return null;
        }
        return dictDataList.stream()
                .filter(data -> dictValue.equals(data.getDictValue()))
                .findFirst()
                .orElse(null);

    }

    public DictData getByDictLabel(String dictLabel) {
        if (dictDataList == null || dictLabel == null) {
            return null;
        }
        return dictDataList.stream()
                .filter(data -> dictLabel.equals(data.getDictLabel()))
                .findFirst()
                .orElse(null);
    }

}
