package com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 寻源认证表单
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-DD154C1EEAA14DF8B3EBBE6D8623C4B7Q9ZL", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class SourceAuthentication extends YidaObject {

    /**
     * 寻源认证编码
     */
    @FormField("serialNumberField_m9i557p3")
    private String sourcingAuthCode;

    /**
     * 需求单编码
     */
    @FormField("textField_m9jm98tr")
    private String requirementCode;

    /**
     * 寻源员
     */
    @FormField("employeeField_m9i39v9e")
    private List<String> sourcingAgents;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

    /**
     * 日期
     */
    @FormField("dateField_m9jk8xub")
    private LocalDateTime date;

    /**
     * 状态
     */
    @FormField("selectField_ma0g5quo")
    private String status;

    /**
     * 建议物料名称/货品名称
     */
    @FormField("textField_m6yrlpsf")
    private String itemName;

    /**
     * 货品编码（隐藏）
     */
    @FormField("textField_m9qg6fop")
    private String itemCode;

    /**
     * 规格型号
     */
    @FormField("textField_ma3bbscm")
    private String specification;

    /**
     * 供应商初步报价
     */
    @FormField("numberField_m6yrlpsl")
    private Float initialQuote;

    /**
     * 报价单位
     */
    @FormField("selectField_m9qj295w")
    private String quoteUnit;

    /**
     * 货品大类（关联）
     */
    @FormField("associationFormField_m6yrlpsg")
    private List<YidaAssociation> itemCategory;

    /**
     * 货品小类（关联）
     */
    @FormField("associationFormField_m6yrlpsh")
    private List<YidaAssociation> itemSubcategory;

    /**
     * 产品测试表（附件）
     */
    @FormField("attachmentField_m6yrlpsi")
    private List<YidaFile> productTestAttachment;

    /**
     * 备注说明
     */
    @FormField("textareaField_m9jk8xvy")
    private String remarks;

    /**
     * 货品大类ID（隐藏）
     */
    @FormField("numberField_m9qg6fon")
    private Float itemCategoryId;

    /**
     * 货品小类ID（隐藏）
     */
    @FormField("numberField_m9qg6foo")
    private Float itemSubcategoryId;

    /**
     * 是否新增供应商
     */
    @FormField("radioField_m9i9v1nx")
    private String isNewSupplier;

    /**
     * 已有供应商（关联）
     */
    @FormField("associationFormField_m6yrlps9")
    private List<YidaAssociation> existingSuppliers;

    /**
     * 新增供应商
     */
    @FormField("textField_m6yrlpsa")
    private String newSupplierName;

    /**
     * 供应商联系人
     */
    @FormField("textField_m9qmk22y")
    private String supplierContactPerson;

    /**
     * 供应商联系方式
     */
    @FormField("textField_m9w4wo95")
    private String supplierContact;

    /**
     * 注册资金（万元）
     */
    @FormField("numberField_ma3bbscn")
    private Float registeredCapital;

    /**
     * 发票类型
     */
    @FormField("selectField_m6yrlpsm")
    private String invoiceType;

    /**
     * 现有合作品牌
     */
    @FormField("textField_m9v0zxvq")
    private String existingPartnerBrand;

    /**
     * 年销售总额（万元）
     */
    @FormField("numberField_ma3bbsco")
    private Float annualSales;

    /**
     * 结账方式
     */
    @FormField("selectField_m6yrlpsn")
    private String settlementMethod;

    /**
     * 供应商编码（隐藏）
     */
    @FormField("textField_m9qj2961")
    private String supplierCode;

    /**
     * 营业执照
     */
    @FormField("attachmentField_m6yrlpsc")
    private List<YidaFile> businessLicenseAttachment;

    /**
     * 食品生产许可证
     */
    @FormField("attachmentField_m6yrlpsd")
    private List<YidaFile> foodProductionLicenseAttachment;

    /**
     * 食品生产许可证到期时间
     */
    @FormField("dateField_m9koepyh")
    private LocalDateTime foodProductionLicenseExpiry;

    /**
     * 食品经营许可证
     */
    @FormField("attachmentField_m6yrlpse")
    private List<YidaFile> foodBusinessLicenseAttachment;

    /**
     * 食品经营许可证到期时间
     */
    @FormField("dateField_m9koepyi")
    private LocalDateTime foodBusinessLicenseExpiry;

    /**
     * 其它证件
     */
    @FormField("attachmentField_m9mm4mjw")
    private List<YidaFile> otherDocuments;
}
