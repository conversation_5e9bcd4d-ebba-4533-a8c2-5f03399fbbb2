package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 子表：供应商报价行
 */
@Data
@FormEntity("tableField_m5p4tavc")
public class SupplierQuoteItem {

    /**
     * 供应商信息（关联表单）
     */
    @FormField("associationFormField_mbj2j2re")
    private List<YidaAssociation> supplierInfo;

    /**
     * 供应商报价
     */
    @FormField("numberField_m5xokx7t")
    private Float supplierPrice;

    /**
     * 价格开始时间
     */
    @FormField("dateField_madq6goi")
    private LocalDateTime priceStartDate;

    /**
     * 价格结束时间
     */
    @FormField("dateField_madq6goj")
    private LocalDateTime priceEndDate;

    /**
     * 供应商名称
     */
    @FormField("textField_mbj36d15")
    private String supplierName;

    /**
     * 供应商联系方式
     */
    @FormField("textField_m9kphxpf")
    private String supplierContact;

    /**
     * 供应商编码
     */
    @FormField("textField_m9kphxpe")
    private String supplierCode;

    /**
     * 发票类型
     */
    @FormField("textField_mbj3y8g5")
    private String invoiceType;

    /**
     * 结账方式
     */
    @FormField("textField_mbj3y8g6")
    private String settlementMethod;
}
