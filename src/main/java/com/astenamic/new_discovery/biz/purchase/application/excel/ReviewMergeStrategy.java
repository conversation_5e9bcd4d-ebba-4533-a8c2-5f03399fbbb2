package com.astenamic.new_discovery.biz.purchase.application.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateV2DTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.*;

/**
 * 上半表：连续行中「名称 + 货品编码」相同 → 合并指定列
 */
public class ReviewMergeStrategy extends AbstractMergeStrategy {

    private final List<int[]> ranges   = new ArrayList<>();
    private final List<Integer> cols   = ExcelConst.REVIEW_COLS;

    public ReviewMergeStrategy(List<PricingReviewTemplateV2DTO.ReviewTemplate> list) {
        if (list == null || list.isEmpty()) return;

        int curRow = ExcelConst.REVIEW_START_ROW - 1;          // 转 0-base
        for (int i = 0; i < list.size(); ) {
            int j = i + 1;
            while (j < list.size()
                    && Objects.equals(list.get(i).getReviewProductName(), list.get(j).getReviewProductName())
                    && Objects.equals(list.get(i).getReviewProductCode(), list.get(j).getReviewProductCode())) {
                j++;
            }
            if (j - i > 1) {                                   // 区段长度>1 行才要合并
                ranges.add(new int[]{curRow, curRow + j - i - 1});
            }
            curRow += j - i;
            i = j;
        }
    }

    @Override
    protected void merge(Sheet sh, Cell cell, Head head, Integer ignored) {
        if (cell == null) return;
        int r = cell.getRowIndex(), c = cell.getColumnIndex();
        for (int[] rg : ranges) {
            if (r == rg[0] && cols.contains(c)) {
                sh.addMergedRegionUnsafe(new CellRangeAddress(rg[0], rg[1], c, c));
                break;
            }
        }
    }
}
