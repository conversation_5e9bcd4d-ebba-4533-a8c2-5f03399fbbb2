package com.astenamic.new_discovery.biz.purchase.domain.pricing.service;


import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductSupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.SupplierQuotationFlow;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class ProductSupplierQuotationFlowService extends AbstractFlowService<ProductSupplierQuotationFlow> {

    public ProductSupplierQuotationFlowService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(ProductSupplierQuotationFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据定价单号查询供应商报价流程
     */
    public List<ProductSupplierQuotationFlow> getFlowsByPricingOrderNumber(String pricingOrderNumber) {

        SearchConditions conf = SearchCondition.builder()
                .textEq(ProductSupplierQuotationFlow.class, ProductSupplierQuotationFlow::getPricingFlowNumber, pricingOrderNumber, "+")
                .get();

        return super.getFlowsByCond(conf);
    }


    /**
     * 批量发起流程
     */
    public void batchLaunchProcess(String pricingOrderNumber, List<ProductSupplierQuotationFlow> flows) {
        if (flows == null || flows.isEmpty()) {
            log.warn("货品定价流程列表为空，定价单号: {}", pricingOrderNumber);
            return;
        }
        List<ProductSupplierQuotationFlow> oldData = this.getFlowsByPricingOrderNumber(pricingOrderNumber);
        if (oldData != null && !oldData.isEmpty()) {
            for (ProductSupplierQuotationFlow oldDatum : oldData) {
                for (ProductSupplierQuotationFlow newDatum : flows) {
                    if (oldDatum.getProductCode().equals(newDatum.getProductCode())) {
                        newDatum.setInstanceId(oldDatum.getInstanceId());
                        break;
                    }
                }
            }
        }
        super.batchLaunchProcess(flows);
    }

}
