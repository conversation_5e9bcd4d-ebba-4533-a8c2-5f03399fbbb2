package com.astenamic.new_discovery.biz.purchase.application.command;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.ExportPricingReviewReq;
import com.astenamic.new_discovery.biz.purchase.application.excel.handler.TwoLevelMergeHandler;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateDTO;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 导出定价评审表单命令
 * 1. 根据定价单号导出定价评审表单
 */
@Component
@RequiredArgsConstructor
public class ExportPricingReviewCmd implements
        PricingCommand<ExportPricingReviewReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ExportPricingReviewCmd.class);
    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "导出定价评审表单", atMobiles = {"19353308172"})
    public Void execute(ExportPricingReviewReq req) {

        OutputStream outputStream = req.out();
        String pricingOrderNumber = req.pricingOrderNumber();

        List<PricingReview> reviews =
                ctx.getPricingReviewService().getByPricingOrderNumber(pricingOrderNumber);

        /* 样品测试表 URL 缓存 */
        Map<String, String> urlCache = new HashMap<>();
        reviews.forEach(r -> {
            if (r.getSampleTestForms() != null) {
                r.getSampleTestForms().forEach(file -> {
                    String u = file.getDownloadUrl();
                    if (u != null && !urlCache.containsKey(u)) {
                        urlCache.put(u, ctx.getYiDaSession()
                                .temporaryUrls(u, PricingReview.class));
                    }
                    file.setDownloadUrl(urlCache.get(u));
                });
            }
        });

        if (reviews.isEmpty()) {
            logger.warn("未找到定价评审表单，定价单号：{}", pricingOrderNumber);
            throw new BizException("未找到定价评审表单");
        }

        reviews.sort(Comparator
                .comparing(PricingReview::getProductName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(PricingReview::getSupplierName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(PricingReview::getExecuteStoreCode, Comparator.nullsFirst(Comparator.naturalOrder()))
        );


        ProductPricingFlow flow =
                ctx.getProductPricingFlowService().getByPricingOrderNumber(pricingOrderNumber);

        Map<String, Object> fillMap = new HashMap<>();
        fillMap.put("applicationDate",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        fillMap.put("description", flow.getDescription());
        fillMap.put("decisionNote", flow.getDecisionNote());

        List<PricingReviewTemplateDTO> exportData = reviews.stream()
                .map(r -> {
                    PricingReviewTemplateDTO dto = new PricingReviewTemplateDTO();
                    BeanUtils.copyProperties(r, dto);
                    if (r.getSampleTestForms() != null) {
                        dto.setSampleTestForms(r.getSampleTestForms().stream()
                                .map(YidaFile::getDownloadUrl)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(",")));
                    }
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    dto.setPriceStartDate(r.getPriceStartDate() == null ? "" : df.format(r.getPriceStartDate()));
                    dto.setPriceEndDate(r.getPriceEndDate() == null ? "" : df.format(r.getPriceEndDate()));
                    dto.setLastEndDate(r.getLastEndDate() == null ? "" : df.format(r.getLastEndDate()));
                    return dto;
                }).toList();

        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try (InputStream template = resolver.getResource("classpath:templates/定价评审模板.xlsx")
                .getInputStream();
             ExcelWriter writer = EasyExcel.write(outputStream)
                     .withTemplate(template)
                     .registerWriteHandler(new TwoLevelMergeHandler(exportData, 2))
                     .build()) {

            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig cfg = FillConfig.builder().forceNewRow(true).build();
            writer.fill(exportData, cfg, writeSheet);
            writer.fill(fillMap, writeSheet);
            writer.finish();

        } catch (Exception e) {
            logger.error("导出定价评审表单失败：{}", e.getMessage(), e);
        }
        return null;
    }
}
