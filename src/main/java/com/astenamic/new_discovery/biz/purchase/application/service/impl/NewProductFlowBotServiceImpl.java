package com.astenamic.new_discovery.biz.purchase.application.service.impl;


import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.NewProductIntroductionFlow;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.NewProductIntroductionFlowService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class NewProductFlowBotServiceImpl extends AbstractFlowBotService<NewProductIntroductionFlow> {

    @Autowired
    private NewProductIntroductionFlowService newProductIntroductionFlowService;

    public NewProductFlowBotServiceImpl(YiDaSession yiDaSession, YidaConfigProperties yidaConfigProperties) {
        super(NewProductIntroductionFlow.class, yiDaSession, yidaConfigProperties);
    }

    @Override
    protected NewProductIntroductionFlow getFlowByCode(String flowCode) {
        return newProductIntroductionFlowService.getFlowByRequirementCode(flowCode);
    }

    @Override
    protected boolean checkFlowRobotPass(String flowCode) {
        return newProductIntroductionFlowService.checkFlowRobotPass(flowCode);
    }
}
