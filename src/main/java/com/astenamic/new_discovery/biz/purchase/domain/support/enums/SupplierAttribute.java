package com.astenamic.new_discovery.biz.purchase.domain.support.enums;

import lombok.Getter;

/**
 * 供应商属性枚举
 */
@Getter
public enum SupplierAttribute {

    /**
     * 0 – 普通供应商
     */
    STANDARD(0, "普通供应商"),

    /**
     * 1 – 门店自购供应商
     */
    STORE_SELF_PURCHASE(1, "门店自购供应商"),

    /**
     * 2 – 总部代采供应商
     */
    HQ_CENTRAL_PURCHASE(2, "总部代采供应商");

    // ===== 字段 =====
    private final int code;
    private final String desc;

    SupplierAttribute(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // ===== 根据 code 反查 =====
    public static SupplierAttribute of(int code) {
        for (SupplierAttribute attr : values()) {
            if (attr.code == code) {
                return attr;
            }
        }
        throw new IllegalArgumentException("未知供应商属性 code = " + code);
    }

    // ===== 根据 desc 反查 =====
    public static SupplierAttribute ofDesc(String desc) {
        for (SupplierAttribute attr : values()) {
            if (attr.desc.equals(desc)) {
                return attr;
            }
        }
        throw new IllegalArgumentException("未知供应商属性 desc = " + desc);
    }
}
