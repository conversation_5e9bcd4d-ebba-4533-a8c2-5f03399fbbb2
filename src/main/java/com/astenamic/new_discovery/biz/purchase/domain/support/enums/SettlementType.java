package com.astenamic.new_discovery.biz.purchase.domain.support.enums;

import lombok.Getter;

/**
 * 结算方式枚举
 */

@Getter
public enum SettlementType {

    /** 1 - 货到付款 */
    CASH_ON_DELIVERY(1,  "货到付款"),

    /** 2 - 预付 */
    PREPAID(2,           "预付"),

    /** 7 - 周付 */
    WEEKLY(7,            "周付"),

    /** 10 - 十天结算 */
    TEN_DAYS(10,         "十天结算"),

    /** 14 - 半月付 */
    HALF_MONTH(14,       "半月付"),

    /** 28 - 月付 */
    MONTHLY(28,          "月付"),

    /** 60 - 双月付 */
    BIMONTHLY(60,        "双月付"),

    /** 3 - 季度付 */
    QUARTERLY(3,         "季度付"),

    /** 6 - 半年付 */
    HALF_YEAR(6,         "半年付"),

    /** 12 - 年付 */
    YEARLY(12,           "年付"),

    /** 29 - 压批结算 */
    AFTER_BATCH(29,      "压批结算");

    // ========== 字段 ==========
    private final int code;
    private final String desc;

    SettlementType(int code, String desc) {
        this.code  = code;
        this.desc  = desc;
    }

    // ========== 根据 code 反查 ==========
    public static SettlementType of(int code) {
        for (SettlementType t : values()) {
            if (t.code == code) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知结算方式 code = " + code);
    }

    // ========== 根据 desc 反查 ==========
    public static SettlementType ofDesc(String desc) {
        for (SettlementType t : values()) {
            if (t.desc.equals(desc)) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知结算方式 desc = " + desc);
    }
}
