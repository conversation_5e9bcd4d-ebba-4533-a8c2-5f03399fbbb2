package com.astenamic.new_discovery.biz.purchase.application.excel;


import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateV2DTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.*;

/**
 * 下半表：<br>
 * ① 同一「货品名称+编码」→ 合并货品列<br>
 * ② 其内部再按「供应商名称+编码」→ 合并多列
 */
public class InputMergeStrategy extends AbstractMergeStrategy {

    private final List<CellRangeAddress> regions = new ArrayList<>();

    public InputMergeStrategy(List<PricingReviewTemplateV2DTO.InputData> list,
                              int inputStart0) {                         // 0-base

        if (list == null || list.isEmpty()) return;

        int row = inputStart0;
        for (int i = 0; i < list.size(); ) {
            /* 一级：货品 */
            int j = i + 1;
            while (j < list.size()
                    && Objects.equals(list.get(i).getInputProductName(), list.get(j).getInputProductName())
                    && Objects.equals(list.get(i).getInputProductName(), list.get(j).getInputProductName())) j++;

            if (j - i > 1)
                regions.add(new CellRangeAddress(row, row + j - i - 1,
                        ExcelConst.PROD_COL, ExcelConst.PROD_COL));

            /* 二级：供应商 */
            int subRow = row;
            for (int k = i; k < j; ) {
                int l = k + 1;
                while (l < j
                        && Objects.equals(list.get(k).getInputSupplierName(), list.get(l).getInputSupplierName())
                        && Objects.equals(list.get(k).getInputSupplierCode(), list.get(l).getInputSupplierCode())) l++;

                if (l - k > 1) {
                    for (int c : ExcelConst.SUPP_COLS)
                        regions.add(new CellRangeAddress(subRow, subRow + l - k - 1, c, c));
                }
                subRow += l - k;
                k = l;
            }
            row += j - i;
            i = j;
        }
    }

    @Override
    protected void merge(Sheet sh, Cell cell, Head head, Integer ignored) {
        if (cell == null) return;
        regions.stream()
                .filter(rg -> rg.getFirstRow() == cell.getRowIndex()
                        && rg.getFirstColumn() == cell.getColumnIndex())
                .forEach(sh::addMergedRegionUnsafe);
    }
}
