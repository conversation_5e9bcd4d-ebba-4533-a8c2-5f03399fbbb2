package com.astenamic.new_discovery.biz.purchase.domain.newproduct.service;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.NewProductIntroductionFlow;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SupplierAdmissionFlow;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.enums.InstanceStatusEnum;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新品引入流程服务
 */
@Slf4j
@Service
public class NewProductIntroductionFlowService extends AbstractFlowService<NewProductIntroductionFlow> {

    @Autowired
    private SupplierAdmissionFlowService supplierAdmissionFlowService;

    public NewProductIntroductionFlowService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(NewProductIntroductionFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据需求单编码查询流程
     */
    public NewProductIntroductionFlow getFlowByRequirementCode(String requirementCode) {
        SearchConditions cond = SearchCondition.builder()
                .textEq("serialNumberField_m4assakr", requirementCode, "+")
                .get();
        return super.getFlowByCond(cond);
    }

    /**
     * 校验流程机器人是否可以通过
     */
    public boolean checkFlowRobotPass(String requirementCode) {
        // 查询供应商准入流程
        List<SupplierAdmissionFlow> flows = supplierAdmissionFlowService.getFlowsByRequirementOrderNumber(requirementCode);
        return flows.isEmpty() || flows.stream()
                .map(SupplierAdmissionFlow::getInstanceStatus)
                .noneMatch(InstanceStatusEnum.RUNNING.getValue()::equals);
    }


}
