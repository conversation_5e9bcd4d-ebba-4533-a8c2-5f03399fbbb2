package com.astenamic.new_discovery.biz.purchase.domain.support.repository;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierProductBinding;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface SupplierProductBindingRepository extends JpaRepository<SupplierProductBinding, Long> {

    @Query("SELECT s FROM xfx_lgt_supgood s WHERE s.productCode = :productCode")
    List<SupplierProductBinding> findByProductCode(@Param("productCode") String productCode);


    List<SupplierProductBinding> findByProductCodeIn(Collection<String> productCodes);
}
