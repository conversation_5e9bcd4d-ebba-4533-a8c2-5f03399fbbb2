package com.astenamic.new_discovery.biz.purchase.application.command;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.ImportPricingReviewV2Req;
import com.astenamic.new_discovery.biz.purchase.application.excel.StringToFloatConverter;
import com.astenamic.new_discovery.biz.purchase.application.excel.MergeAwarePricingV2Listener;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateV2DTO;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 导入定价评审表单命令V2
 */
@Component
@RequiredArgsConstructor
public class ImportPricingReviewV2Cmd implements
        PricingCommand<ImportPricingReviewV2Req, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ImportPricingReviewV2Cmd.class);

    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "导入定价评审表V2", atMobiles = {"19353308172"})
    public Void execute(ImportPricingReviewV2Req req) {

        String url = req.url();

        String pricingOrderNumber = req.pricingOrderNumber();

        try {
            byte[] data = IOUtils.toByteArray(download(
                    ctx.getYiDaSession().temporaryUrls(url, ProductPricingFlow.class)));

            int dataStartRow = findInputDataTableStartRow(data);

            if (dataStartRow < 0) {
                throw new BizException("未找到定价导入模板表格");
            }

            /* 读取InputData数据（下半部分） */
            List<PricingReviewTemplateV2DTO.InputData> inputItems = readInputDataFromExcel(data, dataStartRow);

            if (inputItems.isEmpty()) {
                throw new BizException("未找到有效的导入数据");
            }

            /* 获取现有数据 */
            List<PricingReview> existingData =
                    ctx.getPricingReviewService().getByPricingOrderNumber(pricingOrderNumber);

            /* 更新数据：InputData → PricingReview */
            updatePricingReviewFromInputData(inputItems, existingData);

            /* 数据校验 */
            validatePricingReviewData(inputItems, existingData, dataStartRow);

            /* 保存数据 */
//            ctx.getPricingReviewService().batchSave(existingData);

            logger.info("成功导入定价评审表V2，更新数据行数：{}", inputItems.size());

        } catch (Exception e) {
            logger.error("导入定价评审表V2失败：", e);
            throw new BizException("导入失败，" + e.getMessage());
        }
        return null;
    }


    /**
     * 从Excel中读取InputData数据 - 使用标准的监听器方式
     */
    private List<PricingReviewTemplateV2DTO.InputData> readInputDataFromExcel(byte[] data, int dataStartRow) throws Exception {

        List<PricingReviewTemplateV2DTO.InputData> result;
        try (InputStream is = new ByteArrayInputStream(data)) {

            MergeAwarePricingV2Listener listener = new MergeAwarePricingV2Listener(dataStartRow + 2);

            EasyExcel.read(is, PricingReviewTemplateV2DTO.InputData.class, listener)
                    .registerConverter(new StringToFloatConverter())
                    .extraRead(CellExtraTypeEnum.MERGE) // ★ 开启合并信息
                    .headRowNumber(dataStartRow + 2) // 从数据表头行开始
                    .sheet()
                    .doRead();

            result = listener.getData();
        }

        // 过滤掉无效数据
        result = result.stream()
                .filter(item -> StringUtils.isNotBlank(item.getInputObjectId()) &&
                        !"id".equalsIgnoreCase(item.getInputObjectId()))
                .collect(java.util.stream.Collectors.toList());

        logger.info("读取到 {} 条有效数据", result.size());
        return result;
    }

    /**
     * 查找下半部分表格的起始行
     */
    private int findInputDataTableStartRow(byte[] data) throws Exception {
        try (InputStream is = new ByteArrayInputStream(data)) {
            List<Map<Integer, String>> allRows = readAllRowsKeepBlank(is);
            int dataStartRow = -1;
            for (int i = 0; i < allRows.size(); i++) {
                Map<Integer, String> row = allRows.get(i);
                if (row != null) {
                    // 查找包含"定价导入模板"的行
                    for (String cellValue : row.values()) {
                        if (cellValue != null && cellValue.startsWith("定价导入模板")) {
                            dataStartRow = i;
                            break;
                        }
                    }
                }
            }

            logger.info("数据表头行：{}", dataStartRow);
            return dataStartRow;
        }
    }

    private List<Map<Integer, String>> readAllRowsKeepBlank(InputStream is) {
        List<Map<Integer, String>> allRows = new ArrayList<>();
        EasyExcel.read(is)
                .headRowNumber(0)
                .ignoreEmptyRow(false)
                .registerReadListener(new AnalysisEventListener<Map<Integer, String>>() {
                    @Override
                    public void invoke(Map<Integer, String> row, AnalysisContext ctx) {
                        allRows.add(row);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext ctx) {
                    }
                })
                .sheet()
                .doRead();
        return allRows;
    }


    /**
     * 将InputData数据更新到PricingReview实体
     */
    private void updatePricingReviewFromInputData(List<PricingReviewTemplateV2DTO.InputData> inputItems, List<PricingReview> existingData) {
        Map<String, PricingReview> existingMap = existingData.stream()
                .collect(Collectors.toMap(PricingReview::getObjectId, r -> r));
        for (PricingReviewTemplateV2DTO.InputData inputData : inputItems) {
            PricingReview pr = existingMap.get(inputData.getInputObjectId());
            if (pr != null) {
                if (StringUtils.isNotBlank(inputData.getInputSupplierCode())) {
                    pr.setSupplierCode(inputData.getInputSupplierCode());
                }
                pr.setFinalPrice(inputData.getInputFinalPrice());
                pr.setEnabled(inputData.getInputEnabled());
                // 处理日期字段
                LocalDateTime startDate = TimeUtils.toLocalDateTime(inputData.getInputPriceStartDate());
                LocalDateTime endDate = TimeUtils.toLocalDateTime(inputData.getInputPriceEndDate());
                pr.setPriceStartDate(startDate != null ? startDate.minusDays(1) : null);
                pr.setPriceEndDate(endDate != null ? endDate.minusDays(1) : null);
            }
        }
    }

    /**
     * 数据校验
     */
    private void validatePricingReviewData(List<PricingReviewTemplateV2DTO.InputData> inputItems, List<PricingReview> existingData, int dataStartRow) {

        Map<String, Integer> rowNumMap = new HashMap<>();

        for (int i = 0; i < inputItems.size(); i++) {
            rowNumMap.put(inputItems.get(i).getInputObjectId(), i + 1);
        }

        for (PricingReview pr : existingData) {

            if (!"是".equals(pr.getEnabled())) continue;

            int rowNum = rowNumMap.getOrDefault(pr.getObjectId(), -1);

            // 校验必填字段
            if (StringUtils.isEmpty(pr.getSupplierCode())) {
                throw new BizException("第" + (rowNum + dataStartRow + 2) + "行：供应商编码为空");
            }

            // 校验价格
            if (pr.getFinalPrice() == null || pr.getFinalPrice() <= 0) {
                throw new BizException("第" + (rowNum + dataStartRow + 2) + "行：最终定价无效");
            }

            // 校验日期
            try {
                pr.validatePriceDates();
            } catch (Exception e) {
                throw new BizException("第" + (rowNum + dataStartRow + 2) + "行：" + e.getMessage());
            }
        }
    }

    /* ------------ helpers -------------- */
    private InputStream download(String useUrl) throws Exception {
        java.net.URL u = new java.net.URL(useUrl);
        java.net.HttpURLConnection c = (java.net.HttpURLConnection) u.openConnection();
        c.setRequestMethod("GET");
        c.setConnectTimeout(5000);
        c.setReadTimeout(5000);
        if (c.getResponseCode() != java.net.HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("下载失败，HTTP " + c.getResponseCode());
        }
        return c.getInputStream();
    }
}
