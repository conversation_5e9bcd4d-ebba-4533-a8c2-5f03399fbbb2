package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.SyncPriceSheetCategoryReq;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 同步定价单分类命令
 * 1. 根据产品编号查询产品信息
 * 2. 更新定价单的分类和类型
 */
@Component
@RequiredArgsConstructor
public class SyncPriceSheetCategoryCmd implements
        PricingCommand<SyncPriceSheetCategoryReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(SyncPriceSheetCategoryCmd.class);
    private final ProductPricingContext ctx;

    @Override
    public Void execute(SyncPriceSheetCategoryReq req) {

        String productSno = req.productSno();
        ProductInfo info = ctx.getProductInfoService().getProductInfo(productSno);
        List<ProductPrice> list = ctx.getProductPriceRepository().findByGoodSno(productSno);
        if (list.isEmpty()) {
            logger.warn("没有定价单：{},{}", productSno, info.getName());
            return null;
        }
        list.forEach(p -> {
            p.setGoodPricingCategory(info.getGoodPricingCategory());
            p.setGoodPricingType(info.getGoodPricingType());
        });
        ctx.getProductPriceRepository().saveAll(list);
        return null;
    }
}
