package com.astenamic.new_discovery.biz.purchase.domain.pricing.service;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.SupplierQuotationFlow;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商报价流程服务
 */
@Slf4j
@Service
public class SupplierQuotationFlowService extends AbstractFlowService<SupplierQuotationFlow> {

    public SupplierQuotationFlowService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(SupplierQuotationFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据定价单号查询供应商报价流程
     */
    public List<SupplierQuotationFlow> getFlowsByPricingOrderNumber(String pricingOrderNumber) {

        SearchConditions conf = SearchCondition.builder()
                .textEq("textField_m9kwzh12", pricingOrderNumber, "+")
                .get();
        return super.getFlowsByCond(conf);

    }

    /**
     * 批量发起流程
     */
    public void batchLaunchProcess(String pricingOrderNumber, List<SupplierQuotationFlow> flows) {

        List<SupplierQuotationFlow> oldData = this.getFlowsByPricingOrderNumber(pricingOrderNumber);
        if (oldData != null && !oldData.isEmpty()) {
            for (SupplierQuotationFlow oldDatum : oldData) {
                for (SupplierQuotationFlow newDatum : flows) {
                    if (oldDatum.getSupplierCode().equals(newDatum.getSupplierCode())) {
                        newDatum.setInstanceId(oldDatum.getInstanceId());
                        break;
                    }
                }
            }
        }
        super.batchLaunchProcess(flows);

    }

}
