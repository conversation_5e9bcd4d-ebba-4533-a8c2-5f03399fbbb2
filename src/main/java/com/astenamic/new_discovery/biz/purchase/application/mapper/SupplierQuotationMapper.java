package com.astenamic.new_discovery.biz.purchase.application.mapper;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.*;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.SupplierQuoteItem;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface SupplierQuotationMapper {


    @Mappings({
            @Mapping(target = "productName",            source = "flow.productName"),
            @Mapping(target = "productCode",            source = "flow.productCode"),
            @Mapping(target = "factorySpec",            source = "flow.productSpec"),
            @Mapping(target = "unit",                   source = "flow.orderUnit"),
            @Mapping(target = "supplierName",           source = "item.supplierName"),
            @Mapping(target = "supplierContact",        source = "item.supplierContact"),
            @Mapping(target = "supplierCode",           source = "item.supplierCode"),
            @Mapping(target = "price",                  source = "item.supplierPrice"),
            @Mapping(target = "priceStartDate",         source = "item.priceStartDate"),
            @Mapping(target = "priceEndDate",           source = "item.priceEndDate"),
            @Mapping(target = "invoiceType",            source = "item.invoiceType"),
            @Mapping(target = "paymentMethod",          source = "item.settlementMethod")
    })
    PricingReview toPricingReview(@Context String pricingOrderNumber,
                                  ProductSupplierQuotationFlow flow,
                                  SupplierQuoteItem item);

    @AfterMapping
    default void setPricingOrderNumber(@Context String pricingOrderNumber, @MappingTarget PricingReview review) {
        review.setPricingOrderNumber(pricingOrderNumber);
    }

    default List<PricingReview> toPricingReviewList(ProductSupplierQuotationFlow flow,
                                                    String pricingOrderNumber) {
        return Optional.ofNullable(flow.getSupplierQuoteTable())
                .orElse(List.of())
                .stream()
                .filter(i -> i.getSupplierPrice() != null && i.getSupplierPrice() > 0)  // 只保留有报价的
                .map(i -> toPricingReview(pricingOrderNumber, flow, i))
                .collect(Collectors.toList());
    }
}
