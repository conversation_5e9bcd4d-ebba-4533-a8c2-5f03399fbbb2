package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.SupplierQuoteItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 货品供应商报价表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-BFFEBE61122C4BCE9637E5221C9AB5A0WCB1", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductSupplierQuotationFlow extends YidaFlowObject {

    /**
     * 报价单号（流水号字段）
     */
    @FormField("serialNumberField_m9kwzh11")
    private String quotationOrderNumber;

    /**
     * 定价流程单号（关联主流程）
     */
    @FormField("textField_m9kwzh12")
    private String pricingFlowNumber;

    /**
     * 货品名称
     */
    @FormField("textField_ma21fapx")
    private String productName;

    /**
     * 订货单位
     */
    @FormField("textField_m9kz1v8n")
    private String orderUnit;

    /**
     * 货品编码
     */
    @FormField("textField_m5z5km64")
    private String productCode;

    /**
     * 规格
     */
    @FormField("textField_m9kz1v8m")
    private String productSpec;

    /**
     * 寻源员
     */
    @FormField("employeeField_mbj4bn9e")
    private List<String> sourcingAgents;
    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

    /**
     * 供应商报价表
     */
    @FormField("tableField_m5p4tavc")
    private List<SupplierQuoteItem> supplierQuoteTable;

}
