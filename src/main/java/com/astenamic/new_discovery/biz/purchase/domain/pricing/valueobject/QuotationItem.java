package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@FormEntity("tableField_m5p4tavc")
public class QuotationItem {

    /**
     * 货品名称
     */
    @FormField("textField_ma21fapx")
    private String productName;

    /**
     * 货品编码
     */
    @FormField("textField_m5z5km64")
    private String productCode;

    /**
     * 供应商报价
     */
    @FormField("numberField_m5xokx7t")
    private Float quotationPrice;

    /**
     * 价格开始时间
     */
    @FormField("dateField_madq6goi")
    private LocalDateTime startDate;

    /**
     * 价格结束时间
     */
    @FormField("dateField_madq6goj")
    private LocalDateTime endDate;

    /**
     * 规格
     */
    @FormField("textField_m9kz1v8m")
    private String specification;

    /**
     * 订货单位
     */
    @FormField("textField_m9kz1v8n")
    private String orderUnit;
}