package com.astenamic.new_discovery.biz.purchase.domain.support.service;

import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.DictType;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class DictTypeService extends AbstractFormService<DictType> {

    public DictTypeService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(DictType.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    public DictType getByType(String dictType) {
        SearchConditions cond = SearchCondition.builder()
                .textEq(DictType.class, DictType::getDictType, dictType, "+")
                .get();
        return super.getFormByCond(cond);
    }

}
