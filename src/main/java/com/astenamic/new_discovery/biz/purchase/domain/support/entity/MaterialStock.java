package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;


@Data
@Entity(name = "xfx_material_stock")
public class MaterialStock {

    /** 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 公司编码 */
    @Column(name = "company_code", length = 255)
    private String companyCode;

    /** 公司名称 */
    @Column(name = "company_name", length = 255)
    private String companyName;

    /** 工厂代码 */
    @Column(name = "factory_code", length = 255)
    private String factoryCode;

    /** 物料编码 */
    @Column(name = "material_no")
    private Long materialNo;

    /** 蜀海物料号 */
    @Column(name = "sh_material_id")
    private Integer shMaterialId;

    /** 物料名称 */
    @Column(name = "material_name", length = 255)
    private String materialName;

    /** 上架状态 */
    @Column(name = "status", length = 255)
    private String status;

    /** 销售单位 */
    @Column(name = "xs_unit", length = 255)
    private String xsUnit;

    /** 可用库存 */
    @Column(name = "available_stock")
    private Double availableStock;

    /** 不可用库存 */
    @Column(name = "not_available_stock")
    private Double notAvailableStock;

    /** 基本单位代码 */
    @Column(name = "base_unit_code", length = 255)
    private String baseUnitCode;

    /** 基本单位名称 */
    @Column(name = "base_unit_name", length = 255)
    private String baseUnitName;

    /** 待上架库存 */
    @Column(name = "shelves_stock")
    private Integer shelvesStock;

    /** 待检品库存 */
    @Column(name = "detect_stock", length = 53)
    private String detectStock;

    /** 残品库存 */
    @Column(name = "scrap_stock")
    private Double scrapStock;

    /** 待拣库存 */
    @Column(name = "pick_stock")
    private Double pickStock;

    /** 待分拣库存 */
    @Column(name = "sort_stock")
    private Double sortStock;

    /** 待发货库存 */
    @Column(name = "ship_stock")
    private Double shipStock;

    /** 盘点锁定库存 */
    @Column(name = "lock_stock")
    private Double lockStock;

    /** 库存冻结库存 */
    @Column(name = "freeze_stock")
    private Double freezeStock;

    /** 移位锁定库存 */
    @Column(name = "shift_stock")
    private Double shiftStock;

    /** 补货锁定库存 */
    @Column(name = "replenishment_stock")
    private Double replenishmentStock;

    /** 报缺锁定库存 */
    @Column(name = "miss_stock")
    private Double missStock;

    /** 盲收锁定库存 */
    @Column(name = "blind_stock")
    private Double blindStock;

    /** 补录锁定库存 */
    @Column(name = "replenish_stock")
    private Double replenishStock;

    /** 更新时间 */
    @Column(name = "update_time")
    private Date updateTime;
}
