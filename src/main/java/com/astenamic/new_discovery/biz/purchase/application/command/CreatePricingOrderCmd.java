package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.CreatePricingOrderReq;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.NewProductIntroductionFlow;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject.RawMaterialItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.*;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.PriceAdjustmentItem;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CreatePricingOrderCmd – V2 聚合策略
 * <p>
 * 同一供应商内部：
 * <ul>
 *     <li>把每个门店视为一个『交易』，交易中包含 Key=(productCode, finalPrice, startDate, endDate)</li>
 *     <li>通过<strong>迭代交集</strong>算法：反复找出【当前最大公共项集】，把拥有这批公共 Key 的所有门店合并；
 *         然后从这些门店里删掉已合并的 Key，继续下一轮，直到全部 Key 被分配完毕</li>
 * </ul>
 * </p>
 */
@Component
@RequiredArgsConstructor
public class CreatePricingOrderCmd implements
        PricingCommand<CreatePricingOrderReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(CreatePricingOrderCmd.class);
    private final ProductPricingContext ctx;

    /* ---------------- key 定义 ---------------- */
    private record Key(String productCode, Float price, String start, String end) {
    }

    @Override
    @DingTalkAlert(title = "创建定价单", atMobiles = {"19353308172"})
    public Void execute(CreatePricingOrderReq req) {

        String pricingType = req.pricingType();
        String pricingOrderNumber = req.pricingOrderNumber();
        String requirementCode = req.requirementCode();

        /* 1. 取启用评审行 */
        List<PricingReview> reviews = ctx.getPricingReviewService()
                .getByPricingOrderNumber(pricingOrderNumber).stream()
                .filter(r -> "是".equals(r.getEnabled()))
                .toList();
        if (reviews.isEmpty()) return null;

        /* 1.1 新品编码替换 */
        if (PricingType.NEW_PRODUCT.getDesc().equals(pricingType)) {
            NewProductIntroductionFlow npf = ctx.getNewProductIntroductionFlowService()
                    .getFlowByRequirementCode(requirementCode);
            if (npf == null) throw new BizException(Result.ErrorCode.FLOW_NOT_FOUND);
            if ("是".equals(npf.getIsNewProduct())) {
                List<RawMaterialItem> tbl = npf.getRawMaterialTable();
                if (tbl == null || tbl.isEmpty()) throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
                String newCode = tbl.get(0).getProductCode();
                reviews.forEach(r -> r.setProductCode(newCode));
            }
        }

        /* 2. 供应商 → 门店 → Key 集合 */
        Map<String, List<PricingReview>> bySupplier = reviews.stream()
                .collect(Collectors.groupingBy(PricingReview::getSupplierCode));

        DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<ProductPricingSheet> sheetAll = new ArrayList<>();

        for (Map.Entry<String, List<PricingReview>> supEntry : bySupplier.entrySet()) {
            String supplierCode = supEntry.getKey();
            List<PricingReview> supRows = supEntry.getValue();
            String supplierName = supRows.get(0).getSupplierName();

            /* 2.1 store → 剩余 Key 集合 */
            class StoreBag {
                String code;
                String name;
                List<PricingReview> originRows = new ArrayList<>();
                Set<Key> keys = new LinkedHashSet<>();
            }
            Map<String, StoreBag> storeMap = new LinkedHashMap<>();
            for (PricingReview r : supRows) {
                storeMap.computeIfAbsent(r.getExecuteStoreCode(), k -> {
                    StoreBag sb = new StoreBag();
                    sb.code = k;
                    sb.name = r.getExecuteStore();
                    return sb;
                });
                StoreBag sb = storeMap.get(r.getExecuteStoreCode());
                sb.originRows.add(r);
                sb.keys.add(new Key(r.getProductCode(), r.getFinalPrice(),
                        r.getPriceStartDate() == null ? "" : r.getPriceStartDate().format(DF),
                        r.getPriceEndDate() == null ? "" : r.getPriceEndDate().format(DF)));
            }

            /* 3. 迭代找最大公共项集 & 合并门店 */
            List<ProductPricingSheet> sheetsBySup = new ArrayList<>();
            while (storeMap.values().stream().anyMatch(b -> !b.keys.isEmpty())) {

                // 3.1 选当前 Key 最多的门店做 seed
                StoreBag seed = storeMap.values().stream()
                        .filter(b -> !b.keys.isEmpty())
                        .max(Comparator.comparingInt(b -> b.keys.size()))
                        .orElseThrow();

                Set<Key> common = new LinkedHashSet<>(seed.keys);
                List<StoreBag> groupStores = new ArrayList<>();
                groupStores.add(seed);

                // 3.2 尝试把其他门店纳入并取交集
                for (StoreBag other : storeMap.values()) {
                    if (other == seed || other.keys.isEmpty()) continue;
                    Set<Key> inter = new LinkedHashSet<>(common);
                    inter.retainAll(other.keys);
                    if (!inter.isEmpty()) {
                        common = inter; // 更新公共键集合
                        groupStores.add(other);
                    }
                }

                // 3.3 生成 Sheet 行（公共 Key *每个* 货品生成一行）
                ProductPricingSheet sheet = new ProductPricingSheet();
                sheet.setPricingOrderNumber(pricingOrderNumber);
                sheet.setSupplier_sno(supplierCode);
                sheet.setSupplier_name(supplierName);
                sheet.setShop_sno(groupStores.stream().map(s -> s.code).collect(Collectors.joining(",")));
                sheet.setShop_name(groupStores.stream().map(s -> s.name).collect(Collectors.joining(",")));
                sheet.setItems(new ArrayList<>());
                sheet.setSyncStatus("待同步");

                for (Key k : common) {
                    PricingReview sample = groupStores.get(0).originRows.stream()
                            .filter(r -> k.productCode.equals(r.getProductCode())
                                    && Objects.equals(k.price, r.getFinalPrice()))
                            .findFirst().orElse(null);
                    if (sample == null) continue; // 理论不会为空

                    PriceAdjustmentItem item = new PriceAdjustmentItem();
                    item.setGood_sno(sample.getProductCode());
                    item.setProductName(sample.getProductName());
                    item.setUprice(sample.getFinalPrice());
                    item.setSupplier_price(sample.getSecondQuotation());
                    item.setStartdate(sample.getPriceStartDate());
                    item.setEnddate(sample.getPriceEndDate());
                    sheet.getItems().add(item);
                }
                sheetsBySup.add(sheet);

                // 3.4 把 "common" 从所有 groupStores 中移除，准备下一轮
                for (StoreBag sb : groupStores) sb.keys.removeAll(common);
            }
            sheetAll.addAll(sheetsBySup);
        }

        /* 4. 反向校验 & 保存（逻辑与之前一致） */
        if (!PricingReviewComparator.isExactMatch(reviews, restore(sheetAll))) {
            throw new BizException(Result.ErrorCode.VALIDATION_FAILED);
        }
        if (!ctx.getProductPricingSheetService().getByPricingOrderNumber(pricingOrderNumber).isEmpty()) {
            throw new BizException("定价单已存在，无法重复创建");
        }
        ctx.getProductPricingSheetService().batchSave(sheetAll);
        return null;
    }

    /* ----------------- restore & compare ----------------- */
    private List<PricingReview> restore(List<ProductPricingSheet> sheets) {
        DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<PricingReview> res = new ArrayList<>();
        for (ProductPricingSheet sheet : sheets) {
            List<String> shopCodes = Arrays.asList(Optional.ofNullable(sheet.getShop_sno()).orElse("").split(","));
            List<String> shopNames = Arrays.asList(Optional.ofNullable(sheet.getShop_name()).orElse("").split(","));
            for (PriceAdjustmentItem it : sheet.getItems()) {
                for (int i = 0; i < shopCodes.size(); i++) {
                    PricingReview pr = new PricingReview();
                    pr.setSupplierCode(sheet.getSupplier_sno());
                    pr.setSupplierName(sheet.getSupplier_name());
                    pr.setExecuteStoreCode(shopCodes.get(i));
                    pr.setExecuteStore(i < shopNames.size() ? shopNames.get(i) : "");
                    pr.setProductCode(it.getGood_sno());
                    pr.setProductName(it.getProductName());
                    pr.setFinalPrice(it.getUprice());
                    pr.setSecondQuotation(it.getSupplier_price());
                    pr.setPriceStartDate(it.getStartdate());
                    pr.setPriceEndDate(it.getEnddate());
                    res.add(pr);
                }
            }
        }
        return res;
    }

    private static class PricingReviewComparator {
        private record K(String sup, String shop, String prod, Float price, String s, String e) {
        }

        public static boolean isExactMatch(List<PricingReview> a, List<PricingReview> b) {
            return a.size() == b.size() && toKey(a).equals(toKey(b));
        }

        private static Set<K> toKey(List<PricingReview> l) {
            DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return l.stream().map(r -> new K(
                            r.getSupplierCode(),
                            r.getExecuteStoreCode(),
                            r.getProductCode(),
                            r.getFinalPrice(),
                            r.getPriceStartDate() == null ? "" : r.getPriceStartDate().format(DF),
                            r.getPriceEndDate() == null ? "" : r.getPriceEndDate().format(DF)))
                    .collect(Collectors.toSet());
        }
    }
}
