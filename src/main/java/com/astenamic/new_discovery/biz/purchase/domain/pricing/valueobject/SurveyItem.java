package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import lombok.Data;

import java.util.List;

@Data
@FormEntity("tableField_m5p4tavc")
public  class SurveyItem {
    /**
     * 货品名称
     */
    @FormField("selectField_m5xlkoa6")
    private String productName;

    /**
     * 货品编码
     */
    @FormField("textField_m5z5km64")
    private String productCode;

    /**
     * 市调价格一
     */
    @FormField("numberField_m9l0gxwg")
    private Float surveyPrice1;

    /**
     * 摊位号一
     */
    @FormField("textField_m9l0gxwi")
    private String stallNumber1;

    /**
     * 图片一
     */
    @FormField("imageField_m9l0gxwj")
    private List<YidaFile> image1;

    /**
     * 市调价格二
     */
    @FormField("numberField_m9l0gxwl")
    private Float surveyPrice2;

    /**
     * 摊位号二
     */
    @FormField("textField_m9l0gxwn")
    private String stallNumber2;

    /**
     * 图片二
     */
    @FormField("imageField_m9l0gxwp")
    private List<YidaFile> image2;
}
