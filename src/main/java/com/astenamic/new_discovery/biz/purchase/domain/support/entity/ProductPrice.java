package com.astenamic.new_discovery.biz.purchase.domain.support.entity;


import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 奥琦玮定价单项
 */
@Data
@Entity(name = "xfx_product_price")
public class ProductPrice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 审核时间
     */
    @Comment("审核时间")
    @Column(name = "atime")
    private LocalDateTime atime;

    @Comment("定价单code")
    @Column(name = "pricing_code", length = 55)
    private String pricingCode;

    @Comment("供应商的名称")
    @Column(name = "supplier_name", length = 55)
    private String supplierName;

    @Comment("供应商编码")
    @Column(name = "supplier_sno", length = 55)
    private String supplierSno;

    @Comment("当前执行价格")
    @Column(name = "uprice")
    private Float uprice;

    @Comment("价格执行门店名称")
    @Column(name = "shop_name", length = 55)
    private String shopName;

    @Comment("价格执行门店编码")
    @Column(name = "shop_sno", length = 55)
    private String shopSno;

    @Comment("货品名称")
    @Column(name = "good_name", length = 55)
    private String goodName;

    @Comment("货品编码")
    @Column(name = "good_sno", length = 55)
    private String goodSno;

    @Comment("货品品牌")
    @Column(name = "good_brand", length = 35)
    private String goodBrand;

    @Comment("规格")
    @Column(name = "std", length = 45)
    private String std;

    @Comment("货品大类")
    @Column(name = "fgoodtype_name", length = 55)
    private String fgoodtypeName;

    @Comment("货品小类")
    @Column(name = "goodtype_name", length = 55)
    private String goodtypeName;

    @Comment("订货单位名称")
    @Column(name = "applyguname", length = 55)
    private String applyguname;

    @Comment("价格开始生效日期")
    @Column(name = "startdate")
    private LocalDate startdate;

    @Comment("价格结束日期")
    @Column(name = "enddate")
    private LocalDate enddate;

    @Comment("去年同期平均单价")
    @Column(name = "lastyprice", length = 55)
    private String lastyprice;

    @Comment("上期平均采购单价")
    @Column(name = "lastprice", length = 55)
    private String lastprice;

    @Comment("上月采购数量")
    @Column(name = "applyamount", length = 55)
    private String applyamount;

    /**
     * 生鲜：1、集采：2
     */
    @Comment("定价类型")
    @Column(name = "good_pricing_type", length = 55)
    private String goodPricingType;

    public void setGoodPricingType(String goodPricingType) {
        if (PricingType.FRESH_PRODUCE.getDesc().equals(goodPricingType)) {
            this.goodPricingType = "1";
        } else if (PricingType.CENT_PROCUREMENT.getDesc().equals(goodPricingType)) {
            this.goodPricingType = "2";
        } else {
            this.goodPricingType = goodPricingType;
        }
    }

    @Comment("定价分类")
    @Column(name = "good_pricing_category", length = 55)
    private String goodPricingCategory;

    @Comment("是否已发起")
    @Column(name = "enabled")
    private String enabled;

}

