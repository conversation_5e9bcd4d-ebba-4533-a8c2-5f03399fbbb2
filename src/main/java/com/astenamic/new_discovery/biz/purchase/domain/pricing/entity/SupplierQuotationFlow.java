package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.QuotationItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 供应商报价表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-31B37ADF83DC4B3FA02843696810EFD9DZ90", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class SupplierQuotationFlow extends Yida<PERSON>lowObject {

    /**
     * 报价单号
     */
    @FormField("serialNumberField_m9kwzh11")
    private String quotationNumber;

    /**
     * 定价单号
     */
    @FormField("textField_m9kwzh12")
    private String pricingOrderNumber;

    /**
     * 供应商名称
     */
    @FormField("textField_ma21fapw")
    private String supplierName;

    /**
     * 供应商编码
     */
    @FormField("textField_m9kphxpe")
    private String supplierCode;

    /**
     * 供应商联系方式
     */
    @FormField("textField_m9kphxpf")
    private String supplierContact;

    /**
     * 结算方式
     */
    @FormField("selectField_m6yrlpsn")
    private String accountdatetype;

    /**
     * 发票种类
     */
    @FormField("selectField_m6yrlpsm")
    private String invoicetype;

    /**
     * 物料报价表-行信息
     */
    @FormField("tableField_m5p4tavc")
    private List<QuotationItem> quotationItems;


    /**
     * 寻源员
     */
    @FormField("employeeField_mbhm5axm")
    private List<String> sourcingAgents;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

}
