package com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto;

import java.math.BigDecimal;

public interface DispatchOutSummaryDTO {
    /**
     * 配送中心编码，对应 xfx_lgt_dispatchout.slscode
     * @return 配送中心编码
     */
    String getSlsCode();

    /**
     * 物料编码，对应 xfx_lgt_dispatchout.lgcode
     * @return 物料编码
     */
    String getLgCode();

    /**
     * 库存单位，对应 xfx_lgt_dispatchout.unit
     * @return 库存单位
     */
    String getDhUnit();

    /**
     * 汇总配送数量，SUM(ps_qty)
     * @return 配送总量
     */
    BigDecimal getQty();
}
