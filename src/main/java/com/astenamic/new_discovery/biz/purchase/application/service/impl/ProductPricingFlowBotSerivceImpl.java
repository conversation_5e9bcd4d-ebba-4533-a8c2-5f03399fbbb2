package com.astenamic.new_discovery.biz.purchase.application.service.impl;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.service.ProductPricingFlowService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProductPricingFlowBotSerivceImpl extends AbstractFlowBotService<ProductPricingFlow> {

    @Autowired
    private ProductPricingFlowService productPricingFlowService;


    public ProductPricingFlowBotSerivceImpl(YiDaSession yiDaSession, YidaConfigProperties yidaConfigProperties) {
        super(ProductPricingFlow.class, yiDaSession, yidaConfigProperties);
    }

    @Override
    protected boolean checkFlowRobotPass(String flowCode) {
        return productPricingFlowService.checkFlowRobotPass(flowCode);
    }

    @Override
    protected ProductPricingFlow getFlowByCode(String flowCode) {
        return productPricingFlowService.getByPricingOrderNumber(flowCode);
    }
}
