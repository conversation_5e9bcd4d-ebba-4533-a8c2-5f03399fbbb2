package com.astenamic.new_discovery.biz.purchase.application.service.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导出定价评审表 DTO
 */

@Data
public class PricingReviewTemplateV2DTO {

    private List<ReviewTemplate> reviewTemplates;

    private List<InputData> inputDatas;

    @Data
    public static class ReviewTemplate {

        @ExcelProperty("名称")
        private String reviewProductName;

        @ExcelProperty("货品编码")
        private String reviewProductCode;

        @ExcelProperty("月用量")
        private String reviewMonthlyUsage;

        @ExcelProperty("历史采购价")
        private Float reviewHistoricalPurchasePrice;

        @ExcelProperty("库存")
        private String reviewStock;

        @ExcelProperty("供应商名称")
        private String reviewSupplierName;

        @ExcelProperty("供应商联系方式")
        private String reviewSupplierContact;

        @ExcelProperty("厂号及规格")
        private String reviewFactorySpec;

        @ExcelProperty("价格")
        private Float reviewPrice;

        @ExcelProperty("发票类型")
        private String reviewInvoiceType;

        @ExcelProperty("付款方式")
        private String reviewPaymentMethod;

        @ExcelProperty("需求量")
        private String reviewText1;

        @ExcelProperty("样品测试是否通过")
        private String reviewText2;

        @ExcelProperty("二次报价")
        private String reviewText3;

        @ExcelProperty("金额预估")
        private String reviewText4;

        @ExcelProperty("本次采购与历史价格比的变化金额")
        private String reviewText5;

        @ExcelProperty("金额变动幅度")
        private String reviewText6;
    }

    @Data
    @ExcelIgnoreUnannotated
    public static class InputData {

        @ExcelProperty("id")
        private String inputObjectId;

        @ExcelProperty("货品名称")
        private String inputProductName;

        private String inputProductCode;

        @ExcelProperty("供应商名称")
        private String inputSupplierName;

        @ExcelProperty("供应商编码")
        private String inputSupplierCode;

        @ExcelProperty("执行门店")
        private String inputExecuteStore;

        private String inputExecuteStoreCode;

        @ExcelProperty("最终定价")
        private Float inputFinalPrice;

        @ExcelProperty("是否启用")
        private String inputEnabled;

        @ExcelProperty("价格开始日期")
        private String inputPriceStartDate;

        @ExcelProperty("价格结束日期")
        private String inputPriceEndDate;

    }

    public static PricingReviewTemplateV2DTO toDTO(List<PricingReview> pricingReviews) {
        PricingReviewTemplateV2DTO dto = new PricingReviewTemplateV2DTO();

        // 按货品+供应商维度分组
        record GroupKey(String productName, String supplierName, String productCode, String supplierCode) {
        }

        Map<GroupKey, List<PricingReview>> grouped = pricingReviews.stream()
                .filter(r -> r.getProductName() != null && r.getSupplierName() != null
                        && r.getProductCode() != null && r.getSupplierCode() != null)
                .collect(Collectors.groupingBy(
                        r -> new GroupKey(r.getProductName(), r.getSupplierName(),
                                r.getProductCode(), r.getSupplierCode())
                ));

        // 构建 ReviewTemplate 列表
        List<ReviewTemplate> reviewTemplates = grouped.entrySet().stream()
                .map(entry -> {
                    List<PricingReview> reviews = entry.getValue();
                    PricingReview first = reviews.get(0); // 取第一个作为基准

                    ReviewTemplate template = new ReviewTemplate();
                    template.setReviewProductName(first.getProductName());
                    template.setReviewProductCode(first.getProductCode());
                    template.setReviewSupplierName(first.getSupplierName());
                    template.setReviewSupplierContact(first.getSupplierContact());
                    template.setReviewFactorySpec(first.getFactorySpec());
                    template.setReviewPrice(first.getPrice());
                    template.setReviewInvoiceType(first.getInvoiceType());
                    template.setReviewPaymentMethod(first.getPaymentMethod());
                    template.setReviewHistoricalPurchasePrice(first.getHistoricalPurchasePrice());

                    // 拼接所有仓库的月用量信息
                    String monthlyUsageStr = buildMonthlyUsageString(reviews);
                    template.setReviewMonthlyUsage(monthlyUsageStr);

                    // 拼接所有仓库的库存信息
                    String stockStr = buildStockString(reviews);
                    template.setReviewStock(stockStr);

                    return template;
                })
                .sorted((t1, t2) -> {
                    // 根据货品名称、供应商名称排序
                    int productCompare = t1.getReviewProductName().compareTo(t2.getReviewProductName());
                    if (productCompare != 0) {
                        return productCompare;
                    }
                    return t1.getReviewSupplierName().compareTo(t2.getReviewSupplierName());
                })
                .toList();

        // 构建 InputData 列表
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<InputData> inputDatas = pricingReviews.stream()
                .map(r -> {
                    InputData inputData = new InputData();
                    inputData.setInputObjectId(r.getObjectId());
                    inputData.setInputProductName(r.getProductName());
                    inputData.setInputProductCode(r.getProductCode());
                    inputData.setInputExecuteStore(r.getExecuteStore());
                    inputData.setInputExecuteStoreCode(r.getExecuteStoreCode());
                    inputData.setInputSupplierName(r.getSupplierName());
                    inputData.setInputSupplierCode(r.getSupplierCode());
                    inputData.setInputFinalPrice(r.getFinalPrice());
                    inputData.setInputEnabled(r.getEnabled());
                    inputData.setInputPriceStartDate(r.getPriceStartDate() == null ? "" : df.format(r.getPriceStartDate()));
                    inputData.setInputPriceEndDate(r.getPriceEndDate() == null ? "" : df.format(r.getPriceEndDate()));
                    return inputData;
                })
                .sorted((i1, i2) -> {
                    // 根据货品名称、供应商名称、仓库名称排序
                    int productCompare = i1.getInputProductName().compareTo(i2.getInputProductName());
                    if (productCompare != 0) {
                        return productCompare;
                    }
                    int supplierCompare = i1.getInputSupplierName().compareTo(i2.getInputSupplierName());
                    if (supplierCompare != 0) {
                        return supplierCompare;
                    }
                    return i1.getInputExecuteStore().compareTo(i2.getInputExecuteStore());
                })
                .toList();

        dto.setReviewTemplates(reviewTemplates);
        dto.setInputDatas(inputDatas);

        return dto;
    }

    /**
     * 构建月用量字符串（包含合计）
     */
    private static String buildMonthlyUsageString(List<PricingReview> reviews) {
        List<PricingReview> validMonthlyUsageReviews = reviews.stream()
                .filter(r -> r.getMonthlyUsage() != null && r.getMonthlyUsage() > 0) // 过滤掉月用量为0的仓库
                .toList();

        String monthlyUsageStr = validMonthlyUsageReviews.stream()
                .map(r -> {
                    String storeName = r.getExecuteStore() != null ? r.getExecuteStore() : "未知门店";
                    storeName = storeName.replace("蜀海代仓", ""); // 去除仓库名称中的'蜀海代仓'
                    return storeName + " " + r.getMonthlyUsage();
                })
                .collect(Collectors.joining(System.lineSeparator()));

        // 计算月用量总计
        Float totalMonthlyUsage = validMonthlyUsageReviews.stream()
                .map(PricingReview::getMonthlyUsage)
                .reduce(0f, Float::sum);

        // 如果有多个门店且总计大于0，则添加合计行
        if (validMonthlyUsageReviews.size() > 1 && totalMonthlyUsage > 0) {
            monthlyUsageStr += System.lineSeparator() + "合计 " + totalMonthlyUsage;
        }

        return monthlyUsageStr;
    }

    /**
     * 构建库存字符串（包含合计和可用天数）
     */
    private static String buildStockString(List<PricingReview> reviews) {
        List<PricingReview> validStockReviews = reviews.stream()
                .filter(r -> r.getStock() != null && r.getStock() > 0) // 过滤掉库存为0的仓库
                .toList();

        String stockStr = validStockReviews.stream()
                .map(r -> {
                    String storeName = r.getExecuteStore() != null ? r.getExecuteStore() : "未知门店";
                    storeName = storeName.replace("蜀海代仓", ""); // 去除仓库名称中的'蜀海代仓'

                    Float useabledDay = 0f;
                    if (r.getMonthlyUsage() != null && r.getMonthlyUsage() != 0) {
                        useabledDay = r.getStock() / r.getMonthlyUsage() * 30;
                        // 保留一位小数
                        useabledDay = new BigDecimal(useabledDay)
                                .setScale(1, RoundingMode.HALF_UP)
                                .floatValue();
                    }
                    return storeName + " " + r.getStock() + " " + (useabledDay > 0 ? useabledDay : "");
                })
                .collect(Collectors.joining(System.lineSeparator()));

        // 计算库存总计和总月用量
        Float totalStock = validStockReviews.stream()
                .map(PricingReview::getStock)
                .reduce(0f, Float::sum);

        Float totalMonthlyUsageForStock = validStockReviews.stream()
                .filter(r -> r.getMonthlyUsage() != null && r.getMonthlyUsage() > 0)
                .map(PricingReview::getMonthlyUsage)
                .reduce(0f, Float::sum);

        // 如果有多个门店且总计大于0，则添加合计行
        if (validStockReviews.size() > 1 && totalStock > 0) {
            String totalLine = "合计 " + totalStock;

            // 计算总的可用天数
            if (totalMonthlyUsageForStock != null && totalMonthlyUsageForStock > 0) {
                Float totalUseabledDay = totalStock / totalMonthlyUsageForStock * 30;
                // 保留一位小数
                totalUseabledDay = new BigDecimal(totalUseabledDay)
                        .setScale(1, RoundingMode.HALF_UP)
                        .floatValue();
                totalLine += " " + totalUseabledDay;
            }

            stockStr += System.lineSeparator() + totalLine;
        }

        return stockStr;
    }
}
