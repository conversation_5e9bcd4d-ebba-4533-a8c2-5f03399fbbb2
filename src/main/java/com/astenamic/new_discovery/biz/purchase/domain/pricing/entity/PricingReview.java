package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 定价评审表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-798A769040F9476C91E51115C1A181D76UV1", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class PricingReview extends YidaObject {

    /**
     * 上期价格到期时间
     */
    @FormField("dateField_madqi4zy")
    private LocalDateTime lastEndDate;

    /**
     * 价格结束日期
     */
    @FormField("dateField_m9yx5cmw")
    private LocalDateTime priceEndDate;

    /**
     * 价格开始日期
     */
    @FormField("dateField_m9yx5cmv")
    private LocalDateTime priceStartDate;

    /**
     * 执行门店
     */
    @FormField("textField_m9yx5cmu")
    private String executeStore;

    /**
     * 是否启用
     */
    @FormField("radioField_m9yx5cmt")
    private String enabled;

    /**
     * 本次叫货量
     */
    @FormField("textField_m9yx5cmr")
    private String currentCallQuantity;

    /**
     * 单位
     */
    @FormField("textField_m9yx5cmq")
    private String unit;

    /**
     * 最终定价
     */
    @FormField("numberField_m9yx5cmp")
    private Float finalPrice;

    /**
     * 本次采购与历史价格比的降低金额
     */
    @FormField("numberField_m9yx5cmo")
    private Float reductionAmount;

    /**
     * 二次报价
     */
    @FormField("numberField_m9yx5cmk")
    private Float secondQuotation;

    /**
     * 付款方式
     */
    @FormField("textField_m9yx5cmn")
    private String paymentMethod;

    /**
     * 发票类型
     */
    @FormField("textField_m9mdevwx")
    private String invoiceType;

    /**
     * 初步报价
     */
    @FormField("numberField_m9yx5cmj")
    private Float price;

    /**
     * 样品测试表
     */
    @FormField("attachmentField_m9yx5cmi")
    private List<YidaFile> sampleTestForms;

    /**
     * 厂号及规格
     */
    @FormField("textField_m9yx5cmh")
    private String factorySpec;

    /**
     * 供应商联系方式
     */
    @FormField("textField_m9mdevx4")
    private String supplierContact;

    /**
     * 供应商名称
     */
    @FormField("textField_m9mdevww")
    private String supplierName;

    /**
     * 预估月采购额
     */
    @FormField("numberField_m9m2j59e")
    private Float estimatedMonthlyPurchase;

    /**
     * 当前执行价格
     */
    @FormField("numberField_m9m2j59d")
    private Float historicalPurchasePrice;


    /**
     * 货品编码
     */
    @FormField("textField_m6vsnv3i")
    private String productCode;

    /**
     * 货品名称
     */
    @FormField("textField_m6vtfht3")
    private String productName;

    /**
     * 定价单号
     */
    @FormField("textField_m9m4cq3e")
    private String pricingOrderNumber;

    /**
     * 评审项编码
     */
    @FormField("serialNumberField_maacmfbn")
    private String reviewOrderNumber;

    /**
     * 执行门店编码
     */
    @FormField("textField_ma38jl04")
    private String executeStoreCode;

    /**
     * 供应商编码
     */
    @FormField("textField_ma38hp1x")
    private String supplierCode;

    /**
     * 月用量
     */
    @FormField("numberField_mbhkosdd")
    private Float monthlyUsage;

    /**
     * 当前库存
     */
    @FormField("numberField_mbhkosdc")
    private Float stock;

    /**
     * 寻源认证编码
     */
    @FormField("textField_mag2sy8k")
    private String sourcingAuthCode;


    public PricingReview withExecution(String storeCode, String storeName) {
        PricingReview copy = new PricingReview();
        BeanUtils.copyProperties(this, copy);
        copy.setExecuteStoreCode(storeCode);
        copy.setExecuteStore(storeName);
        return copy;
    }

    public void validatePriceDates() {
        if (priceStartDate == null || priceEndDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        // 只比较日期，忽略时间
        LocalDate today = LocalDate.now();
        LocalDate startDate = priceStartDate.toLocalDate();
        LocalDate endDate = priceEndDate.toLocalDate();

        if (!endDate.isAfter(startDate)) {
            throw new IllegalArgumentException("价格结束日期必须大于价格开始日期");
        }
        if (startDate.isBefore(today)) {
            throw new IllegalArgumentException("价格开始日期必须大于等于当天日期");
        }
    }

}
