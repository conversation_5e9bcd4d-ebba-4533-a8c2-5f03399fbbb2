package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-343D911727E24762B3352A51F596DF9A4W93", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ShopInfo extends BaseEntity {

    /**
     * 门店 id
     */
    @FormField("numberField_ma7vu91t")
    private Integer id;

    /**
     * 组织中心 id
     */
    @FormField("textField_ma7vu91u")
    private String uuid;

    /**
     * 对接方唯一标识
     */
    @FormField("textField_ma7vu91z")
    private String csid;

    /**
     * 门店名称
     */
    @FormField("textField_ma7vu924")
    private String name;

    /**
     * 门店编码
     */
    @FormField("textField_ma7vu925")
    private String sno;

    /**
     * 门店地址
     */
    @FormField("textField_ma7vu926")
    private String shopadd;

    /**
     * 门店电话
     */
    @FormField("textField_ma7vu927")
    private String shoptel;

    /**
     * 门店类型（3-配送中心 4-直营店 5-加盟店 6-外销客户）
     */
    @FormField("selectField_ma7vu929")
    private String stype;

    public void setStype(String stype) {
        if ("3".equals(stype)) {
            this.stype = "配送中心";
        } else if ("4".equals(stype)) {
            this.stype = "直营店";
        } else if ("5".equals(stype)) {
            this.stype = "加盟店";
        } else if ("6".equals(stype)) {
            this.stype = "外销客户";
        } else {
            this.stype = stype;
        }
    }

    /**
     * 状态（1-有效，非 1-无效）
     */
    @FormField("selectField_ma7vu92b")
    private String status;

    public void setStatus(String status) {
        if (StringUtils.equals("1", status)) {
            this.status = "有效";
        } else {
            this.status = "无效";
        }
    }

    /**
     * 默认配送中心 id
     */
    @FormField("textField_ma7vu92f")
    private Integer delivery_id;

    /**
     * 默认配送中心名称
     */
    @FormField("textField_ma7vu92e")
    private String delivery_name;

    /**
     * 默认配送中心编码
     */
    @FormField("textField_ma7vu92g")
    private String delivery_sno;


    /**
     * 品牌 id
     */
    @FormField("textField_ma7vu92h")
    private Integer brand_id;

    /**
     * 品牌名称
     */
    @FormField("textField_ma7vu92i")
    private String brand_name;

    /**
     * 品牌编码
     */
    @FormField("textField_ma7vu92k")
    private String brand_sno;

    /**
     * 区域 id
     */
    @FormField("textField_ma7vu92l")
    private Integer area_id;

    /**
     * 区域名称
     */
    @FormField("textField_ma7vu92m")
    private String area_name;

    /**
     * 区域编码
     */
    @FormField("textField_ma7vu92o")
    private String area_sno;

    /**
     * 城市 id
     */
    @FormField("textField_ma7vu92p")
    private Integer city_id;

    /**
     * 城市名称
     */
    @FormField("textField_ma7vu92q")
    private String city_name;
}
