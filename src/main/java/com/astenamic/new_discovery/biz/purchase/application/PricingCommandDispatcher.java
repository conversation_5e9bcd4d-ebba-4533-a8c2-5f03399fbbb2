package com.astenamic.new_discovery.biz.purchase.application;

import com.astenamic.new_discovery.biz.purchase.application.command.PricingCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;


@Slf4j
@Service
public class PricingCommandDispatcher {

    /** RequestType → Command */
    private final Map<Class<?>, PricingCommand<?, ?>> registry = new HashMap<>();

    @Autowired
    public PricingCommandDispatcher(List<PricingCommand<?, ?>> commands) {
        for (PricingCommand<?, ?> cmd : commands) {
            try {
                Class<?> rqType = resolveRequestType(cmd);
                registry.put(rqType, cmd);
            } catch (Exception ex) {
                log.error("⚠ Unable to resolve request type for Command: {}", cmd.getClass().getName(), ex);
            }
        }
    }

    /* -------------------------------------------------- public API -------------------------------------------------- */

    @SuppressWarnings("unchecked")
    public <RQ, RS> RS dispatch(RQ request) {
        PricingCommand<RQ, RS> cmd = (PricingCommand<RQ, RS>) registry.get(request.getClass());
        if (cmd == null) {
            cmd = (PricingCommand<RQ, RS>) registry.entrySet().stream()
                    .filter(e -> e.getKey().isAssignableFrom(request.getClass()))
                    .map(Map.Entry::getValue)
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("No command registered for " + request.getClass()));
        }
        return cmd.execute(request);
    }

    private static Class<?> resolveRequestType(PricingCommand<?, ?> bean) {
        Class<?> target = AopUtils.getTargetClass(bean);  // 去掉代理
        while (target != null && target != Object.class) {
            Optional<Class<?>> hit = findInInterfaces(target);
            if (hit.isPresent()) return hit.get();
            target = target.getSuperclass();
        }
        throw new IllegalStateException("PricingCommand missing generic info: " + bean.getClass());
    }

    private static Optional<Class<?>> findInInterfaces(Class<?> cls) {
        for (Type t : cls.getGenericInterfaces()) {
            if (t instanceof ParameterizedType pt && pt.getRawType() == PricingCommand.class) {
                return Optional.of((Class<?>) pt.getActualTypeArguments()[0]);
            }
        }
        return Optional.empty();
    }
}
