package com.astenamic.new_discovery.biz.purchase.application.command;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;

import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.ExportPricingReviewV2Req;
import com.astenamic.new_discovery.biz.purchase.application.excel.ExcelConst;
import com.astenamic.new_discovery.biz.purchase.application.excel.InputMergeStrategy;
import com.astenamic.new_discovery.biz.purchase.application.excel.ReviewMergeStrategy;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateV2DTO;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.InputStream;


import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 导出定价评审表单命令V2
 * 1. 根据定价单号导出定价评审表单
 * 2. 在一个sheet中包含两个表：价格评审表 + 导入定价导入模板
 */
@Component
@RequiredArgsConstructor
public class ExportPricingReviewV2Cmd implements PricingCommand<ExportPricingReviewV2Req, Void> {

    private static final Logger logger = LoggerFactory.getLogger(ExportPricingReviewV2Cmd.class);
    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "导出定价评审表单", atMobiles = {"19353308172"})
    public Void execute(ExportPricingReviewV2Req req) {

        OutputStream outputStream = req.out();
        String pricingOrderNumber = req.pricingOrderNumber();

        List<PricingReview> reviews = ctx.getPricingReviewService().getByPricingOrderNumber(pricingOrderNumber);

        /* 样品测试表 URL 缓存 */
        Map<String, String> urlCache = new HashMap<>();
        reviews.forEach(r -> {
            if (r.getSampleTestForms() != null) {
                r.getSampleTestForms().forEach(file -> {
                    String u = file.getDownloadUrl();
                    if (u != null && !urlCache.containsKey(u)) {
                        urlCache.put(u, ctx.getYiDaSession()
                                .temporaryUrls(u, PricingReview.class));
                    }
                    file.setDownloadUrl(urlCache.get(u));
                });
            }
        });

        if (reviews.isEmpty()) {
            logger.warn("未找到定价评审表单，定价单号：{}", pricingOrderNumber);
            throw new BizException("未找到定价评审表单");
        }

        reviews.sort(Comparator
                .comparing(PricingReview::getProductName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(PricingReview::getSupplierName, Comparator.nullsFirst(Comparator.naturalOrder()))
                .thenComparing(PricingReview::getExecuteStoreCode, Comparator.nullsFirst(Comparator.naturalOrder()))
        );

        // 转换数据
        PricingReviewTemplateV2DTO dto = PricingReviewTemplateV2DTO.toDTO(reviews);

        // ========== 计算行号 ==========
        int reviewRows = dto.getReviewTemplates().size();
        int inputStart0 = (ExcelConst.REVIEW_START_ROW - 1)
                + reviewRows
                + ExcelConst.GAP_ROWS
                + ExcelConst.REVIEW_END_ROW
                + ExcelConst.INPUT_HEADER_ROWS;       // 0-base 行号

        // ========== 创建两个策略 ==========
        ReviewMergeStrategy reviewMerge = new ReviewMergeStrategy(dto.getReviewTemplates());
        InputMergeStrategy inputMerge = new InputMergeStrategy(dto.getInputDatas(), inputStart0);

        // 准备填充数据
        Map<String, Object> fillMap = new HashMap<>();
        fillMap.put("applicationDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        // ========== 写入文件 ==========
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try (InputStream template = resolver.getResource("classpath:templates/定价评审模版V2.xlsx")
                .getInputStream();
             ExcelWriter writer = EasyExcel.write(outputStream)
                     .withTemplate(template)
                     .registerWriteHandler(reviewMerge)
                     .registerWriteHandler(inputMerge)
                     .build()) {

            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            // 先填充基础数据
            writer.fill(fillMap, writeSheet);

            /* -------- 上半部分：价格评审明细 -------- */
            FillConfig cfg = FillConfig.builder().forceNewRow(true).build();
            writer.fill(new FillWrapper("reviewList", dto.getReviewTemplates()), cfg, writeSheet);

            /* -------- 下半部分：定价导入模板 -------- */
            writer.fill(new FillWrapper("inputList", dto.getInputDatas()), cfg, writeSheet);

            writer.finish();

        } catch (Exception e) {
            logger.error("导出定价评审表单失败：{}", e.getMessage(), e);
            throw new BizException("导出定价评审表单失败: " + e.getMessage());
        }

        return null;
    }
}
