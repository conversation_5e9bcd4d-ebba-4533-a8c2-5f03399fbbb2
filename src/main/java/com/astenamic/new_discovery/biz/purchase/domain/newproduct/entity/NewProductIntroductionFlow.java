package com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject.RawMaterialItem;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject.SourceAuthItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 新品引入流程
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-EBB41FDAC7A14AD89D485C3F727315DC7FFA", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class NewProductIntroductionFlow extends YidaFlowObject {

    /**
     * 需求单编码（隐藏）
     */
    @FormField("serialNumberField_m4assakr")
    private String requirementCode;

    /**
     * 流程状态（隐藏）
     */
    @FormField("selectField_ma3nxn3s")
    private String status;

    /**
     * 定价单号（隐藏）
     */
    @FormField("textField_m9mfym4m")
    private String priceListNumber;

    /**
     * 申请日期（隐藏）
     */
    @FormField("dateField_m4qolqcg")
    private LocalDateTime applicationDate;

    /**
     * 申请人（隐藏）
     */
    @FormField("employeeField_m4qolqca")
    private List<String> applicants;

    public void setApplicants(String applicants) {
        if (StringUtils.isNotBlank(applicants)) {
            this.applicants = List.of(applicants);
        }
    }

    /**
     * 申请部门（隐藏）
     */
    @FormField("departmentSelectField_m4qolqcf")
    private List<String> applyingDepartments;

    public void setApplyingDepartments(String applyingDepartments) {
        if (StringUtils.isNotBlank(applyingDepartments)) {
            this.applyingDepartments = List.of(applyingDepartments);
        }
    }

    /**
     * 需求日期
     */
    @FormField("dateField_m4qolqch")
    private LocalDateTime demandDate;

    /**
     * 是否新货品
     */
    @FormField("radioField_m9jculni")
    private String isNewProduct;

    /**
     * 货品名称
     */
    @FormField("textField_m6vtfht3")
    private String productName;

    /**
     * 已有货品（关联）
     */
    @FormField("associationFormField_m56zbxgr")
    private List<YidaAssociation> existingProducts;

    /**
     * 货品大类（关联）
     */
    @FormField("associationFormField_m9mirv5k")
    private List<YidaAssociation> itemCategory;

    /**
     * 货品小类（关联）
     */
    @FormField("associationFormField_m9mirv5l")
    private List<YidaAssociation> itemSubcategory;

    /**
     * 需求单位
     */
    @FormField("selectField_m4qolqct")
    private String demandUnit;

    /**
     * 月度需求数量
     */
    @FormField("numberField_m4qolqcs")
    private Float monthlyDemandQuantity;

    /**
     * 需求规格/型号
     */
    @FormField("textField_m4qolqcu")
    private String demandSpecificationOrModel;

    /**
     * 需求风味/风格
     */
    @FormField("textField_m4qolqd0")
    private String demandFlavorOrStyle;

    /**
     * 建议品牌/厂家
     */
    @FormField("textField_m4qolqci")
    private String suggestedBrandOrManufacturer;

    /**
     * 建议产地
     */
    @FormField("textField_m4qolqcz")
    private String suggestedOrigin;

    /**
     * 需求标准
     */
    @FormField("textareaField_m9jbmpfk")
    private String demandStandard;

    /**
     * 申请说明
     */
    @FormField("textareaField_m4assakn")
    private String applicationNotes;

    /**
     * 推荐样品图片
     */
    @FormField("imageField_m4qolqd1")
    private List<YidaFile> recommendedSampleImages;

    /**
     * 定价类型
     */
    @FormField("selectField_ma3izcyt")
    private String pricingType;

    /**
     * 寻源员
     */
    @FormField("employeeField_m9jmyfgz")
    private List<String> sourcingAgents;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

    /**
     * 审批意见
     */
    @FormField("textareaField_m4qolqd6")
    private String approvalComments;

    /**
     * 原料表（子表单）
     */
    @FormField("tableField_ma6inwm4")
    private List<RawMaterialItem> rawMaterialTable;

    /**
     * 货品大类ID（隐藏）
     */
    @FormField("numberField_m56zbxgx")
    private Float itemCategoryId;

    /**
     * 货品小类ID（隐藏）
     */
    @FormField("numberField_m56zbxgy")
    private Float itemSubcategoryId;

    /**
     * 货品编码（隐藏）
     */
    @FormField("textField_m6vsnv3i")
    private String productCode;


    @FormField("tableField_madbkufb")
    private List<SourceAuthItem> sourceAuthTable;
}

