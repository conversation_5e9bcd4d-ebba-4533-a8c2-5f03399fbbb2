package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.util.List;

/**
 * 市调员表-行信息
 */
@Data
@FormEntity("tableField_m5bxvpci")
public class MarketResearcherInfo {
    /**
     * 货品名称
     */
    @FormField("textField_m9yy35iy")
    private String productName;

    /**
     * 市调员
     */
    @FormField("employeeField_m66a7mg9")
    private List<String> researchers;

    public void setResearchers(String researchers) {
        if (researchers != null && !researchers.isEmpty()) {
            this.researchers = List.of(researchers);
        }
    }

}
