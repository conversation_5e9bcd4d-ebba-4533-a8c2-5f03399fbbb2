package com.astenamic.new_discovery.biz.purchase.domain.pricing.service;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductSupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.SupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.enums.InstanceStatusEnum;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 货品定价流程服务
 */
@Slf4j
@Service
public class ProductPricingFlowService extends AbstractFlowService<ProductPricingFlow> {

    @Autowired
    private ProductSupplierQuotationFlowService productSupplierQuotationFlowService;



    public ProductPricingFlowService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(ProductPricingFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据需求单号查询货品定价流程
     *
     * @param requirementCode 需求单号
     * @return 定价流程实体
     */
    public ProductPricingFlow getByRequirementCode(String requirementCode) {

        SearchConditions cond = SearchCondition.builder()
                .textEq("textField_m9kn3ybq", requirementCode, "+")
                .get();
        return super.getFlowByCond(cond);

    }

    /**
     * 根据定价单号查询货品定价流程
     *
     * @param pricingOrderNumber 定价单号
     * @return 定价流程实体
     */
    public ProductPricingFlow getByPricingOrderNumber(String pricingOrderNumber) {

        SearchConditions cond = SearchCondition.builder()
                .textEq("serialNumberField_m9kn3ybo", pricingOrderNumber, "+")
                .get();
        return super.getFlowByCond(cond);

    }

    /**
     * 更新货品定价流程
     */
    public void updateFlow(ProductPricingFlow flow) {
        try {
            yiDaSession.batchUpdateDataByInstanceId(List.of(flow), ProductPricingFlow.class);
        } catch (Exception e) {
            log.error("更新货品定价流程失败，实例ID：{}，错误信息：{}", flow.getInstanceId(), e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
    }

    /**
     * 校验流程机器人是否可以通过
     */
    public boolean checkFlowRobotPass(String pricingOrderNumber) {
        // 查询供应商准入流程
        List<ProductSupplierQuotationFlow> supplierQuotationFlows = productSupplierQuotationFlowService.getFlowsByPricingOrderNumber(pricingOrderNumber);
        return supplierQuotationFlows.isEmpty() || supplierQuotationFlows.stream()
                .map(ProductSupplierQuotationFlow::getInstanceStatus)
                .noneMatch(InstanceStatusEnum.RUNNING.getValue()::equals);
    }

    /**
     * 查询子表单
     */
    public List<ProductConfirmationItem> queryProductConfirmationList(String objectId) {

        List<ProductConfirmationItem> result = new ArrayList<>();

        int page = 1;
        int pageSize = 50;
        int size = 0;

        do {
            List<ProductConfirmationItem> list = new ArrayList<>();
            try {
                list = this.yiDaSession.queryInnerTables(
                        ProductPricingFlow.class,
                        ProductConfirmationItem.class,
                        objectId,
                        page,
                        pageSize
                );
            } catch (Exception e) {
                log.error("查询货品确认表: {}", e.getMessage(), e);
                throw e;
            }
            result.addAll(list);
            size = list.size();
            page++;
        } while (size == pageSize);

        return result;
    }
}