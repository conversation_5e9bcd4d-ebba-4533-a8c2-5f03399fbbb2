package com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 寻源认证列表-行实体
 */
@Data
@FormEntity("tableField_madbkufb")
public class SourceAuthItem {

    /**
     * 寻源认证（关联）
     */
    @FormField("associationFormField_madbkuff")
    private List<YidaAssociation> sourceAuthentication;

    /**
     * 寻源员
     */
    @FormField("employeeField_m9i39v9e")
    private List<String> sourcingAgents;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

    /**
     * 供应商名称
     */
    @FormField("textField_m6yrlpsa")
    private String supplierName;

    /**
     * 供应商初步报价
     */
    @FormField("numberField_m6yrlpsl")
    private Float initialQuote;

    /**
     * 是否新供应商
     */
    @FormField("radioField_maewqo9n")
    private String isNewSupplier;

    /**
     * 单位
     */
    @FormField("selectField_m9qj295w")
    private String unit;

    /**
     * 审批结果
     */
    @FormField("selectField_madbkufg")
    private String approvalResult;

    /**
     * 寻源认证编码（隐藏）
     */
    @FormField("textField_madbkufc")
    private String sourceAuthCode;
}
