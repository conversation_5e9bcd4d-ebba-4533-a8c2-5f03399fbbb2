package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity(value = "FORM-63171A138522470084A0BBE1E5A7826DIXB6", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductInfo extends BaseEntity {

    // aceid主键
    @FormField("numberField_m56z0sqc")
    private Integer id;
    // 货品名称
    @FormField("textField_m56z0sqd")
    private String name;
    // 货品状态 1-有效 0-无效 2-冻结
    @FormField("textField_m56z0sqe")
    private String status;
    // 货品规格
    @FormField("textField_m56z0sqf")
    private String std;
    // 货品编码
    @FormField("textField_m56z0sqg")
    private String sno;
    // 货品小类ID
    @FormField("numberField_m56z0sqk")
    private Integer goodtype_id;
    // 	货品大类ID
    @FormField("numberField_m56z0sqj")
    private Integer fgoodtype_id;
    // 货品大类
    @FormField("textField_m56z0sql")
    private String fgoodtype_name;
    // 货品小类
    @FormField("textField_m56z0sqm")
    private String goodtype_name;
    //核算单位
    @FormField("textField_m9uzq22x")
    private String baseunit_name;
    // 订货单位
    @FormField("textField_m9uzq22y")
    private String applyunit_name;
    // 成本单位
    @FormField("textField_m9uzq22z")
    private String costunit_name;
    // 库存单位
    @FormField("textField_m9uzq230")
    private String unit_Name;
    // 盘点单位
    @FormField("textField_m9uzq231")
    private String checkunit_name;
    // 订货系数
    @FormField("numberField_m9uzq232")
    private Float applycode;
    // 成本系数
    @FormField("numberField_m9uzq233")
    private Float costcode;
    // 库存系数
    @FormField("numberField_m9uzq234")
    private Float stockcode;
    // 盘点系数
    @FormField("numberField_m9uzq235")
    private Float checkcode;
    // 含税单价
    @FormField("textField_m9uzq237")
    private String applyunit;
    // 物流属性  1-直送 2-直通 3-配送 4-虚拟直通
    @FormField("textField_m9uzq238")
    private String logisticstype;
    // 货品类型 1-原材料 2-半成品 3-粗加工
    @FormField("textField_m9uzq239")
    private String type;

    // 生鲜/采集
    @FormField("selectField_m5buxpkb")
    private String goodPricingType;

    @FormField("selectField_mani1p4z")
    private String goodPricingCategory;

    @FormField("textField_maertv88")
    private String isFrozen;

    public void setStatus(String status) {
        if ("1".equals(status)) {
            this.status = "有效";
        } else if ("0".equals(status)) {
            this.status = "无效";
        } else if ("2".equals(status)) {
            this.status = "冻结";
        } else {
            this.status = status;
        }
    }

    public void setType(String type) {
        if ("1".equals(type)) {
            this.type = "原材料";
        } else if ("2".equals(type)) {
            this.type = "半成品";
        } else if ("3".equals(type)) {
            this.type = "粗加工";
        } else {
            this.type = type;
        }
    }

    public void setLogisticstype(String logisticstype) {
        if ("1".equals(logisticstype)) {
            this.logisticstype = "直送";
        } else if ("2".equals(logisticstype)) {
            this.logisticstype = "直通";
        } else if ("3".equals(logisticstype)) {
            this.logisticstype = "配送";
        } else if ("4".equals(logisticstype)) {
            this.logisticstype = "虚拟直通";
        } else {
            this.logisticstype = logisticstype;
        }
    }

}
