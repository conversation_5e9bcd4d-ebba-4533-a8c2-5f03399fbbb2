package com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto;

public interface StockSummaryDTO {


    /**
     * 获取物料编码
     *
     * @return 物料编号，对应 xfx_material_stock.material_no
     */
    String getLgcode();

    /**
     * 获取物料名称
     *
     * @return 物料名称，对应 xfx_material_stock.material_name
     */
    String getLgname();

    /**
     * 获取配送中心 ID
     *
     * @return 配送中心标识（根据 company_name 转换得来）
     */
    String getSlsid();

    /**
     * 获取配送中心名称
     *
     * @return 配送中心名称（根据 company_name 转换得来）
     */
    String getSlsname();

    /**
     * 获取当前库存总量
     *
     * @return 库存数量，为 available_stock + shelves_stock + lock_stock + replenishment_stock 的汇总
     */
    Float getStock();

    /**
     * 获取单位名称
     *
     * @return 单位名称
     */
    String getUnitName();
}
