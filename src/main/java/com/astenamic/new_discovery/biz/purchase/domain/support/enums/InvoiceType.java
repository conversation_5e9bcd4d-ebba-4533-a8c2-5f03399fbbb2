package com.astenamic.new_discovery.biz.purchase.domain.support.enums;


import lombok.Getter;

/**
 * 发票种类枚举
 */
@Getter
public enum InvoiceType {

    /** 1 - 增值税专用发票 */
    VAT_SPECIAL(1, "增值税专用发票"),

    /** 2 - 农副产品收购发票 */
    FARM_PURCHASE(2, "农副产品收购发票"),

    /** 3 - 农产品增值税普通发票 */
    VAT_AGRICULTURAL_NORMAL(3, "农产品增值税普通发票"),

    /** 4 - 增值税普通发票 */
    VAT_NORMAL(4, "增值税普通发票"),

    /** 5 - 无发票 */
    NO_INVOICE(5, "无发票");

    // ===== 字段 =====
    private final int code;
    private final String desc;

    InvoiceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // ===== 根据 code 反查 =====
    public static InvoiceType of(int code) {
        for (InvoiceType t : values()) {
            if (t.code == code) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知发票种类 code = " + code);
    }

    // ===== 根据 desc 反查 =====
    public static InvoiceType ofDesc(String desc) {
        for (InvoiceType t : values()) {
            if (t.desc.equals(desc)) {
                return t;
            }
        }
        throw new IllegalArgumentException("未知发票种类 desc = " + desc);
    }
}
