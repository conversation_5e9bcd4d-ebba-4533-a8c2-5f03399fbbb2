package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.InvoiceType;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.SettlementType;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.SupplierAttribute;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.SupplierType;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-36669FE6C920493CB02BF0BFE1FAFAEEF41R", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class SupplierInfo extends BaseEntity {

    // 供应商ID
    @FormField("textField_m9wbxw21")
    private Integer lspid;

    // 对接唯一标识:客户供应商ID
    @FormField("textField_m9wbxw22")
    private String clspid;

    @FormField("textField_m9j9x033")
    private String name;

    @FormField("textField_m9j9x034")
    private String sno;

    @FormField("textField_m9v0zxvj")
    private String contact;

    @FormField("textField_m9w4wo95")
    private String mobile;

    private String phone;

    //1有效，0无效
    @FormField("selectField_m9j9x03i")
    private String valid;

    public void setValid(String valid) {
        if ("1".equals(valid)) {
            this.valid = "有效";
        } else if ("0".equals(valid)) {
            this.valid = "无效";
        } else {
            this.valid = valid;
        }
    }

    // 发票
    @FormField("textField_m9wbxw1j")
    private String binvoice;

    // 结算方式
    @FormField("selectField_m9wbxw1q")
    private String accountdatetype;

    public void setAccountdatetype(String accountdatetype) {
        try {
            this.accountdatetype = SettlementType.of(Integer.parseInt(accountdatetype)).getDesc();
        } catch (Exception e) {
            this.accountdatetype = accountdatetype;
        }
    }

    //发票种类
    @FormField("selectField_m9wbxw1p")
    private String invoicetype;

    public void setInvoicetype(String invoicetype) {
        try {
            this.invoicetype = InvoiceType.of(Integer.parseInt(invoicetype)).getDesc();
        } catch (Exception e) {
            this.invoicetype = invoicetype;
        }
    }

    //供应商类型:1外部供应商 2 内部供应商
    @FormField("selectField_m9wbxw1r")
    private String type;

    public void setType(String type) {
        try {
            this.type = SupplierType.of(Integer.parseInt(type)).getDesc();
        } catch (Exception e) {
            this.type = type;
        }
    }

    //	供应商属性:0 普通供应商 1 门店自购供应商 2 总部代采供应商
    @FormField("selectField_m9wbxw1w")
    private String bselfpur;

    public void setBselfpur(String bselfpur) {
        try {
            this.bselfpur = SupplierAttribute.of(Integer.parseInt(bselfpur)).getDesc();
        } catch (Exception e) {
            this.bselfpur = bselfpur;
        }
    }

    //注册资金
    @FormField("numberField_mad8gahd")
    private Float registeredCapital;

    //年销售总额
    @FormField("numberField_mad8gahc")
    private Float annualTotalSales;

    //现有合作单位
    @FormField("textField_m9v0zxvq")
    private String existingCooperativeUnits;

    @FormField("dateField_m9j9x03f")
    private LocalDateTime contractBeginDate;

    @FormField("dateField_m9j9x03h")
    private LocalDateTime contractEndDate;

    @FormField("attachmentField_m9j9x03l")
    private List<YidaFile> businessLicenseFiles;

    /**
     * 食品经营许可证
     */
    @FormField("attachmentField_m9j9x03n")
    private List<YidaFile> foodBusinessLicenseFiles;

    /**
     * 食品经营许可证到期时间
     */
    @FormField("dateField_m9jl36yx")
    private LocalDateTime foodBusinessLicenseFilesUpdateTime;

    /**
     * 食品生产许可证
     */
    @FormField("attachmentField_m9j9x03m")
    private List<YidaFile> foodProductionLicenseFiles;

    /**
     * 食品生产许可证到期时间
     */
    @FormField("dateField_m9jl36yz")
    private LocalDateTime foodProductionLicenseFilesUpdateTime;

    /**
     * 其它证件
     */
    @FormField("attachmentField_m9v0zxw2")
    private List<YidaFile> otherFiles;

    /**
     * 寻源员
     */
    @FormField("employeeField_m9v0zxw3")
    private List<String> applyPeople;


    public void setApplyPeople(String applyPeople) {
        if (StringUtils.isNotBlank(applyPeople)) {
            this.applyPeople = List.of(applyPeople);
        }
    }


}
