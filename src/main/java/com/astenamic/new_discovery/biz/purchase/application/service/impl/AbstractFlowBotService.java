package com.astenamic.new_discovery.biz.purchase.application.service.impl;

import com.aliyun.dingtalkyida_1_0.models.GetOperationRecordsResponseBody;
import com.astenamic.new_discovery.biz.purchase.application.service.FlowBotService;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.enums.OutResultEnum;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class AbstractFlowBotService<T extends YidaFlowObject> implements FlowBotService<T> {

    protected final Class<T> flowClass;

    protected final YiDaSession yiDaSession;

    protected final YidaConfigProperties yidaConfigProperties;

    protected AbstractFlowBotService(Class<T> flowClass, YiDaSession yiDaSession, YidaConfigProperties yidaConfigProperties) {
        this.flowClass = flowClass;
        this.yiDaSession = yiDaSession;
        this.yidaConfigProperties = yidaConfigProperties;
    }

    /**
     * 校验机器人是否可执行
     */
    protected abstract boolean checkFlowRobotPass(String flowCode);

    /**
     * 根据编码获取流程实例
     */
    protected abstract T getFlowByCode(String flowCode);


    protected Class<T> getFlowClass() {
        return flowClass;
    }

    /**
     * 执行机器人任务
     *
     * @param flowCode
     */
    @Override
    @DingTalkAlert(title = "流程机器人自动审批", atMobiles = {"19353308172"})
    public void executeTask(String flowCode) {
        if (checkFlowRobotPass(flowCode)) {

            T flow = getFlowByCode(flowCode);

            if (flow == null) {
                log.warn("未找到流程实例，编码：{}", flowCode);
                throw new BizException(Result.ErrorCode.FLOW_NOT_FOUND);
            }

            List<GetOperationRecordsResponseBody.GetOperationRecordsResponseBodyResult> records = yiDaSession.operationRecords(
                    getFlowClass(),
                    flow.getInstanceId(),
                    yidaConfigProperties.getYidaBot().getFlowBot()
            );

            Optional<GetOperationRecordsResponseBody.GetOperationRecordsResponseBodyResult> toExecuteOpt = records.stream()
                    .filter(r -> "doing".equalsIgnoreCase(r.getActionExit())
                            && yidaConfigProperties.getYidaBot().getFlowBot().equals(r.getOperatorUserId()))
                    .findFirst();

            if (toExecuteOpt.isEmpty()) {
                log.info("无可执行的机器人任务，编码：{}", flowCode);
                throw new BizException(Result.ErrorCode.TASK_NOT_FOUND);
            }

            GetOperationRecordsResponseBody.GetOperationRecordsResponseBodyResult record = toExecuteOpt.get();
            try {
                yiDaSession.executeTask(
                        flow,
                        getFlowClass(),
                        flow.getInstanceId(),
                        yidaConfigProperties.getYidaBot().getFlowBot(),
                        Long.valueOf(record.getTaskId()),
                        OutResultEnum.AGREE.getCode()
                );
                log.info("执行机器人任务成功，任务ID：{}", record.getTaskId());
            } catch (Exception e) {
                log.error("执行机器人任务失败，任务ID：{}，错误信息：{}", record.getTaskId(), e.getMessage(), e);
                throw new BizException(Result.ErrorCode.TASK_ERROR);
            }
        }
    }

}
