package com.astenamic.new_discovery.biz.purchase.domain.support.service;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductInfo;
import com.astenamic.new_discovery.ace.scm.productmanage.api.ProductSession;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Slf4j
@Service
public class ProductInfoService extends AbstractFormService<ProductInfo> {

    private final ProductSession productSession;

    public ProductInfoService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties, ProductSession productSession) {
        super(ProductInfo.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
        this.productSession = productSession;
    }


    @ScheduledTask(name = "货品信息同步", cron = "0 0 1 * * ?")
    public void syncProductInfoYida() {
        List<ProductInfo> newData = this.productSession.getProductList(20);
        this.getFormsByCond(SearchCondition.builder().get()).forEach(old -> {
            newData.forEach(newShop -> {
                if (old.getSno().equals(newShop.getSno())) {
                    newShop.setObjectId(old.getObjectId());
                    newShop.setGoodPricingType(old.getGoodPricingType());
                    newShop.setGoodPricingCategory(old.getGoodPricingCategory());
                }
            });
        });
        super.batchSave(newData);
    }

    public Map<String, ProductInfo> getProductInfoIn(Set<String> goodCodes) {
        if (goodCodes == null || goodCodes.isEmpty()) {
            return Map.of();
        }
        Map<String, ProductInfo> result = new HashMap<>();
        goodCodes.forEach(goodSno -> {
            try {
                ProductInfo info = getProductInfo(goodSno);
                if (info != null) {
                    result.put(goodSno, info);
                }
            } catch (Exception e) {
                log.warn("获取商品信息失败: {}", goodSno, e);
            }
        });
        return result;
    }

    public ProductInfo getProductInfo(String goodCode) {
        if (StringUtils.isBlank(goodCode)) {
            return null;
        }
        SearchConditions cond = SearchCondition.builder().textEq(ProductInfo.class, ProductInfo::getSno, goodCode, "+").get();
        return this.getFormByCond(cond);
    }

    public List<ProductInfo> getProductInfoByCategory(String category) {
        if (StringUtils.isBlank(category)) {
            return List.of();
        }
        SearchConditions cond = SearchCondition.builder()
                .textEq(ProductInfo.class, ProductInfo::getGoodPricingCategory, category, "+")
                .textEq(ProductInfo.class, ProductInfo::getGoodPricingType, "集采", "+")
                .get();
        return this.getFormsByCond(cond);
    }
}
