package com.astenamic.new_discovery.biz.purchase.domain.newproduct.service;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SupplierAdmissionFlow;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商准入流程服务
 */
@Slf4j
@Service
public class SupplierAdmissionFlowService extends AbstractFlowService<SupplierAdmissionFlow> {

    public SupplierAdmissionFlowService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(SupplierAdmissionFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据需求单号查询供应商准入流程
     */
    public List<SupplierAdmissionFlow> getFlowsByRequirementOrderNumber(String requirementOrderNumber) {
        SearchConditions conf = SearchCondition.builder()
                .textEq("textField_m9za4j43", requirementOrderNumber, "+")
                .get();
        return super.getFlowsByCond(conf);
    }

}