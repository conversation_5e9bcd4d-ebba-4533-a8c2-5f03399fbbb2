package com.astenamic.new_discovery.biz.purchase.domain.support.service;

import com.astenamic.new_discovery.ace.scm.suppliermanage.api.SupplierSession;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierInfo;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SupplierInfoService extends AbstractFormService<SupplierInfo> {

    private final SupplierSession supplierSession;

    public SupplierInfoService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties, SupplierSession supplierSession) {
        super(SupplierInfo.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
        this.supplierSession = supplierSession;
    }

    @ScheduledTask(name = "供应商信息同步", cron = "0 0 1 * * ?")
    public void syncSupplierToYida() {
        List<SupplierInfo> newData = supplierSession.getSupplierList(100);
        List<SupplierInfo> oldData = super.getFormsByCond(SearchCondition.builder().get());
        if (!oldData.isEmpty()) {
            for (SupplierInfo old : oldData) {
                for (SupplierInfo newSupplier : newData) {
                    if (old.getSno().equals(newSupplier.getSno())) {
                        newSupplier.setObjectId(old.getObjectId());
                        newSupplier.setAccountdatetype(old.getAccountdatetype());
                        newSupplier.setInvoicetype(old.getInvoicetype());
                        break;
                    }
                }
            }
        }
        super.batchSave(newData);
    }

    public SupplierInfo getSupplierBySno(String sno) {
        if (StringUtils.isEmpty(sno)) {
            log.warn("供应商编号不能为空");
            return null;
        }
        List<SupplierInfo> suppliers = super.getFormsByCond(SearchCondition.builder().textEq(SupplierInfo.class, SupplierInfo::getSno, sno, "+").get());
        if (suppliers.isEmpty()) {
            return null;
        }
        return suppliers.get(0);
    }

}
