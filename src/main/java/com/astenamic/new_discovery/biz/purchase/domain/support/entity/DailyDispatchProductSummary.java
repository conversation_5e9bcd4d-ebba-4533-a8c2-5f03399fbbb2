package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 实体类：日度配送货品汇总
 */
@Data
@Entity(name = "xfx_lgt_dispatchout")
public class DailyDispatchProductSummary {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配送中心ID
     */
    @Column(name = "slsid")
    private Integer slsId;

    /**
     * 配送中心
     */
    @Column(name = "slsname", length = 50)
    private String slsName;

    /**
     * 配送中心编码
     */
    @Column(name = "slscode", length = 55)
    private String slsCode;

    /**
     * 货品ID
     */
    @Column(name = "lgid")
    private Integer lgId;

    /**
     * 货品名称
     */
    @Column(name = "lgname", length = 100)
    private String lgName;

    /**
     * 审核日期
     */
    @Column(name = "review_date")
    private LocalDate reviewDate;

    /**
     * 配送数量
     */
    @Column(name = "ps_qty", precision = 34, scale = 4)
    private BigDecimal psQty;

    /**
     * 货品编码
     */
    @Column(name = "lgcode", length = 55)
    private String lgCode;

    /**
     * 货品库存单位
     */
    @Column(name = "unit", length = 55)
    private String unit;

    @Column(name = "dh_qty", precision = 34, scale = 4)
    private BigDecimal dhQty;

    @Column(name = "dh_unit", length = 55)
    private String dhUnit;


}
