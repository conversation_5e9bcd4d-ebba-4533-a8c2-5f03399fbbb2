package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.SyncPricingOrderReq;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingSheet;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.PriceCreateRequestDTO;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.PriceCreateResponseDTO;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 同步定价单命令
 * 1. 根据定价单号查询定价单
 * 2. 将定价单转换为供应商定价请求格式
 * 3. 调用供应商定价服务同步定价单
 * 4. 更新定价单同步状态和错误信息
 * 5. 保存定价单
 */
@Component
@RequiredArgsConstructor
public class SyncPricingOrderCmd implements
        PricingCommand<SyncPricingOrderReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(SyncPricingOrderCmd.class);
    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "同步定价单", atMobiles = {"19353308172"})
    public Void execute(SyncPricingOrderReq req) {

        String code = req.code();
        ProductPricingSheet sheet =
                ctx.getProductPricingSheetService().getByThirdPartyCode(code);
        if (sheet == null) {
            throw new BizException(Result.ErrorCode.FORM_NOT_FOUND);
        }

        PriceCreateRequestDTO dto = new PriceCreateRequestDTO();
        BeanUtils.copyProperties(sheet, dto);
        List<PriceCreateRequestDTO.ItemDTO> items = sheet.getItems().stream().map(it -> {
            PriceCreateRequestDTO.ItemDTO d = new PriceCreateRequestDTO.ItemDTO();
            BeanUtils.copyProperties(it, d);
            d.setStartdate(it.getStartdate().toLocalDate());
            d.setEnddate(it.getEnddate().toLocalDate());
            return d;
        }).toList();
        dto.setItems(items);
        dto.setCuser_sno("72");
        dto.setAuser_sno("72");
        dto.setType(0);
        dto.setAuto_audit(0);

        try {
            PriceCreateResponseDTO resp =
                    ctx.getSupplierPricingSession().addSupplierPrice(dto);
            if (resp != null && Boolean.TRUE.equals(resp.getSuccess())) {
                sheet.setSyncStatus("同步成功");
                sheet.setAceCode(resp.getData().getCode());
            } else {
                sheet.setSyncStatus("同步失败");
                sheet.setFailReason(resp == null ? "响应为空" : resp.getMsg());
            }
        } catch (Exception e) {
            logger.error("同步定价单失败：{}", code, e);
            sheet.setSyncStatus("同步失败");
            sheet.setFailReason(e.getMessage());
        }

        ctx.getProductPricingSheetService().batchSave(List.of(sheet));
        return null;
    }
}
