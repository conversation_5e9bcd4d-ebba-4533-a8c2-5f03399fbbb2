package com.astenamic.new_discovery.biz.purchase.domain.support.repository;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPriceCache;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ProductPriceCacheRepository extends JpaRepository<ProductPriceCache, Long> {

    List<ProductPriceCache> findByInstanceId(String instanceId);

}
