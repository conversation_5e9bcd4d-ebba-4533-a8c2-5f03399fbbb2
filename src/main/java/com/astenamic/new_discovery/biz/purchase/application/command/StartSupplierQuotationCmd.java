package com.astenamic.new_discovery.biz.purchase.application.command;

import com.alibaba.fastjson2.JSON;
import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.StartSupplierQuotationReq;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.SupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.enums.PricingType;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.QuotationItem;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierProductBinding;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.InvoiceType;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.SettlementType;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import com.astenamic.new_discovery.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 开始供应商报价流程命令
 * 1. 根据定价单号查询定价流程
 * 2. 查询产品确认单列表
 * 3. 检查产品代码与供应商绑定关系
 * 4. 按供应商分组生成报价流程
 * 5. 保存报价流程
 */
@Component
@RequiredArgsConstructor
public class StartSupplierQuotationCmd implements PricingCommand<StartSupplierQuotationReq, Void> {

    private static final Logger log = LoggerFactory.getLogger(StartSupplierQuotationCmd.class);
    private final ProductPricingContext ctx;

    @Override
    @DingTalkAlert(title = "开始供应商报价流程", atMobiles = {"19353308172"})
    public Void execute(StartSupplierQuotationReq req) {

        String pricingOrderNumber = req.pricingOrderNumber();

        ProductPricingFlow productPricingFlow =
                ctx.getProductPricingFlowService().getByPricingOrderNumber(pricingOrderNumber);

        if (productPricingFlow == null) {
            log.warn("未找到定价流程，定价单号：{}", pricingOrderNumber);
            throw new BizException("未找到定价流程");
        }

        List<ProductConfirmationItem> confirmations =
                ctx.getProductPricingFlowService()
                        .queryProductConfirmationList(productPricingFlow.getInstanceId());
        if (confirmations.isEmpty()) {
            throw new BizException("定价流程中没有产品确认单：" + pricingOrderNumber);
        }

        List<String> itemCodes = confirmations.stream()
                .map(ProductConfirmationItem::getItemCode)
                .distinct()
                .toList();
        List<SupplierProductBinding> bindings =
                ctx.getBindingRepo().findByProductCodeIn(itemCodes);

        // 差异检查
        List<String> diff = itemCodes.stream()
                .filter(c -> bindings.stream().map(SupplierProductBinding::getProductCode)
                        .noneMatch(c::equals))
                .toList();
        log.error("开启供应商报价的货品差异情况：{}", JSON.toJSONString(diff));

        // 按供应商分组
        Map<String, List<SupplierProductBinding>> bindsBySupplierCode =
                bindings.stream()
                        .collect(Collectors.groupingBy(SupplierProductBinding::getSupplierCode));

        // 新品排除
        if (StringUtils.equals(productPricingFlow.getPricingType(),
                PricingType.NEW_PRODUCT.getDesc())) {
            List<String> exclude = ctx.getSourceAuthService()
                    .getByRequirementCode(productPricingFlow.getRequirementCode()).stream()
                    .map(a -> a.getSupplierCode())
                    .distinct()
                    .toList();
            bindsBySupplierCode.keySet().removeIf(exclude::contains);
        }

        // 组装 SupplierQuotationFlow
        List<SupplierQuotationFlow> flows = bindsBySupplierCode.values().stream()
                .map(binds -> {
                    SupplierProductBinding sample = binds.get(0);
                    SupplierQuotationFlow sq = new SupplierQuotationFlow();
                    sq.setPricingOrderNumber(pricingOrderNumber);
                    sq.setSupplierName(sample.getSupplierName());
                    sq.setSupplierCode(sample.getSupplierCode());
                    sq.setSupplierContact(sample.getSupplierMobile());
                    sq.setSourcingAgents(productPricingFlow.getSourcingAgents().get(0));

                    if (StringUtils.isNotBlank(sample.getAccountdatetype())) {
                        try {
                            sq.setAccountdatetype(SettlementType
                                    .of(Integer.parseInt(sample.getAccountdatetype()))
                                    .getDesc());
                        } catch (Exception e) {
                            log.error("设置结算方式失败！", e);
                            sq.setAccountdatetype(sample.getAccountdatetype());
                        }
                    }
                    if (StringUtils.isNotBlank(sample.getInvoicetype())) {
                        try {
                            sq.setInvoicetype(InvoiceType
                                    .of(Integer.parseInt(sample.getInvoicetype()))
                                    .getDesc());
                        } catch (Exception e) {
                            log.error("设置发票类型失败！", e);
                            sq.setInvoicetype(sample.getInvoicetype());
                        }
                    }

                    // 报价行
                    Set<String> codes = binds.stream()
                            .map(SupplierProductBinding::getProductCode)
                            .collect(Collectors.toSet());
                    List<QuotationItem> items = confirmations.stream()
                            .filter(c -> codes.contains(c.getItemCode()))
                            .map(c -> {
                                QuotationItem qi = new QuotationItem();
                                qi.setProductName(c.getItemName());
                                qi.setProductCode(c.getItemCode());
                                qi.setSpecification(c.getItemSpec());
                                qi.setOrderUnit(c.getOrderUnit());
                                if (c.getEndDate() != null) {
                                    qi.setStartDate(c.getEndDate().plusDays(1));
                                }
                                return qi;
                            }).toList();

                    sq.setQuotationItems(items);
                    return sq;
                }).toList();

        // 保存
        ctx.getSupplierQuotationFlowService().batchLaunchProcess(pricingOrderNumber, flows);

        return null;
    }
}
