package com.astenamic.new_discovery.biz.purchase.domain.pricing.service;


import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingSheet;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ProductPricingSheetService extends AbstractFormService<ProductPricingSheet> {

    public ProductPricingSheetService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2,
                                      YidaConfigProperties yidaConfigProperties) {
        super(ProductPricingSheet.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    public List<ProductPricingSheet> getByPricingOrderNumber(String pricingOrderNumber) {

        SearchConditions cond = SearchCondition.builder()
                .textEq(ProductPricingSheet.class, ProductPricingSheet::getPricingOrderNumber, pricingOrderNumber, "+")
                .get();
        return super.getFormsByCond(cond);

    }

    public ProductPricingSheet getByThirdPartyCode(String thirdPartyCode) {
        SearchConditions cond = SearchCondition.builder()
                .textEq(ProductPricingSheet.class, ProductPricingSheet::getThird_party_code, thirdPartyCode, "+")
                .get();
        return super.getFormByCond(cond);
    }

}
