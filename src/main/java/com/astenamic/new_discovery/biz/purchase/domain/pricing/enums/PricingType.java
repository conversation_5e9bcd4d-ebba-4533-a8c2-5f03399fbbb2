package com.astenamic.new_discovery.biz.purchase.domain.pricing.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum PricingType {


    DEFAULT(""),

    NEW_PRODUCT("新品"),

    CENT_PROCUREMENT("集采"),

    FRESH_PRODUCE("生鲜");


    private final String desc;

    PricingType(String desc) {
        this.desc = desc;
    }

    public static PricingType fromDesc(String desc) {
        for (PricingType type : values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        log.error("Unknown PricingType: {}", desc);
        return DEFAULT;
    }

}
