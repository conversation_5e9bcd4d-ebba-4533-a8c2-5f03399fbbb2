package com.astenamic.new_discovery.biz.purchase.application.mapper;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity.SourceAuthentication;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface SourceAuthMapper {


    @Mappings({
            @Mapping(target = "objectId", ignore = true),
            @Mapping(target = "sourcingAuthCode",     source = "auth.sourcingAuthCode"),
            @Mapping(target = "productName",          source = "auth.itemName"),
            @Mapping(target = "productCode",          source = "auth.itemCode"),
            @Mapping(target = "supplierName",         source = "auth.newSupplierName"),
            @Mapping(target = "supplierContact",      source = "auth.supplierContact"),
            @Mapping(target = "factorySpec",          source = "auth.specification"),
            @Mapping(target = "sampleTestForms",      source = "auth.productTestAttachment"),
            @Mapping(target = "price",                source = "auth.initialQuote"),
            @Mapping(target = "invoiceType",          source = "auth.invoiceType"),
            @Mapping(target = "paymentMethod",        source = "auth.settlementMethod"),
            @Mapping(target = "unit",                 source = "auth.quoteUnit"),
            @Mapping(target = "supplierCode",         source = "auth.supplierCode")
    })
    PricingReview toReview(@Context String pricingOrderNumber,
                           SourceAuthentication auth);

    @AfterMapping
    default void setPricingOrderNumber(@Context String pricingOrderNumber, @MappingTarget PricingReview review) {
        review.setPricingOrderNumber(pricingOrderNumber);
    }

    default List<PricingReview> toReviewList(List<SourceAuthentication> auths,
                                             String pricingOrderNumber) {
        return Optional.ofNullable(auths).orElse(List.of())
                .stream()
                .map(a -> toReview(pricingOrderNumber, a))
                .collect(Collectors.toList());
    }
}
