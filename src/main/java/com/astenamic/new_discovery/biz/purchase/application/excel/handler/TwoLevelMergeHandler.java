package com.astenamic.new_discovery.biz.purchase.application.excel.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.astenamic.new_discovery.biz.purchase.application.service.dto.PricingReviewTemplateDTO;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 纵向合并策略：
 * ① 相同货品编码 -> 合并【货品名称、单位、上期到期时间】等列
 * ② 在同一货品分组内，相同供应商编码 -> 合并【供应商名称、联系方式、厂号、样品测试表、初次报价、发票类型、付款方式、价格起止】等列
 * <p>
 * 只遍历一次数据列表，根据行号算出 CellRangeAddress，最后一次性 addMergedRegion。
 */
public class TwoLevelMergeHandler implements WorkbookWriteHandler {

    // 模板头部行数
    private final int headerRowCount;

    // 与 EasyExcel 填充的顺序一致的数据列表
    private final List<PricingReviewTemplateDTO> data;

    // 货品级需要合并的列
    private static final int[] PRODUCT_MERGE_COLS = {1, 3, 7};

    // 供应商级需要合并的列
    private static final int[] SUPPLIER_MERGE_COLS = {9, 10, 11, 12, 13, 15, 16,17, 22, 23};


    public TwoLevelMergeHandler(List<PricingReviewTemplateDTO> data, int headerRowCount) {
        this.data = data;
        this.headerRowCount = headerRowCount;
    }

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheet = workbook.getSheetAt(0);

        int startRow = headerRowCount;
        int endRow = headerRowCount + data.size() - 1;

        List<CellRangeAddress> toMerge = new ArrayList<>();

        int i = 0;
        while (i < data.size()) {
            int prodStart = i;
            String prodCode = data.get(i).getProductCode();
            // 找到同一货品编码的 [prodStart, prodEnd]
            while (i < data.size() && Objects.equals(data.get(i).getProductCode(), prodCode)) {
                i++;
            }
            int prodEnd = i - 1;

            // ① 货品级合并
            if (prodEnd > prodStart) {
                for (int col : PRODUCT_MERGE_COLS) {
                    toMerge.add(new CellRangeAddress(startRow + prodStart, startRow + prodEnd, col, col));
                }
            }

            // ② 在货品分组内部再分供应商
            int j = prodStart;
            while (j <= prodEnd) {
                int suppStart = j;
                String suppCode = data.get(j).getSupplierCode();

                while (j <= prodEnd && Objects.equals(data.get(j).getSupplierCode(), suppCode)) {
                    j++;
                }
                int suppEnd = j - 1;

                if (suppEnd > suppStart) {
                    for (int col : SUPPLIER_MERGE_COLS) {
                        toMerge.add(new CellRangeAddress(startRow + suppStart, startRow + suppEnd, col, col));
                    }
                }
            }
        }

        toMerge.forEach(sheet::addMergedRegion);
    }
}
