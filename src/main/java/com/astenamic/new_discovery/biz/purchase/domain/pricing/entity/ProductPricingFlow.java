package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;


import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.MarketResearcherInfo;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 货品定价流程表单
 */

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-326057C98AAF44EAADE1B467C4C7993E1071", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductPricingFlow extends YidaFlowObject {

    /**
     * 市调员表
     */
    @FormField("tableField_m5bxvpci")
    private List<MarketResearcherInfo> marketResearcherTable;

    /**
     * 货品确认表
     */
    @FormField("tableField_maan4f6f")
    private List<ProductConfirmationItem> productConfirmationList;

    /**
     * 是否新货品
     */
    @FormField("radioField_ma67egi3")
    private String isNewProduct;

    /**
     * 货品表
     */
    @FormField("tableField_m52hzd3h")
    private List<ProductItem> productTable;

    /**
     * 定价类型
     */
    @FormField("selectField_m9ggbab8")
    private String pricingType;

    /**
     * 申请日期
     */
    @FormField("dateField_m9ggbab1")
    private LocalDateTime applicationDate;

    /**
     * 需求单编码
     */
    @FormField("textField_m9kn3ybq")
    private String requirementCode;

    /**
     * 定价单号
     */
    @FormField("serialNumberField_m9kn3ybo")
    private String pricingOrderNumber;

    /**
     * 说明
     */
    @FormField("textField_ma1u3cfs")
    private String description;

    /**
     * 决议说明
     */
    @FormField("textareaField_ma1xbj7g")
    private String decisionNote;


    @FormField("textareaField_ma1xbj7f")
    private String approvalComments;

    /**
     * 货品类别
     */
    @FormField("textField_map389m6")
    private String productCategory;

    /**
     * 寻源员
     */
    @FormField("employeeField_mbhltwso")
    private List<String> sourcingAgents;

    @FormField("radioField_maq7euv1")
    private String viewButtonText;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }
}
