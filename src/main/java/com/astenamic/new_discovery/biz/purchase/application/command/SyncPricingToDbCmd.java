package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.ctx.ProductPricingContext;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.SyncPricingToDbReq;
import com.astenamic.new_discovery.biz.purchase.application.mapper.SupplierPriceMapper;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceDTO;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceItemDTO;
import com.astenamic.new_discovery.common.annotation.DingTalkAlert;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * 同步奥琦玮定价单到数据库命令
 * 1. 从供应商定价管理系统获取前一天的定价单
 * 2. 将定价单转换为产品价格实体
 * 3. 去重并更新数据库
 */
@Component
@RequiredArgsConstructor
public class SyncPricingToDbCmd implements PricingCommand<SyncPricingToDbReq, Void> {

    private static final Logger logger = LoggerFactory.getLogger(SyncPricingToDbCmd.class);

    private final ProductPricingContext ctx;

    private final SupplierPriceMapper supplierPriceMapper;

    @Override
    public Void execute(SyncPricingToDbReq req) {

        /*
          1、从奥琦玮获取定价单
         */
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);

        List<SupplierPriceItemDTO> allItems = ctx.getSupplierPricingSession()
                .getPriceList(20, yesterday.toLocalDate(), yesterday.toLocalDate(), -2).stream()
                .flatMap(p -> p.getItems().stream())
                .toList();

        /*
           2、转换为定价单实体
         */
        List<ProductPrice> productPrices = allItems.stream()
                .flatMap(dto -> supplierPriceMapper.toProductPriceList(dto).stream())
                .toList();

        Set<String> distinctGoodSnos = productPrices.stream()
                .map(ProductPrice::getGoodSno)
                .collect(Collectors.toSet());
        Map<String, ProductInfo> productInfoCache = ctx.getProductInfoService().getProductInfoIn(distinctGoodSnos);

        productPrices.forEach(pp -> {
            try {
                ProductInfo info = productInfoCache.get(pp.getGoodSno());
                if (info != null) {
                    pp.setGoodPricingType(info.getGoodPricingType());
                    pp.setGoodPricingCategory(info.getGoodPricingCategory());
                }
            } catch (Exception e) {
                logger.warn("设置商品定价信息失败: {}, 商品: {}", e.getMessage(), pp.getGoodSno());
            }
        });

        /*
            3、去重：同一供应商、同一店铺、同一货品的定价单，取最新的(根据审核时间 atime)
            注意：如果 atime 为空，则认为是旧数据，优先保留有 atime 的数据
         */
        List<ProductPrice> uniqueList = productPrices.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                p -> p.getSupplierSno() + ":" + p.getShopSno() + ":" + p.getGoodSno(),
                                Function.identity(),
                                (a, b) -> a.getAtime() == null ? b :
                                        b.getAtime() == null ? a :
                                                a.getAtime().isAfter(b.getAtime()) ? a : b),
                        map -> new ArrayList<>(map.values())));

        /*
           4、更新数据库：根据供应商编号、店铺编号和货品编号去查找已有的定价单，
         */
        ctx.getProductPriceService().saveAll(uniqueList);
        logger.info("同步定价单完成, {}", productPrices.size());
        return null;
    }
}
