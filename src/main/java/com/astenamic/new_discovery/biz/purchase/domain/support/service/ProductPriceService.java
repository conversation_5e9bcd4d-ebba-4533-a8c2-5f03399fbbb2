package com.astenamic.new_discovery.biz.purchase.domain.support.service;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.ProductPriceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductPriceService {

    private final ProductPriceRepository productPriceRepository;

    /**
     * 批量保存
     */
    public void saveAll(List<ProductPrice> productPrices) {
        List<String> goodSnos = productPrices.stream()
                .map(ProductPrice::getGoodSno).distinct().toList();
        List<ProductPrice> dbList = productPriceRepository.findByGoodSnoIn(goodSnos);
        Map<String, ProductPrice> dbMap = dbList.stream()
                .collect(Collectors.toMap(
                        p -> p.getSupplierSno() + ":" + p.getShopSno() + ":" + p.getGoodSno(),
                        Function.identity()));
        productPrices.forEach(p -> {
            ProductPrice db = dbMap.get(p.getSupplierSno() + ":" + p.getShopSno() + ":" + p.getGoodSno());
            if (db != null) p.setId(db.getId());
        });
        productPriceRepository.saveAll(productPrices);
        log.info("批量保存产品定价信息，数量: {}", productPrices.size());
    }
}
