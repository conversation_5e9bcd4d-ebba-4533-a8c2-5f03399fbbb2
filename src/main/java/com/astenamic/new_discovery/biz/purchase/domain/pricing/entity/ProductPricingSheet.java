package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.PriceAdjustmentItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 定价单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-44A8CAA9A73A41449E2DA6544ABE375CBPRK", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductPricingSheet extends YidaObject {

    /**
     * 定价单号
     */
    @FormField("serialNumberField_mam6v7be")
    private String third_party_code;


    @FormField("selectField_mam6svrv")
    private String syncStatus;

    @FormField("textareaField_mam7b6pw")
    private String failReason;

    /**
     * 定价流程单号
     */
    @FormField("textField_makrd76l")
    private String pricingOrderNumber;

    /**
     * 通用字段
     */
    private String common;
    /**
     * 货品信息-调价列表
     */
    @FormField("tableField_ma388idd")
    private List<PriceAdjustmentItem> items;

    /**
     * 询价市场一
     */
    @FormField("textareaField_ma388id7")
    private String pricemarketone;

    /**
     * 询价市场二
     */
    @FormField("textareaField_ma388id8")
    private String pricemarkettwo;

    /**
     * 门店编号
     */
    @FormField("textField_mam42uyc")
    private String shop_sno;

    @FormField("textareaField_mam42uya")
    private String shop_name;

    /**
     * 供应商编号
     */
    @FormField("textField_mam42uyb")
    private String supplier_sno;


    @FormField("textareaField_mam42uy9")
    private String supplier_name;


    @FormField("textField_mam7srdv")
    private String aceCode;


}