package com.astenamic.new_discovery.biz.purchase.domain.support.service;

import com.astenamic.new_discovery.ace.scm.productmanage.api.ProductSession;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductCategory;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class ProductCategoryService extends AbstractFormService<ProductCategory> {

    private final ProductSession productSession;

    public ProductCategoryService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties, ProductSession productSession) {
        super(ProductCategory.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
        this.productSession = productSession;
    }

    @ScheduledTask(name = "货品分类同步", cron = "0 0 1 * * ?")
    public void syncProductCategoryToYida() {
        List<ProductCategory> newData = this.productSession.getProductCategoryList(20);
        this.getFormsByCond(SearchCondition.builder().get()).forEach(old -> {
            newData.forEach(newShop -> {
                if (old.getId().equals(newShop.getId())) {
                    newShop.setObjectId(old.getObjectId());
                }
            });
        });
        super.batchSave(newData);
    }

}
