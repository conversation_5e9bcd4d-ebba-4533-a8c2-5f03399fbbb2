package com.astenamic.new_discovery.biz.purchase.domain.support.repository;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.MaterialStock;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.dto.StockSummaryDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MaterialStockRepository extends JpaRepository<MaterialStock, Long> {

    @Query(value = """
            WITH summary AS (
              SELECT
                material_no   AS lgcode,
                material_name AS lgname,
                CASE company_name
                  WHEN '杭州新发现' THEN '6'
                  WHEN '新发现'     THEN '5'
                  WHEN '南京新发现' THEN '2'
                  WHEN '新发现无锡' THEN '7'
                  WHEN '新发现宁波' THEN '8'
                END            AS slscode,
                CASE company_name
                  WHEN '杭州新发现' THEN '杭州蜀海代仓'
                  WHEN '新发现'     THEN '上海蜀海代仓'
                  WHEN '南京新发现' THEN '南京蜀海代仓'
                  WHEN '新发现无锡' THEN '无锡蜀海代仓'
                  WHEN '新发现宁波' THEN '宁波蜀海代仓'
                END            AS slsname,
                COALESCE(SUM(available_stock),0)
                + COALESCE(SUM(shelves_stock),0)
                + COALESCE(SUM(lock_stock),0)
                + COALESCE(SUM(replenishment_stock),0) AS stock,
                xs_unit as unit_name
              FROM xfx_material_stock
              WHERE material_no IS NOT NULL
              GROUP BY material_no, material_name, company_name, xs_unit 
            )
            SELECT *
            FROM summary
            WHERE slscode = :companyCode and lgcode = :materialNo;
            """, nativeQuery = true)
    StockSummaryDTO findStockSummary(@Param("companyCode") String companyCode, @Param("materialNo") String materialNo);


    @Query(value = """
            SELECT
              material_no AS lgcode,
              material_name AS lgname,
              COALESCE ( SUM ( available_stock ), 0 ) + COALESCE ( SUM ( shelves_stock ), 0 ) + COALESCE ( SUM ( lock_stock ), 0 ) + COALESCE ( SUM ( replenishment_stock ), 0 ) AS stock,
              xs_unit AS unit_name
            FROM
              xfx_material_stock
            WHERE
              material_no IS NOT NULL
              and material_no = :materialNo
            GROUP BY
              material_no,
              material_name,
              xs_unit;
            """, nativeQuery = true)
    StockSummaryDTO findStockSummaryByMaterial(@Param("materialNo") String materialNo);
}
