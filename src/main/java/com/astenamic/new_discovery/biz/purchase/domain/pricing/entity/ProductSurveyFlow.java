package com.astenamic.new_discovery.biz.purchase.domain.pricing.entity;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.SurveyItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 货品市调表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-6DCAE98F191540D0BC29D86D2ABE15E7860C", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductSurveyFlow extends YidaFlowObject {

    /**
     * 市调单号
     */
    @FormField("serialNumberField_m9kwzh11")
    private String surveyOrderNumber;

    /**
     * 定价单号
     */
    @FormField("textField_m9kwzh12")
    private String pricingOrderNumber;

    /**
     * 市调日期
     */
    @FormField("dateField_m9kwzh1e")
    private LocalDateTime surveyDate;

    /**
     * 市调员
     */
    @FormField("employeeField_m9l0gxwc")
    private List<String> surveyors;

    public void setSurveyors(String surveyors) {
        if (StringUtils.isNotBlank(surveyors)) {
            this.surveyors = List.of(surveyors);
        }
    }

    /**
     * 货品市调表-行信息
     */
    @FormField("tableField_m5p4tavc")
    private List<SurveyItem> surveyItems;
}
