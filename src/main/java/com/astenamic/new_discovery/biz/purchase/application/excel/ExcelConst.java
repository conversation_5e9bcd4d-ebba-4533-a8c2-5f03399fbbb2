package com.astenamic.new_discovery.biz.purchase.application.excel;

import java.util.List;

public final class ExcelConst {

    /* ============= 价格评审表 ============= */
    /**
     * {reviewList.xxx} 第一行（Excel 行号从 1 开始）
     */
    public static final int REVIEW_START_ROW = 3;
    /**
     * 需要纵向合并的列
     */
    public static final List<Integer> REVIEW_COLS =
            List.of(1, 2, 3, 4, 5);

    public static final int REVIEW_END_ROW = 4;

    /* 空行数（价格评审表与导入模板之间） */
    public static final int GAP_ROWS = 1;

    /* ============= 定价导入模板 ============= */
    /**
     * 下半表表头共 2 行
     */
    public static final int INPUT_HEADER_ROWS = 2;
    /**
     * 货品名称列
     */
    public static final int PROD_COL = 1;
    /**
     * 供应商级需要合并的列
     */
    public static final List<Integer> SUPP_COLS = List.of(2, 3, 7, 8);

    private ExcelConst() {
    }
}


