package com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 调价子项
 */
@Data
@FormEntity("tableField_ma388idd")
public class PriceAdjustmentItem {

    /**
     * 调价原因
     */
    @FormField("textareaField_ma388idm")
    private String reason;

    /**
     * 供应商价格
     */
    @FormField("numberField_ma388idl")
    private Float supplier_price;

    /**
     * 市场价格二
     */
    @FormField("numberField_ma388idk")
    private Float marketpricetwo;

    /**
     * 市场价格一
     */
    @FormField("numberField_ma388idj")
    private Float marketpriceone;

    /**
     * 价格生效结束日期
     */
    @FormField("dateField_ma388idi")
    private LocalDateTime enddate;

    /**
     * 价格生效开始日期
     */
    @FormField("dateField_ma388idh")
    private LocalDateTime startdate;

    /**
     * 定价
     */
    @FormField("numberField_ma388idg")
    private Float uprice;

    /**
     * 货品编号
     */
    @FormField("textField_ma388ide")
    private String good_sno;

    /**
     * 货品名称
     */
    @FormField("textField_ma388idf")
    private String productName;
}
