package com.astenamic.new_discovery.biz.purchase.domain.support.service;


import com.astenamic.new_discovery.ace.scm.shopmanage.api.ShopSession;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ShopInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.enums.StoreType;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ShopInfoService extends AbstractFormService<ShopInfo> {

    private final ShopSession shopSession;

    public ShopInfoService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties, ShopSession shopSession) {
        super(ShopInfo.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
        this.shopSession = shopSession;
    }

    @ScheduledTask(name = "门店信息同步", cron = "0 0 1 * * ?")
    public void syncShopToYida() {
        List<ShopInfo> newData = shopSession.getShopList(25);
        List<ShopInfo> oldData = super.getFormsByCond(SearchCondition.builder().get());
        if (!oldData.isEmpty()) {
            for (ShopInfo old : oldData) {
                for (ShopInfo newShop : newData) {
                    if (old.getSno().equals(newShop.getSno())) {
                        newShop.setObjectId(old.getObjectId());
                        break;
                    }
                }
            }
        }
        super.batchSave(newData);
    }

    public List<ShopInfo> getWarehouses() {
        SearchConditions cond = SearchCondition.builder().textEq(ShopInfo.class, ShopInfo::getStype, StoreType.DISTRIBUTION_CENTER.getDesc(), "+").get();
        return super.getFormsByCond(cond);
    }

}
