package com.astenamic.new_discovery.biz.purchase.domain.pricing.service;

import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.PricingReview;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PricingReviewService extends AbstractFormService<PricingReview> {

    public PricingReviewService(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2,
                                YidaConfigProperties yidaConfigProperties) {
        super(PricingReview.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据定价单号查询定价评审表单列表
     *
     * @param pricingOrderNumber 定价单号
     * @return 对应的定价评审表单列表
     */
    public List<PricingReview> getByPricingOrderNumber(String pricingOrderNumber) {

        SearchConditions cond = SearchCondition.builder()
                .textEq("textField_m9m4cq3e", pricingOrderNumber, "+")
                .get();
        return super.getFormsByCond(cond);

    }

    public List<PricingReview> getBySourcingAuthCode(String sourcingAuthCode) {

        SearchConditions cond = SearchCondition.builder()
                .textEq(PricingReview.class, PricingReview::getSourcingAuthCode, sourcingAuthCode, "+")
                .get();
        return super.getFormsByCond(cond);

    }


}
