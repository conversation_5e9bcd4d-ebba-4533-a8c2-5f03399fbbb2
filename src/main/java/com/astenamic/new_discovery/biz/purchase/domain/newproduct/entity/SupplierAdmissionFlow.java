package com.astenamic.new_discovery.biz.purchase.domain.newproduct.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商准入流程表单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FormEntity(value = "FORM-B7E765DA40804515B4911278B7FFA4F98FTJ", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class SupplierAdmissionFlow extends YidaFlowObject {

    /**
     * 准入编号
     */
    @FormField("serialNumberField_ma7u6jsi")
    private String admissionSerialNumber;

    /**
     * 新品需求单号
     */
    @FormField("textField_m9za4j43")
    private String requirementOrderNumber;

    /**
     * 寻源认证编码
     */
    @FormField("textField_m9mfym4m")
    private String sourcingAuthCode;

    /**
     * 寻源员
     */
    @FormField("employeeField_ma7jongb")
    private List<String> sourcingAgents;

    public void setSourcingAgents(String sourcingAgents) {
        if (StringUtils.isNotBlank(sourcingAgents)) {
            this.sourcingAgents = List.of(sourcingAgents);
        }
    }

    /**
     * 寻源时间
     */
    @FormField("dateField_ma7jonfy")
    private LocalDateTime sourcingTime;

    /**
     * 状态
     */
    @FormField("selectField_ma7u3u5a")
    private String status;

    /**
     * 供应商名称
     */
    @FormField("textField_m9j9x033")
    private String supplierName;

    /**
     * 联系人
     */
    @FormField("textField_m9v0zxvj")
    private String contactPerson;

    /**
     * 注册资金（万元）
     */
    @FormField("numberField_ma7jong1")
    private Float registeredCapital;

    /**
     * 联系方式
     */
    @FormField("textField_m9w4wo95")
    private String contactInfo;

    /**
     * 现有合作品牌
     */
    @FormField("textField_m9v0zxvq")
    private String existingPartners;

    /**
     * 年销售总额（万元）
     */
    @FormField("numberField_ma7jong2")
    private Float annualSalesTotal;

    /**
     * 营业执照
     */
    @FormField("attachmentField_m9j9x03l")
    private List<YidaFile> businessLicenseAttachment;


    /**
     * 食品经营许可证
     */
    @FormField("attachmentField_m9j9x03n")
    private List<YidaFile> foodBusinessLicenseAttachment;

    /**
     * 食品经营许可证到期时间
     */
    @FormField("dateField_m9jl36yx")
    private LocalDateTime foodBusinessLicenseExpiry;

    /**
     * 食品生产许可证
     */
    @FormField("attachmentField_m9j9x03m")
    private List<YidaFile> foodProductionLicenseAttachment;

    /**
     * 食品生产许可证到期时间
     */
    @FormField("dateField_m9jl36yz")
    private LocalDateTime foodProductionLicenseExpiry;

    /**
     * 其它证件
     */
    @FormField("attachmentField_m9v0zxw2")
    private List<YidaFile> otherDocuments;
}
