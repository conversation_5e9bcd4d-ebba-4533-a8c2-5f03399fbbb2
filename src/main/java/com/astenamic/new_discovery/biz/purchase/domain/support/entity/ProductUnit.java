package com.astenamic.new_discovery.biz.purchase.domain.support.entity;


import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity(value = "FORM-F000C751CDCE4395B7E1488867BF6AB25ZYJ",appType = "APP_VAWCFBK8UUNBTJINOCWQ",sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductUnit extends BaseEntity {

    @FormField("numberField_ma1xtklw")
    private Float lguid;

    @FormField("textField_ma1xtklx")
    private String name;

    @FormField("textField_ma1xtkm2")
    private String sno;

    @FormField("radioField_ma1xtkm3")
    private String ifbalance;

    public void setIfbalance(String ifbalance) {
        if ("0".equals(ifbalance)) {
            this.ifbalance = "否";
        } else if ("1".equals(ifbalance)) {
            this.ifbalance = "是";
        } else {
            this.ifbalance = ifbalance;
        }
    }
}
