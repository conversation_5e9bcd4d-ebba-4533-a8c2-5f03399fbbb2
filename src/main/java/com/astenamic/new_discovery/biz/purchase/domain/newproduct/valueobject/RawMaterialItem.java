package com.astenamic.new_discovery.biz.purchase.domain.newproduct.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

/**
 * 新品引入流程-原料表行实体
 */
@Data
@FormEntity("tableField_ma6inwm4")
public class RawMaterialItem {

    /**
     * 税率
     */
    @FormField("textField_ma6inwmv")
    private String taxRate;

    /**
     * 供应商名称
     */
    @FormField("textField_ma6inwmu")
    private String supplierName;

    /**
     * 属性（原材料 / 半成品 / 粗加工）
     */
    @FormField("selectField_ma6inwmt")
    private String attribute;

    /**
     * 货品类型标识（原料 / 物料）
     */
    @FormField("selectField_ma6inwms")
    private String productTypeFlag;

    /**
     * 物流属性（直送 / 直通 / 配送）
     */
    @FormField("selectField_ma6inwmr")
    private String logisticsAttr;

    /**
     * 含税单价（订货单位）
     */
    @FormField("numberField_ma6inwmq")
    private Float taxIncludedPrice;

    /**
     * 财务统计分类
     */
    @FormField("textField_ma6inwmn")
    private String financeCategory;

    /**
     * 所属小类
     */
    @FormField("selectField_ma6inwmp")
    private String subcategory;

    /**
     * 所属大类
     */
    @FormField("selectField_ma6inwmo")
    private String category;

    /**
     * 盘点系数（主核算单位 / 盘点单位）
     */
    @FormField("numberField_ma6inwmk")
    private Float countCoefficient;

    /**
     * 库存系数（库存单位 / 主核算单位）
     */
    @FormField("numberField_ma6inwmj")
    private Float inventoryCoefficient;

    /**
     * 成本系数（主核算单位 / 成本单位）
     */
    @FormField("numberField_ma6inwmi")
    private Float costCoefficient;

    /**
     * 订货系数（订货单位 / 主核算单位）
     */
    @FormField("numberField_ma6inwmh")
    private Float orderCoefficient;

    /**
     * 盘点单位
     */
    @FormField("selectField_ma6inwmg")
    private String countUnit;

    /**
     * 库存单位
     */
    @FormField("selectField_ma6inwme")
    private String inventoryUnit;

    /**
     * 成本单位
     */
    @FormField("textField_mdecegij")
    private String costUnit;

    /**
     * 订货单位
     */
    @FormField("selectField_ma6inwma")
    private String orderUnit;

    /**
     * 主核算单位
     */
    @FormField("selectField_ma6inwmc")
    private String mainUnit;

    /**
     * 规格
     */
    @FormField("textField_ma6inwm7")
    private String specification;

    /**
     * 货品编码
     */
    @FormField("textField_ma6inwm6")
    private String productCode;

    /**
     * 货品名称
     */
    @FormField("textField_ma6inwm5")
    private String productName;
}