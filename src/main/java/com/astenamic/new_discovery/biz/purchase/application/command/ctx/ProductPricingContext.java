package com.astenamic.new_discovery.biz.purchase.application.command.ctx;

import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.NewProductIntroductionFlowService;
import com.astenamic.new_discovery.biz.purchase.domain.newproduct.service.SourceAuthenticationService;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductSupplierQuotationFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.service.*;
import com.astenamic.new_discovery.biz.purchase.domain.support.repository.*;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.api.SupplierPricingSession;
import com.astenamic.new_discovery.biz.purchase.domain.support.service.ProductInfoService;
import com.astenamic.new_discovery.biz.purchase.domain.support.service.ProductPriceService;
import com.astenamic.new_discovery.biz.purchase.domain.support.service.SupplierInfoService;
import com.astenamic.new_discovery.biz.purchase.infrastructure.decorator.WarehouseDecorator;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Component;


@Getter
@Component
@AllArgsConstructor
public class ProductPricingContext {

    private final YiDaSession yiDaSession;
    private final SourceAuthenticationService sourceAuthService;
    private final PricingReviewService pricingReviewService;
    private final NewProductIntroductionFlowService newProductIntroductionFlowService;
    private final ProductPricingFlowService productPricingFlowService;
    private final SupplierProductBindingRepository bindingRepo;
    private final SupplierQuotationFlowService supplierQuotationFlowService;
    private final WarehouseDecorator warehouseDecorator;
    private final YidaConfigProperties yidaConfigProperties;
    private final MaterialStockRepository materialStockRepository;
    private final DailyDispatchProductSummaryRepository dailyDispatchProductSummaryRepository;
    private final ProductPricingSheetService productPricingSheetService;
    private final SupplierPricingSession supplierPricingSession;
    private final ProductInfoService productInfoService;
    private final ProductPriceRepository productPriceRepository;
    private final ProductPriceCacheRepository productPriceCacheRepository;
    private final ProductSupplierQuotationFlowService productSupplierQuotationFlowService;
    private final SupplierInfoService supplierInfoService;
    private final FormManager formManager;
    private final ProductPriceService productPriceService;
}
