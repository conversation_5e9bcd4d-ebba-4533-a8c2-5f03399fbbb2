package com.astenamic.new_discovery.biz.purchase.domain.support.enums;


import lombok.Getter;

@Getter
public enum PurchaseStatus {
    /**
     * 0:待确认 1:未完成 -2:已完成 -1:已废弃 -6 反审核
     */
    WAIT_CONFIRM(0, "待确认"),
    UNFINISHED(1, "未完成"),
    COMPLETED(-2, "已完成"),
    ABANDONED(-1, "已废弃"),
    UNCHECKED(-6, "反审核"),
    ;


    private final Integer code;

    private final String value;


    PurchaseStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

}
