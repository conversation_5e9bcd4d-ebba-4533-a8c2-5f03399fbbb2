package com.astenamic.new_discovery.biz.purchase.domain.support.repository;

import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductPriceRepository extends JpaRepository<ProductPrice, Long> {


    @Query(value = "SELECT * FROM xfx_product_price WHERE good_sno IN (:goodSnos)", nativeQuery = true)
    List<ProductPrice> findByGoodSnoIn(@Param("goodSnos") List<String> goodSnos);

    @Query(value = "SELECT * FROM xfx_product_price WHERE good_sno = :goodSno", nativeQuery = true)
    List<ProductPrice> findByGoodSno(@Param("goodSno") String goodSno);


    @Query(value = """
            SELECT
             *
            FROM
              PUBLIC.xfx_product_price
            WHERE
              enddate IS NOT NULL
              AND enddate BETWEEN CURRENT_DATE
              AND CURRENT_DATE + INTERVAL '30 days'
              AND enabled = '1'
              AND good_pricing_type = '2';
            """, nativeQuery = true
    )
    List<ProductPrice> findRecentWithin30Days();

    @Query(
            value = """
                    SELECT
                      DISTINCT  good_pricing_category
                    FROM
                      PUBLIC.xfx_product_price
                    WHERE
                      enddate IS NOT NULL
                      AND enddate BETWEEN CURRENT_DATE
                      AND CURRENT_DATE + INTERVAL '30 days'
                      AND enabled = '1'
                      AND good_pricing_type = '2';
                    """,
            nativeQuery = true
    )
    List<String> findCategoryWithinNext30Days();

}
