package com.astenamic.new_discovery.biz.purchase.application.mapper;

import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceItemDTO;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductPrice;
import org.mapstruct.*;

import java.util.*;
import java.util.stream.*;

@Mapper(componentModel = "spring", imports = {Stream.class, IntStream.class})
public interface SupplierPriceMapper {

    @Mappings({
            @Mapping(target = "pricingCode", source = "code"),
            @Mapping(target = "goodName", source = "good_name"),
            @Mapping(target = "goodSno", source = "good_sno"),
            @Mapping(target = "goodBrand", source = "good_brand"),
            @Mapping(target = "fgoodtypeName", source = "fgoodtype_name"),
            @Mapping(target = "goodtypeName", source = "goodtype_name"),
            @Mapping(target = "applyguname", source = "applyguname"),
            @Mapping(target = "startdate", source = "startdate"),
            @Mapping(target = "enddate", source = "enddate"),
            @Mapping(target = "lastprice", source = "lastprice"),
            @Mapping(target = "lastyprice", source = "lastyprice"),
            @Mapping(target = "applyamount", source = "applyamount"),
            @Mapping(target = "std", source = "std"),
            @Mapping(target = "atime", source = "atime"),
            @Mapping(target = "supplierName", ignore = true),
            @Mapping(target = "supplierSno", ignore = true),
            @Mapping(target = "shopName", ignore = true),
            @Mapping(target = "shopSno", ignore = true),
            @Mapping(target = "goodPricingType", source = "goodPriceType"),
            @Mapping(target = "enabled", constant = "1")
    })
    ProductPrice toProductPrice(SupplierPriceItemDTO dto);

    default List<ProductPrice> toProductPriceList(SupplierPriceItemDTO dto) {

        String[] suppSnos = Optional.ofNullable(dto.getSupplier_sno()).orElse("").split(",");
        String[] suppNames = Optional.ofNullable(dto.getSupplier_name()).orElse("").split(",");
        String[] shopSnos = Optional.ofNullable(dto.getShop_sno()).orElse("").split(",");
        String[] shopNames = Optional.ofNullable(dto.getShop_name()).orElse("").split(",");

        return IntStream.range(0, suppSnos.length).boxed()
                .flatMap(i -> IntStream.range(0, shopSnos.length)
                        .mapToObj(j -> {
                            ProductPrice pp = toProductPrice(dto);
                            pp.setSupplierSno(suppSnos[i]);
                            pp.setSupplierName(i < suppNames.length ? suppNames[i] : "");
                            pp.setShopSno(shopSnos[j]);
                            pp.setShopName(j < shopNames.length ? shopNames[j] : "");
                            if (dto.getUprice() != null) {
                                pp.setUprice(dto.getUprice().floatValue());
                            }
                            return pp;
                        }))
                .toList();
    }
}
