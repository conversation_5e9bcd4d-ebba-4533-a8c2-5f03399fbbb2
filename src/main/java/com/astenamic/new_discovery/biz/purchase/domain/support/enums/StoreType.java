package com.astenamic.new_discovery.biz.purchase.domain.support.enums;

import lombok.Getter;

/**
 * 门店类型枚举
 */
@Getter
public enum StoreType {
    /** 配送中心 */
    DISTRIBUTION_CENTER("3", "配送中心"),

    /** 直营店 */
    DIRECT_SALES("4", "直营店"),

    /** 加盟店 */
    FRANCHISE("5", "加盟店"),

    /** 外销客户 */
    EXPORT_CUSTOMER("6", "外销客户");

    private final String code;

    private final String desc;

    StoreType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 code 获取枚举
     */
    public static StoreType fromCode(String code) {
        for (StoreType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的门店类型: " + code);
    }
}
