package com.astenamic.new_discovery.biz.purchase.domain.support.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity(name = "xfx_lgt_supgood")
@Data
public class SupplierProductBinding {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "lgid", nullable = false)
    private Integer productId;

    @Column(name = "sno", length = 20)
    private String productCode;

    @Column(name = "lgname", length = 100)
    private String productName;

    @Column(name = "std", length = 50)
    private String productSpec;

    @Column(name = "lspid", nullable = false)
    private Integer supplierId;

    @Column(name = "supplier_name", length = 40)
    private String supplierName;

    @Column(name = "supplier_code", length = 25)
    private String supplierCode;

    @Column(name = "supplier_mobile", length = 55)
    private String supplierMobile;

    /**
     * 结算方式
     */
    @Column(name = "accountdatetype", length = 55)
    private String accountdatetype;

    /**
     * 发票类型
     */
    @Column(name = "invoicetype", length = 55)
    private String invoicetype;

}