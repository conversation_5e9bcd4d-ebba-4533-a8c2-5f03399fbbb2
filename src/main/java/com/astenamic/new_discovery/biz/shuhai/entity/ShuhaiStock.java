package com.astenamic.new_discovery.biz.shuhai.entity;


import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity(name = "xfx_shuhai_stock")
@Data
public class ShuhaiStock {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物料名称
     */
    @Column(name = "sku_name", length = 64)
    private String skuName;

    /**
     * 可用库存
     */
    @Column(name = "actual_qty", length = 64)
    private String actualQty;

    /**
     * 不可用库存
     */
    @Column(name = "unavailable_qty", length = 64)
    private String unavailableQty;

    /**
     * 待上架
     */
    @Column(name = "show_put_pending_qty", length = 64)
    private String showPutPendingQty;

    /**
     * 待检品
     */
    @Column(name = "show_quality_pending_qty", length = 64)
    private String showQualityPendingQty;

    /**
     * 残品
     */
    @Column(name = "show_quality_reject_qty", length = 64)
    private String showQualityRejectQty;

    /**
     * 待拣库存
     */
    @Column(name = "show_pick_pending_qty", length = 64)
    private String showPickPendingQty;

    /**
     * 待分拣
     */
    @Column(name = "show_sort_pending_qty", length = 64)
    private String showSortPendingQty;

    /**
     * 待发货
     */
    @Column(name = "show_ship_pending_qty", length = 64)
    private String showShipPendingQty;

    /**
     * 盘点锁定
     */
    @Column(name = "show_inventory_lock_qty", length = 64)
    private String showInventoryLockQty;

    /**
     * 库存冻结
     */
    @Column(name = "show_freeze_sign_qty", length = 64)
    private String showFreezeSignQty;

    /**
     * 移位锁定
     */
    @Column(name = "show_move_lock_qty", length = 64)
    private String showMoveLockQty;

    /**
     * 补货锁定
     */
    @Column(name = "show_replenish_lock_qty", length = 64)
    private String showReplenishLockQty;

    /**
     * 报缺锁定
     */
    @Column(name = "show_vacancy_lock_qty", length = 64)
    private String showVacancyLockQty;

    /**
     * 盲收锁定
     */
    @Column(name = "show_blind_lock_qty", length = 64)
    private String showBlindLockQty;

    /**
     * 补录锁定
     */
    @Column(name = "show_supplement_lock_qty", length = 64)
    private String showSupplementLockQty;

    /**
     * 单位编码
     */
    @Column(name = "unit_code", length = 64)
    private String unitCode;

    /**
     * 单位名称
     */
    @Column(name = "unit_name", length = 64)
    private String unitName;

    /**
     * 客户物料编码
     */
    @Column(name = "customer_material_number", length = 64, nullable = false)
    private String customerMaterialNumber;

    /**
     * 客户仓库编码
     */
    @Column(name = "customer_warehouse_code", length = 64, nullable = false)
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    @Column(name = "customer_warehouse", length = 64)
    private String customerWarehouse;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
