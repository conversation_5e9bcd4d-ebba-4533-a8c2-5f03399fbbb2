package com.astenamic.new_discovery.biz.shuhai.mapper;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiInventoryReport;
import com.astenamic.new_discovery.external.modal.dto.shuhai.InventoryReportDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * MapStruct 转换器：InventoryReportDTO ↔ ShuhaiInventoryReport
 */
@Mapper(componentModel = "spring")
public interface ShuhaiInventoryReportMapper {

    /**
     * DTO → 实体（忽略主键 id）
     */
    @Mapping(target = "id", ignore = true)
    ShuhaiInventoryReport toEntity(InventoryReportDTO dto);

    /**
     * 实体 → DTO
     */
    InventoryReportDTO toDto(ShuhaiInventoryReport entity);

    /**
     * DTO 列表 → 实体列表
     */
    List<ShuhaiInventoryReport> toEntities(List<InventoryReportDTO> dtoList);

    /**
     * 实体列表 → DTO 列表
     */
    List<InventoryReportDTO> toDtos(List<ShuhaiInventoryReport> entityList);
}