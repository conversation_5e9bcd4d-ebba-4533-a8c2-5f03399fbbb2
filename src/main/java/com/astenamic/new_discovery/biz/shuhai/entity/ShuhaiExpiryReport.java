package com.astenamic.new_discovery.biz.shuhai.entity;


import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity(name = "xfx_shuhai_expiry_report")
@Data
public class ShuhaiExpiryReport {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物料名称
     */
    @Column(name = "sku_name", length = 64)
    private String skuName;

    /**
     * 物料编码
     */
    @Column(name = "material_number", length = 64)
    private String materialNumber;

    /**
     * 库存数量
     */
    @Column(name = "stock_qty")
    private Float stockQty;

    /**
     * 保质期
     */
    @Column(name = "shelf", length = 10)
    private String shelf;

    /**
     * 生产日期 (yyyy-MM-dd)
     */
    @Column(name = "product_date", length = 64)
    private String productDate;

    /**
     * 入库日期 (yyyy-MM-dd)
     */
    @Column(name = "storage_date", length = 64)
    private String storageDate;

    /**
     * 过期日期 (yyyy-MM-dd)
     */
    @Column(name = "expire_date", length = 64)
    private String expireDate;

    /**
     * 制表日期 (yyyy-MM-dd)
     */
    @Column(name = "send_time", length = 64)
    private String sendTime;

    /**
     * 库存状态
     */
    @Column(name = "stock_status", length = 10)
    private String stockStatus;

    /**
     * 单位名称
     */
    @Column(name = "unit_name", length = 64)
    private String unitName;

    /**
     * 客户物料编码
     */
    @Column(name = "customer_material_number", length = 64)
    private String customerMaterialNumber;

    /**
     * 客户仓库编码
     */
    @Column(name = "customer_warehouse_code", length = 64)
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    @Column(name = "customer_warehouse", length = 64)
    private String customerWarehouse;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}

