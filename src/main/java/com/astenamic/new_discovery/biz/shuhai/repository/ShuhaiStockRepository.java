package com.astenamic.new_discovery.biz.shuhai.repository;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiStock;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ShuhaiStockRepository extends JpaRepository<ShuhaiStock, Long> {


    /**
     * 根据客户仓库编码查询库存记录
     *
     * @param warehouseCode 客户仓库编码
     * @return 符合条件的库存列表
     */
    List<ShuhaiStock> findByCustomerWarehouseCode(String warehouseCode);

}
