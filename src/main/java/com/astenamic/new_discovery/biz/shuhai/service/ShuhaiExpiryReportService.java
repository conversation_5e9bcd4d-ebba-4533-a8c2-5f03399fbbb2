package com.astenamic.new_discovery.biz.shuhai.service;


import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiExpiryReport;
import com.astenamic.new_discovery.biz.shuhai.mapper.ShuhaiExpiryReportMapper;
import com.astenamic.new_discovery.biz.shuhai.repository.ShuhaiExpiryReportRepository;
import com.astenamic.new_discovery.external.client.shuhai.service.ExpiryReportService;
import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import com.astenamic.new_discovery.external.modal.enums.ShuhaiWarehouseEnum;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class ShuhaiExpiryReportService {

    private final ExpiryReportService expiryReportService;

    private final ShuhaiExpiryReportRepository shuhaiExpiryReportRepository;

    private final ShuhaiExpiryReportMapper shuhaiExpiryReportMapper;

    @ScheduledTask(name = "蜀海效期报表同步", cron = "0 0 1 * * ?")
    @Transactional
    public void syncExpiryReportToDb() {
        for (ShuhaiWarehouseEnum wh : ShuhaiWarehouseEnum.values()) {
            LocalDateTime now = LocalDateTime.now();
            List<ExpiryReportDTO> dtoList = expiryReportService
                    .getExpiryReportList(wh.getEntCode(), wh.getCustomerWarehouseCode());

            if (dtoList.isEmpty()) {
                continue;
            }

            // 清除所有蜀海效期报表数据
            shuhaiExpiryReportRepository.deleteByCustomerWarehouseCode(wh.getCustomerWarehouseCode());

            List<ShuhaiExpiryReport> entityList = dtoList.stream()
                    .map(shuhaiExpiryReportMapper::toEntity)
                    .peek(e -> {
                        e.setUpdateTime(now);
                    })
                    .toList();

            shuhaiExpiryReportRepository.saveAll(entityList);
        }
    }
}
