package com.astenamic.new_discovery.biz.shuhai.mapper;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiExpiryReport;
import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * MapStruct 转换器：ExpiryReportDTO ↔ ShuhaiExpiryReport
 */
@Mapper(componentModel = "spring")
public interface ShuhaiExpiryReportMapper {

    /**
     * DTO → 实体（忽略主键 id）
     */
    @Mapping(target = "id", ignore = true)
    ShuhaiExpiryReport toEntity(ExpiryReportDTO dto);

    /**
     * DTO 列表 → 实体列表
     */
    List<ShuhaiExpiryReport> toEntities(List<ExpiryReportDTO> dtoList);
}
