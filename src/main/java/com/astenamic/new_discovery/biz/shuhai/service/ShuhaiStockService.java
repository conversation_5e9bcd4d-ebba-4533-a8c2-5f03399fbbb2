package com.astenamic.new_discovery.biz.shuhai.service;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiStock;
import com.astenamic.new_discovery.biz.shuhai.mapper.StockInfoMapper;
import com.astenamic.new_discovery.biz.shuhai.repository.ShuhaiStockRepository;
import com.astenamic.new_discovery.external.client.shuhai.service.StockGetService;
import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import com.astenamic.new_discovery.external.modal.enums.ShuhaiWarehouseEnum;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class ShuhaiStockService {

    private final ShuhaiStockRepository shuhaiStockRepository;

    private final StockGetService stockGetService;

    private final StockInfoMapper stockInfoMapper;

    @ScheduledTask(name = "蜀海库存同步", cron = "0 0 1 * * ?")
    public void syncStockToDb() {
        for (ShuhaiWarehouseEnum wh : ShuhaiWarehouseEnum.values()) {
            LocalDateTime now = LocalDateTime.now();
            // 1. 调用蜀海接口拿最新库存
            List<StockInfoDTO> dtoList = stockGetService
                    .getStockList(wh.getEntCode(), wh.getCustomerWarehouseCode());

            if (dtoList.isEmpty()) {
                continue;
            }

            // 2. 当前仓库已有库存记录
            Map<String, Long> oldIdMap = shuhaiStockRepository
                    .findByCustomerWarehouseCode(wh.getCustomerWarehouseCode())
                    .stream()
                    .filter(shuhaiStock -> StringUtils.isNotBlank(shuhaiStock.getCustomerMaterialNumber()))
                    .collect(Collectors.toMap(
                            ShuhaiStock::getCustomerMaterialNumber,
                            ShuhaiStock::getId
                    ));

            // 3. DTO → Entity，并补充 updateTime / 回填已有 ID
            List<ShuhaiStock> entityList = dtoList.stream()
                    .map(stockInfoMapper::toEntity)     // DTO -> Entity 映射
                    .peek(e -> {
                        e.setUpdateTime(now);
                        Long id = oldIdMap.get(e.getCustomerMaterialNumber());
                        if (id != null) {
                            e.setId(id);
                        }
                    })
                    .toList();

            // 4. 批量保存
            shuhaiStockRepository.saveAll(entityList);
        }
    }

}
