package com.astenamic.new_discovery.biz.shuhai.entity;


import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity(name = "xfx_shuhai_inventory_report")
@Data
public class ShuhaiInventoryReport {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物料名称
     */
    @Column(name = "sku_name", length = 64, nullable = false)
    private String skuName;

    /**
     * 物料编码
     */
    @Column(name = "material_number", length = 64, nullable = false)
    private String materialNumber;

    /**
     * 单位名称
     */
    @Column(name = "unit_name", length = 64, nullable = false)
    private String unitName;

    /**
     * 当日库存
     */
    @Column(name = "stock_qty")
    private Float stockQty;

    /**
     * 前一天入库
     */
    @Column(name = "before_innum")
    private Float beforeInnum;

    /**
     * 前一天出库
     */
    @Column(name = "before_outnum")
    private Float beforeOutnum;

    /**
     * 客户物料编码
     */
    @Column(name = "customer_material_number", length = 64)
    private String customerMaterialNumber;

    /**
     * 前一天库存
     */
    @Column(name = "before_stock_qty")
    private Float beforeStockQty;

    /**
     * 差异
     */
    @Column(name = "dif_qty")
    private Float difQty;

    /**
     * 客户仓库编码
     */
    @Column(name = "customer_warehouse_code", length = 64)
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    @Column(name = "customer_warehouse", length = 64)
    private String customerWarehouse;

    /**
     * 日期
     */
    @Column(name = "date")
    private LocalDate date;

    /**
     * 更新时间
     */
    @Column(name = "update_time", columnDefinition = "timestamp(6)")
    private LocalDateTime updateTime;
}
