package com.astenamic.new_discovery.biz.shuhai.repository;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiExpiryReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShuhaiExpiryReportRepository extends JpaRepository<ShuhaiExpiryReport, Long> {

    /**
     * 根据客户仓库编码查询报告记录
     *
     * @param warehouseCode 客户仓库编码
     * @return 报告列表
     */
    List<ShuhaiExpiryReport> findByCustomerWarehouseCode(String warehouseCode);

    /**
     * 根据客户仓库编码删除报告记录
     *
     * @param customerWarehouseCode 客户仓库编码
     */
    void deleteByCustomerWarehouseCode(String customerWarehouseCode);

}

