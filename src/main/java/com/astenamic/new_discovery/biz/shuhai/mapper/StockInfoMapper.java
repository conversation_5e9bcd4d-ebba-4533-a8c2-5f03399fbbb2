package com.astenamic.new_discovery.biz.shuhai.mapper;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiStock;
import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * MapStruct 转换器：StockInfoDTO <-> XfxShuhaiStock
 */
@Mapper(componentModel = "spring")
public interface StockInfoMapper {

    StockInfoMapper INSTANCE = Mappers.getMapper(StockInfoMapper.class);

    /**
     * DTO 转实体，忽略 id 字段
     *
     * @param dto DTO 对象
     * @return 实体对象
     */
    @Mapping(target = "id", ignore = true)
    ShuhaiStock toEntity(StockInfoDTO dto);

    /**
     * DTO 转实体，忽略 id 字段
     *
     * @param dtos DTO 对象
     * @return 实体对象
     */
    List<ShuhaiStock> toEntities(List<StockInfoDTO> dtos);

    /**
     * 实体 转 DTO
     *
     * @param entity 实体对象
     * @return DTO 对象
     */
    StockInfoDTO toDto(ShuhaiStock entity);
}
