package com.astenamic.new_discovery.biz.shuhai.service;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiExpiryReport;
import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiInventoryReport;
import com.astenamic.new_discovery.biz.shuhai.mapper.ShuhaiInventoryReportMapper;
import com.astenamic.new_discovery.biz.shuhai.repository.ShuhaiInventoryReportRepository;
import com.astenamic.new_discovery.external.client.shuhai.service.InventoryReportService;
import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.InventoryReportDTO;
import com.astenamic.new_discovery.external.modal.enums.ShuhaiWarehouseEnum;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class ShuhaiInventoryReportService {

    private final InventoryReportService inventoryReportService;

    private final ShuhaiInventoryReportRepository shuhaiInventoryReportRepository;

    private final ShuhaiInventoryReportMapper shuhaiInventoryReportMapper;

    @ScheduledTask(name = "蜀海进销存报表同步", cron = "0 0 1 * * ?")
    public void syncInventoryReportToDb() {
        for (ShuhaiWarehouseEnum wh : ShuhaiWarehouseEnum.values()) {
            LocalDateTime now = LocalDateTime.now();
            LocalDate nowDate = now.toLocalDate();

            List<InventoryReportDTO> dtoList = inventoryReportService
                    .getInventoryReportList(wh.getEntCode(), wh.getCustomerWarehouseCode());

            if (dtoList.isEmpty()) {
                continue;
            }

            Map<String, Long> oldIdMap = shuhaiInventoryReportRepository
                    .findByDateAndCustomerWarehouseCode(nowDate, wh.getCustomerWarehouseCode())
                    .stream()
                    .filter(shuhaiInventoryReport -> StringUtils.isNotBlank(shuhaiInventoryReport.getCustomerMaterialNumber()))
                    .collect(Collectors.toMap(
                            ShuhaiInventoryReport::getCustomerMaterialNumber,
                            ShuhaiInventoryReport::getId
                    ));

            List<ShuhaiInventoryReport> entityList = dtoList.stream()
                    .map(shuhaiInventoryReportMapper::toEntity)
                    .peek(e -> {
                        e.setDate(nowDate);
                        e.setUpdateTime(now);
                        Long id = oldIdMap.get(e.getCustomerMaterialNumber());
                        if (id != null) {
                            e.setId(id);
                        }
                    })
                    .toList();

            shuhaiInventoryReportRepository.saveAll(entityList);
        }
    }
}
