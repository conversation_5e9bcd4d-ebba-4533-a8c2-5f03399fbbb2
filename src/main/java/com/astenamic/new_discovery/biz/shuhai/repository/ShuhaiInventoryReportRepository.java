package com.astenamic.new_discovery.biz.shuhai.repository;

import com.astenamic.new_discovery.biz.shuhai.entity.ShuhaiInventoryReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ShuhaiInventoryReportRepository extends JpaRepository<ShuhaiInventoryReport, Long> {

    /**
     * 根据日期和客户仓库编码查询库存报告
     *
     * @param date          报告日期
     * @param warehouseCode 客户仓库编码
     * @return 库存报告列表
     */
    List<ShuhaiInventoryReport> findByDateAndCustomerWarehouseCode(LocalDate date, String warehouseCode);
}
