package com.astenamic.new_discovery.biz.shared.domain.repository;

import com.astenamic.new_discovery.biz.shared.domain.entity.BaseShop;

import java.util.List;
import java.util.Optional;

/**
 * 门店信息仓储接口
 */
public interface BaseShopRepository {


    /**
     * 查询所有门店信息
     * @return 门店信息列表
     */
    List<BaseShop> findAll();

    /**
     * 根据门店ID查询门店信息
     * @param shopId 门店ID
     * @return 门店信息
     */
    Optional<BaseShop> findByShopId(String shopId);

    /**
     * 根据门店名称模糊查询门店信息
     * @param shopName 门店名称
     * @return 门店信息列表
     */
    List<BaseShop> findByShopNameContaining(String shopName);

    /**
     * 检查门店ID是否存在
     * @param shopId 门店ID
     * @return 是否存在
     */
    boolean existsByShopId(String shopId);
}
