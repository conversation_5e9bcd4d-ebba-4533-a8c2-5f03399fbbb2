package com.astenamic.new_discovery.biz.shared.domain.entity;

import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 门店信息实体类
 * 对应数据库表：base_shop
 */
@Data
@Entity(name = "base_shop")
public class BaseShop {

    /** 门店id */
    @Id
    @Column(name = "shop_id", length = 50, nullable = false)
    private String shopId;

    /** 第三方门店id */
    @Column(name = "third_shop_id", length = 50)
    private String thirdShopId = "";

    /** 应用id */
    @Column(name = "app_id", length = 50, nullable = false)
    private String appId;

    /** 门店编码 */
    @Column(name = "shop_no", length = 50)
    private String shopNo = "";

    /** 门店名称 */
    @Column(name = "shop_name", length = 100)
    private String shopName = "";

    /** 门店机构类型 */
    @Column(name = "shop_type_id", length = 100)
    private String shopTypeId = "";

    /** 门店地址 */
    @Column(name = "address", length = 200)
    private String address = "";

    /** 纬度 */
    @Column(name = "shop_lat", length = 200)
    private String shopLat = "";

    /** 经度 */
    @Column(name = "shop_lon", length = 200)
    private String shopLon = "";

    /** 门店联系人姓名 */
    @Column(name = "link_man", length = 50)
    private String linkMan = "";

    /** 联系人电话 */
    @Column(name = "link_tele", length = 50)
    private String linkTele = "";

    /** 联系人手机 */
    @Column(name = "link_mobile", length = 50)
    private String linkMobile = "";

    /** 面积 */
    @Column(name = "shop_area", precision = 16, scale = 2)
    private BigDecimal shopArea;

    /** 状态0正常，1关店 */
    @Column(name = "status")
    private Integer status = 0;

    /** 城市 */
    @Column(name = "city", length = 50)
    private String city = "";

    /** 品牌id */
    @Column(name = "brand_id", length = 50)
    private String brandId = "";

    /** 公司id */
    @Column(name = "company_id", length = 50)
    private String companyId = "";

    /** 区域id */
    @Column(name = "region_id", length = 50)
    private String regionId = "";

    /** 部门id */
    @Column(name = "dept_id", length = 50)
    private String deptId = "";

    /** 归属类型，zhiying：直营，jiameng：加盟 */
    @Column(name = "ownership", length = 50)
    private String ownership = "";

    /** 创建时间 */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /** 修改时间 */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /** 最后更新时间 */
    @Column(name = "last_update_time")
    private LocalDateTime lastUpdateTime;

    /** 是否需要dz */
    @Column(name = "is_need_dz")
    private Integer isNeedDz = 0;

    /** 开店时间 */
    @Column(name = "open_date")
    private LocalDate openDate;

    /** 排序 */
    @Column(name = "sort", nullable = false)
    private Integer sort;

    /** 闭店时间 */
    @Column(name = "close_date")
    private LocalDate closeDate;

    /** 是否删除 */
    @Column(name = "is_del")
    private Integer isDel = 0;

    /** 组织id */
    @Column(name = "org_id", length = 50)
    private String orgId = "";

    /** 商户名称 */
    @Column(name = "business_name", length = 50)
    private String businessName = "";

    /** 省 */
    @Column(name = "province", length = 50)
    private String province = "";

    /** 区 */
    @Column(name = "district", length = 50)
    private String district = "";

    /** 经营模式 */
    @Column(name = "manage_mode", length = 10)
    private String manageMode = "";

    /** 员工人数 */
    @Column(name = "employee_number")
    private Integer employeeNumber;

    /** 关联供应商id */
    @Column(name = "supplier_id", length = 50)
    private String supplierId = "";

    /** 关联客户id */
    @Column(name = "customer_id", length = 50)
    private String customerId = "";

    /** 银行账户 */
    @Column(name = "bank_account", length = 50)
    private String bankAccount = "";

    /** 支付宝账户 */
    @Column(name = "alipay_account", length = 50)
    private String alipayAccount = "";

    /** 微信账户 */
    @Column(name = "wechat_account", length = 50)
    private String wechatAccount = "";

    /** 美团账号 */
    @Column(name = "meituan_account", length = 500)
    private String meituanAccount = "";

    /** eleme账号 */
    @Column(name = "eleme_account", length = 500)
    private String elemeAccount = "";

    /** 饿了么刷新 token 的 RefToken */
    @Column(name = "eleme_refresh_token", length = 500)
    private String elemeRefreshToken = "";

    /** 饿了么token到期时间 */
    @Column(name = "eleme_token_expires", length = 100)
    private String elemeTokenExpires;

    /** 增值税税率 */
    @Column(name = "tax_rate", length = 50)
    private String taxRate = "";

    /** 门店性质, 1-一般纳税人, 2-小规模纳税人 */
    @Column(name = "shop_quality")
    private Integer shopQuality = 1;

    /** 是否处理 */
    @Column(name = "is_dispose")
    private Integer isDispose;

    /** 指令打印机版本 */
    @Column(name = "print_commond_version", length = 50)
    private String printCommondVersion = "1.0";

    /** 负责人 */
    @Column(name = "manager_id", length = 50)
    private String managerId;

    /** 中心密码 */
    @Column(name = "zhongxin_pwd", length = 50)
    private String zhongxinPwd;

    /** 中心私钥 */
    @Column(name = "zhongxin_prikey", length = 100)
    private String zhongxinPrikey;

    /** 中心商户号 */
    @Column(name = "zhongxin_mch_no", length = 50)
    private String zhongxinMchNo;

    /** 对应支付宝后台编码 */
    @Column(name = "alipay_shop_no", length = 50)
    private String alipayShopNo;

    /** 门店线上id-对接品智需要 */
    @Column(name = "shop_on_line_id", length = 50)
    private String shopOnLineId;

    /** 美团营业时间 */
    @Column(name = "meituan_shipping_time", length = 100)
    private String meituanShippingTime;

    /** 饿了么营业时间 */
    @Column(name = "eleme_shipping_time", length = 100)
    private String elemeShippingTime;

    /** 原(旧)门店名称 */
    @Column(name = "shop_old_name", length = 50)
    private String shopOldName;

    /** 状态备注 */
    @Column(name = "status_remark", length = 50)
    private String statusRemark;

    /** 归属备注 */
    @Column(name = "ownership_remark", length = 50)
    private String ownershipRemark;

    /** 开店数据备注 */
    @Column(name = "open_data_remark", length = 50)
    private String openDataRemark;

    /** 适配器id */
    @Column(name = "adapter_id", length = 200)
    private String adapterId;

    /** 批次号 */
    @Column(name = "batch_number", length = 200)
    private String batchNumber;
}
