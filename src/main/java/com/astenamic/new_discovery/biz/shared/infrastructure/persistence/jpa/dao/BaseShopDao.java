package com.astenamic.new_discovery.biz.shared.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.shared.domain.entity.BaseShop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 门店信息 JPA 数据访问接口
 */
@Repository
public interface BaseShopDao extends JpaRepository<BaseShop, String> {

    /**
     * 根据门店ID查询门店信息
     * @param shopId 门店ID
     * @return 门店信息
     */
    Optional<BaseShop> findByShopId(String shopId);


    /**
     * 根据门店名称模糊查询门店信息
     * @param shopName 门店名称
     * @return 门店信息列表
     */
    List<BaseShop> findByShopNameContaining(String shopName);

    /**
     * 检查门店ID是否存在
     * @param shopId 门店ID
     * @return 是否存在
     */
    boolean existsByShopId(String shopId);


}
