package com.astenamic.new_discovery.biz.shared.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.shared.domain.entity.BaseShop;
import com.astenamic.new_discovery.biz.shared.domain.repository.BaseShopRepository;
import com.astenamic.new_discovery.biz.shared.infrastructure.persistence.jpa.dao.BaseShopDao;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 门店信息 JPA 适配器实现
 */
@Repository
public class BaseShopJpaAdapter implements BaseShopRepository {

    private final BaseShopDao baseShopDao;

    public BaseShopJpaAdapter(BaseShopDao baseShopDao) {
        this.baseShopDao = baseShopDao;
    }



    @Override
    public List<BaseShop> findAll() {
        try {
            return baseShopDao.findAll();
        } catch (Exception e) {
            throw new RuntimeException("查询所有门店信息失败：" + e.getMessage(), e);
        }
    }

    @Override
    public Optional<BaseShop> findByShopId(String shopId) {
        try {
            return baseShopDao.findByShopId(shopId);
        } catch (Exception e) {
            throw new RuntimeException("根据门店ID查询门店信息失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<BaseShop> findByShopNameContaining(String shopName) {
        try {
            return baseShopDao.findByShopNameContaining(shopName);
        } catch (Exception e) {
            throw new RuntimeException("根据门店名称模糊查询门店信息失败：" + e.getMessage(), e);
        }
    }
    

    @Override
    public boolean existsByShopId(String shopId) {
        try {
            return baseShopDao.existsByShopId(shopId);
        } catch (Exception e) {
            throw new RuntimeException("检查门店ID是否存在失败：" + e.getMessage(), e);
        }
    }
}
