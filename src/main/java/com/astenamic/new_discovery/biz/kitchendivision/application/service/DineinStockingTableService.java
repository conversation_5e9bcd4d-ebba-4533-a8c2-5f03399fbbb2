package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.SegmentedStockingPageDto;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.TakeawayMealSegmentedStockingPageDto;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.SegmentedStockingTemplateConfig;
import com.astenamic.new_discovery.biz.kitchendivision.domain.repository.BhSfmReportRepository;
import com.astenamic.new_discovery.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DineinStockingTableService {

    public final BhSfmReportRepository bhSfmReportRepository;

    public final SegmentedStockingTemplateConfigAppService segmentedStockingTemplateConfigAppService;


    public List<SegmentedStockingPageDto> buildPages(String shopId, LocalDateTime targetDate) {

        List<SegmentedStockingPageDto> pages = new ArrayList<>();

        // 获取所有模板配置
        List<SegmentedStockingTemplateConfig> configs = getTemplateConfigs();

        for (SegmentedStockingTemplateConfig config : configs) {
            SegmentedStockingPageDto page = buildPage(shopId, targetDate, config);
            pages.add(page);
        }

        return pages;
    }

    /**
     * 构建分段备货页面
     *
     * @param config 分段备货模板配置
     * @return 页面对象
     */
    public SegmentedStockingPageDto buildPage(String shopId, LocalDateTime targetDate, SegmentedStockingTemplateConfig config) {
        if (config == null || config.getFormConfigList() == null || config.getDishMaterialInfoList() == null) {
            return new SegmentedStockingPageDto(targetDate);
        }

        SegmentedStockingPageDto page = new SegmentedStockingPageDto(targetDate);

        // 设置页面信息
        page.setTitle(config.getName());
        page.setRemark(config.getRemark());
        page.setType(config.getType());

        // 构建行表头（时间配置）
        List<SegmentedStockingPageDto.TimeConfigDto> rowHeaders = config.getFormConfigList().stream()
                .map(formConfig -> {
                    SegmentedStockingPageDto.TimeConfigDto timeConfig = new SegmentedStockingPageDto.TimeConfigDto();
                    timeConfig.setTimeNode(formConfig.getTimeNode());
                    timeConfig.setUsageTimeLimit(formConfig.getUsageTimeLimit());
                    timeConfig.setForecastDataTimePeriod(formConfig.getForecastDataTimePeriod());
                    // 根据时间自动判断餐段
                    timeConfig.setCoefficient(formConfig.getCoefficient());
                    timeConfig.setMealPeriod(determineMealPeriod(formConfig.getTimeNode()));
                    return timeConfig;
                })
                .collect(Collectors.toList());

        // 计算餐段的rowspan
        calculateMealPeriodRowSpan(rowHeaders);
        page.setRowHeaders(rowHeaders);

        // 构建列表头（菜品物料信息）- 按materialGroup分组
        Map<String, List<SegmentedStockingPageDto.DishMaterialHeaderDto>> columnHeaders = config.getDishMaterialInfoList().stream()
                .map(dishMaterial -> {
                    SegmentedStockingPageDto.DishMaterialHeaderDto header = new SegmentedStockingPageDto.DishMaterialHeaderDto();
                    header.setMaterialGroup(dishMaterial.getMaterialGroup());
                    header.setDishCode(dishMaterial.getDishCode());
                    header.setMaterialGroupCode(dishMaterial.getMaterialGroupCode());
                    header.setRatio(dishMaterial.getRatio());
                    header.setUnitCode(dishMaterial.getUnitCode());
                    header.setUnitName(dishMaterial.getUnitName());
                    return header;
                })
                .collect(Collectors.groupingBy(SegmentedStockingPageDto.DishMaterialHeaderDto::getMaterialGroup));
        page.setColumnHeaders(columnHeaders);

        // 构建表格数据矩阵
        int rowCount = rowHeaders.size();
        int colCount = columnHeaders.size();
        BigDecimal[][] dataMatrix = new BigDecimal[rowCount][colCount];

        // 根据时间配置和菜品物料信息计算数据
        for (int i = 0; i < rowCount; i++) {
            SegmentedStockingPageDto.TimeConfigDto timeConfig = rowHeaders.get(i);

            // 判断是否是"现来现备"行，如果是则跳过数量计算
            boolean isOnDemandRow = isOnDemandPreparation(timeConfig);

            int j = 0;
            for (String materialGroup : columnHeaders.keySet()) {
                if (isOnDemandRow) {
                    // "现来现备"行不需要计算数量，设置为0
                    dataMatrix[i][j] = BigDecimal.ZERO;
                } else {
                    // 正常行：根据时间节点和物料组信息计算对应的数量
                    List<SegmentedStockingPageDto.DishMaterialHeaderDto> materialHeaders = columnHeaders.get(materialGroup);
                    BigDecimal quantity = calculateQuantity(shopId, targetDate, timeConfig, materialHeaders);
                    dataMatrix[i][j] = quantity;
                }
                j++;
            }
        }

        page.setDataMatrix(dataMatrix);

        return page;
    }


    /**
     * 计算特定时间配置和物料组对应的数量
     *
     * @param timeConfig      时间配置
     * @param materialHeaders 物料组下的所有物料信息
     * @return 计算得出的数量
     */
    private BigDecimal calculateQuantity(String shopId, LocalDateTime targetDate, SegmentedStockingPageDto.TimeConfigDto timeConfig,
                                         List<SegmentedStockingPageDto.DishMaterialHeaderDto> materialHeaders) {

        if (materialHeaders == null || materialHeaders.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 从时间配置中获取预测数据时间段
        List<LocalDateTime> forecastDataTimePeriod = timeConfig.getForecastDataTimePeriod();
        if (forecastDataTimePeriod == null || forecastDataTimePeriod.size() < 2) {
            return BigDecimal.ZERO;
        }

        // 构建查询参数
        String dayFormat = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 时段查询参数
        List<String> hourIntervals = buildHourIntervals(forecastDataTimePeriod.get(0).toLocalTime(), forecastDataTimePeriod.get(1).toLocalTime());

        BigDecimal result = BigDecimal.ZERO;
        for (SegmentedStockingPageDto.DishMaterialHeaderDto material : materialHeaders) {
            BigDecimal coefficient = timeConfig.getCoefficient();
            BigDecimal ratio = material.getRatio();
            String unitCode = material.getUnitCode();
            if (coefficient == null || coefficient.equals(BigDecimal.ZERO)) {
                continue;
            }

            String foodCode = material.getDishCode();
            String materialNoHb = material.getMaterialGroupCode();

            try {
                BigDecimal qtyForecast = bhSfmReportRepository.sumQtyForecastByConditions(
                        dayFormat, hourIntervals, shopId, foodCode, materialNoHb,unitCode);

                BigDecimal materialQuantity = qtyForecast.multiply(coefficient).multiply(ratio);
                result = result.add(materialQuantity);
            } catch (Exception e) {
                continue;
            }
        }
        return result.setScale(0, RoundingMode.HALF_UP);
    }

    /**
     * 规则：
     * 1. 以 endTime 为当前段的结束点 curEnd。
     * 2. 如果 curEnd 的分钟数 == 15，则本段起点 = curEnd - 15 分钟；
     * 否则本段起点 = curEnd - 14 分钟。（这样保证重叠 1 分钟）
     * 3. 输出  curStart~curEnd  这一段。
     * 4. 把 curEnd 往前再推 15 分钟，继续下一轮，直到 curStart 早于 startTime。
     */
    public static List<String> buildHourIntervals(LocalTime startTime, LocalTime endTime) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("HH:mm");
        List<String> segments = new ArrayList<>();

        LocalTime curEnd = endTime;

        while (!curEnd.isBefore(startTime)) {
            int minute = curEnd.getMinute();
            int backStep = (minute == 15) ? 15 : 14;
            LocalTime curStart = curEnd.minusMinutes(backStep);

            if (curStart.isBefore(startTime)) {
                curStart = startTime;
            }

            segments.add(curStart.format(fmt) + "~" + curEnd.format(fmt));

            if (curStart.equals(startTime)) {
                break;
            }
            curEnd = curEnd.minusMinutes(15);
        }
        return segments;
    }



    /**
     * 根据时间节点自动判断餐段
     *
     * @param timeNode 时间节点（如"9:30"、"15:00"等）
     * @return 餐段名称（上午、下午）
     */
    private String determineMealPeriod(String timeNode) {
        if (timeNode == null) {
            return "上午"; // 默认值
        }

        // 提取时间部分（去掉换行和批次信息）
        String timeStr = timeNode.split("\n")[0].trim();

        try {
            // 解析时间（格式如"9:30"、"15:00"）
            String[] timeParts = timeStr.split(":");
            if (timeParts.length >= 2) {
                int hour = Integer.parseInt(timeParts[0]);

                // 根据小时判断餐段
                // 12:00之前为上午，12:00及之后为下午
                if (hour < 12) {
                    return "上午";
                } else {
                    return "下午";
                }
            }
        } catch (NumberFormatException e) {
            // 解析失败时返回默认值
            return "上午";
        }

        return "上午"; // 默认值
    }

    /**
     * 计算餐段的rowspan
     *
     * @param rowHeaders 行表头列表
     */
    private void calculateMealPeriodRowSpan(List<SegmentedStockingPageDto.TimeConfigDto> rowHeaders) {
        if (rowHeaders == null || rowHeaders.isEmpty()) {
            return;
        }

        // 按餐段分组计算rowspan
        Map<String, Integer> mealPeriodCounts = new LinkedHashMap<>();
        for (SegmentedStockingPageDto.TimeConfigDto timeConfig : rowHeaders) {
            String mealPeriod = timeConfig.getMealPeriod();
            if (mealPeriod != null) {
                mealPeriodCounts.put(mealPeriod, mealPeriodCounts.getOrDefault(mealPeriod, 0) + 1);
            }
        }

        // 设置每个时间配置的餐段信息
        Map<String, Boolean> firstRowFlags = new LinkedHashMap<>();
        for (SegmentedStockingPageDto.TimeConfigDto timeConfig : rowHeaders) {
            String mealPeriod = timeConfig.getMealPeriod();
            if (mealPeriod != null) {
                // 判断是否是该餐段的第一行
                boolean isFirst = !firstRowFlags.getOrDefault(mealPeriod, false);
                timeConfig.setFirstRowOfPeriod(isFirst);
                timeConfig.setPeriodRowSpan(mealPeriodCounts.get(mealPeriod));

                if (isFirst) {
                    firstRowFlags.put(mealPeriod, true);
                }
            }
        }
    }

    /**
     * 判断是否是"现来现备"行
     *
     * @param timeConfig 时间配置
     * @return 是否是现来现备行
     */
    private boolean isOnDemandPreparation(SegmentedStockingPageDto.TimeConfigDto timeConfig) {
        // 如果使用时限为空且预测数据时间段也为空，则是"现来现备"
        boolean usageTimeLimitEmpty = timeConfig.getUsageTimeLimit() == null || timeConfig.getUsageTimeLimit().trim().isEmpty();
        boolean forecastDataEmpty = timeConfig.getForecastDataTimePeriod() == null || timeConfig.getForecastDataTimePeriod().isEmpty();

        return usageTimeLimitEmpty && forecastDataEmpty;
    }


    /**
     * 获取模板配置
     */
    private List<SegmentedStockingTemplateConfig> getTemplateConfigs() {
        List<SegmentedStockingTemplateConfig> configs = segmentedStockingTemplateConfigAppService.getAllConfigsWithSubTables();
        if (configs == null || configs.isEmpty()) {
            log.error("未查询到分段备货模板配置！");
            throw new BizException("未查询到分段备货模板配置");
        }
        return configs;
    }
}
