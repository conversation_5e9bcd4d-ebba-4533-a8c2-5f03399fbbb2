package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.SegmentedStockingTemplateConfig;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 分段备货模板配置应用服务
 * 负责分段备货模板配置表单数据的处理和管理
 */
@Slf4j
@Service
public class SegmentedStockingTemplateConfigAppService extends AbstractFormService<SegmentedStockingTemplateConfig> {

    /**
     * 构造函数
     *
     * @param yiDaSession          宜搭会话
     * @param yiDaSessionV2        宜搭会话V2
     * @param yidaConfigProperties 宜搭配置属性
     */
    public SegmentedStockingTemplateConfigAppService(YiDaSession yiDaSession,
                                                     YiDaSessionV2 yiDaSessionV2,
                                                     YidaConfigProperties yidaConfigProperties) {
        super(SegmentedStockingTemplateConfig.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    /**
     * 根据名称查询分段备货模板配置
     *
     * @param name 模板名称
     * @return 分段备货模板配置列表
     */
    public List<SegmentedStockingTemplateConfig> getConfigsByName(String name) {
        if (name == null) {
            return new ArrayList<>();
        }
        SearchConditions cond = SearchCondition.builder()
                .textEq(SegmentedStockingTemplateConfig.class, SegmentedStockingTemplateConfig::getName, name, "+")
                .get();
        List<SegmentedStockingTemplateConfig> results = super.getFormsByCond(cond);
        // 填充子表数据
        results.forEach(this::populateSubTableData);

        return results;
    }

    /**
     * 获取所有分段备货模板配置（包含子表数据）
     *
     * @return 分段备货模板配置列表
     */
    public List<SegmentedStockingTemplateConfig> getAllConfigsWithSubTables() {
        List<SegmentedStockingTemplateConfig> results = super.getFormsByCond(SearchCondition.builder().get());
        // 填充子表数据
        results.forEach(this::populateSubTableData);
        return results;
    }

    /**
     * 填充子表数据
     *
     * @param config 分段备货模板配置
     */
    private void populateSubTableData(SegmentedStockingTemplateConfig config) {
        if (config == null || config.getObjectId() == null) {
            return;
        }

        try {
            // 查询菜品物料表数据
            List<SegmentedStockingTemplateConfig.DishMaterialInfo> dishMaterialInfoList =
                    queryInnerList(SegmentedStockingTemplateConfig.DishMaterialInfo.class, config.getObjectId());
            config.setDishMaterialInfoList(dishMaterialInfoList);

            // 查询表单配置数据
            List<SegmentedStockingTemplateConfig.FormConfig> formConfigList =
                    queryInnerList(SegmentedStockingTemplateConfig.FormConfig.class, config.getObjectId());
            config.setFormConfigList(formConfigList);

        } catch (Exception e) {
            log.error("填充分段备货模板配置子表数据失败, objectId: {}, error: {}", config.getObjectId(), e.getMessage(), e);
        }
    }

}
