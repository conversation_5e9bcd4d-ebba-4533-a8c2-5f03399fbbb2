package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 备货表物料备货预估量报表表复合主键
 */
@Data
@EqualsAndHashCode
public class BhSfmReportId implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    private String dayFormat;

    /**
     * 时段区间
     */
    private String hourInterval;

    /**
     * 门店id
     */
    private String shopId;

    /**
     * 菜品单位id
     */
    private String foodUnitId;

    /**
     * 菜品做法id
     */
    private String foodCookId;

    /**
     * 菜品id
     */
    private String foodId;

    /**
     * 物料编码合并
     */
    private String materialNoHb;
}
