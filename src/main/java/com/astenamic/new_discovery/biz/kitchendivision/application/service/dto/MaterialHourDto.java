package com.astenamic.new_discovery.biz.kitchendivision.application.service.dto;

import lombok.Data;

@Data
public class MaterialHourDto {

    /**
     * 日期
     */
    private String date;

    /**
     * 开始时间：时段一对应 9:00,时段二对应 16:00
     */
    private String startDate;

    /**
     * 结束时间：时段一对应 10:00,时段二对应 17:00
     */
    private String endDate;

    /**
     * 原料名称：备料及工作事项-具体事项与要求
     */
    private String name;

    /**
     * 备货量
     */
    private Float stockQuantity;

    /**
     * 备货单位
     */
    private String unitName;

}
