package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.FoodSalePredicate;
import com.astenamic.new_discovery.biz.report.food.repository.FoodSalePredicateRepository;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Service
public class FoodSalePredicateService {
    private final FoodSalePredicateRepository foodSalePredicateRepository;
    private final DateTimeFormatter dateTimeFormatter;
    private final ShopRelationAppService shopRelationAppService;
    private final YiDaSession yiDaSession;
    private final Integer limit = 50;

    private final Logger logger = LoggerFactory.getLogger(FoodSalePredicateService.class);

    public FoodSalePredicateService(FoodSalePredicateRepository foodSalePredicateRepository,
                                    @Qualifier("DateTimeYMDFormatter") DateTimeFormatter dateTimeFormatter,
                                    YiDaSession yiDaSession,
                                    ShopRelationAppService shopRelationAppService) {
        this.foodSalePredicateRepository = foodSalePredicateRepository;
        this.dateTimeFormatter = dateTimeFormatter;
        this.yiDaSession = yiDaSession;
        this.shopRelationAppService = shopRelationAppService;
    }

    // 预测数据更新到宜搭
    public List<FoodSalePredicate> foodSaleToYiDa(LocalDateTime day) {

        logger.info("同步预测数据: {}", day);

        day = day.withHour(0).withMinute(0).withSecond(0).withNano(0);

        try {
            //删除一星期前的数据
            List<FoodSalePredicate> data = this.findAndDeleteFoodSaleReportByDate(day);
        } catch (Exception e) {
            logger.error("删除7天前数据失败{}", System.lineSeparator(), e);
        }

        // 宜搭的当天的数据
        List<FoodSalePredicate> oldData = this.getOldDataToolByYida(day);

        String dayStr = dateTimeFormatter.format(day);

        // 接口数据
        List<FoodSalePredicate> newData = this.foodSalePredicateRepository.findByDate(dayStr);

        logger.info("获取宜搭数据: {}", oldData.size());
        logger.info("获取预测数据: {}", newData.size());

        newData = this.relateDailyReport(newData);

        List<FoodSalePredicate> update = new ArrayList<>();
        List<FoodSalePredicate> save = new ArrayList<>();

        for (FoodSalePredicate datum : newData) {
            for (FoodSalePredicate oldDatum : oldData) {
                if (StringUtils.equals(datum.getShopId(), oldDatum.getShopId()) && StringUtils.equals(datum.getFoodCode(), oldDatum.getFoodCode())) {
                    if (StringUtils.equals(datum.getFoodUnit(), oldDatum.getFoodUnit())) {
                        datum.setObjectId(oldDatum.getObjectId());
                        break;
                    }
                }
            }
        }

        for (FoodSalePredicate datum : newData) {
            if (datum.getObjectId() == null) {
                save.add(datum);
            } else {
                update.add(datum);
            }
        }
        logger.info("需要更新的数据: {}", update.size());
        logger.info("需要保存的数据: {}", save.size());

        List<List<FoodSalePredicate>> updates = this.splitCollection(update, 20);
        for (List<FoodSalePredicate> foodSalePredicates : updates) {
            this.yiDaSession.batchUpdateDataByObjectId(foodSalePredicates, FoodSalePredicate.class);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
            }
        }

        List<List<FoodSalePredicate>> saves = this.splitCollection(save, 20);
        for (List<FoodSalePredicate> foodSalePredicates : saves) {
            this.yiDaSession.batchSave(foodSalePredicates, FoodSalePredicate.class);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
            }
        }
        return newData;
    }

    public List<FoodSalePredicate> findAndDeleteFoodSaleReportByDate(LocalDateTime day) {

        logger.info("删除7天前预测数据: {}", day);

        day = day.minusDays(8);
        // 转换为时间戳（毫秒）
        long timestampMillis = TimeUtils.toEpochMilli(day);

        int page = 1;
        int len = 0;

        SearchConditions conditions = SearchCondition.builder().dateLe("dateField_m4m1yv3n", timestampMillis, "+").get();

        List<FoodSalePredicate> all = new ArrayList<>();
        do {

            List<FoodSalePredicate> has = this.yiDaSession.searchFormDataConditionsRequest(FoodSalePredicate.class, conditions, page);

            all.addAll(has);

            len = has.size();

            List<List<FoodSalePredicate>> groups;

            if (!has.isEmpty()) {

                groups = this.splitCollection(has, this.limit);

                for (List<FoodSalePredicate> group : groups) {

                    Boolean success = this.yiDaSession.batchDeleteByObjectId(group, FoodSalePredicate.class);

                    if (!success) {
                        throw new IllegalArgumentException("删除失败");
                    }
                }
            }

        } while (len == 100);
        logger.info("删除7天前预测数据完成: {}", all.size());
        return all;
    }

    private <T> List<List<T>> splitCollection(Collection<T> collection, int size) {
        List<List<T>> result = new ArrayList<>();
        List<T> currentList = new ArrayList<>(size);

        for (T element : collection) {
            currentList.add(element);
            if (currentList.size() == size) {
                result.add(new ArrayList<>(currentList));
                currentList.clear();
            }
        }

        // 如果最后的子集合不为空，将其加入结果列表
        if (!currentList.isEmpty()) {
            result.add(new ArrayList<>(currentList));
        }

        return result;
    }

    public <T extends FoodSalePredicate> List<T> relateDailyReport(List<T> data) {

        if (data == null) {
            return null;
        }

        List<ShopRelation> relations = this.shopRelationAppService.getRelationsByClosed("否");

        data.forEach(report -> {

            for (ShopRelation relation : relations) {
                if (Objects.equals(report.getShopId(), relation.getShopId())) {
                    report.setShopSysId(getListValue(relation.getShopSysId()));
                    report.setBrandSysId(getListValue(relation.getBrandSysId()));
                    break;
                }
            }
        });

        return data.stream().filter(f -> f.getShopSysId() != null).toList();
    }

    private String getListValue(List<String> list) {
        return Optional.ofNullable(list)
                .filter(vs -> !vs.isEmpty())
                .map(vs -> vs.get(0))
                .orElse("");
    }

    public List<FoodSalePredicate> getOldDataByYiDa(LocalDateTime day) {

        int page = 1;
        int len = 0;
        long[] dates = TimeUtils.getDayRange(day);

        // 根据日期筛选
        SearchConditions conditions = SearchCondition.builder().dateBetween("dateField_m4m1yv3n", dates, "+").get();

        List<FoodSalePredicate> all = new ArrayList<>();
        do {

            List<FoodSalePredicate> has = this.yiDaSession.searchFormDataConditionsRequest(FoodSalePredicate.class, conditions, page);

            all.addAll(has);

            len = has.size();

            page++;

        } while (len == 100);

        return all;

    }

    public List<FoodSalePredicate> getOldDataToolByYida(LocalDateTime day) {
        logger.info("获取宜搭数据开始: {}", day);

        int page = 1;
        int len = 0;
        long[] dates = TimeUtils.getDayRange(day);

        // 根据日期筛选
        SearchConditions conditions = SearchCondition.builder().dateBetween("dateField_m4m1yv3n", dates, "+").get();

        List<FoodSalePredicate> all = new ArrayList<>();
        do {

            List<FoodSalePredicate> has = this.yiDaSession.searchFormDataConditionsRequest(FoodSalePredicate.class, conditions, page);

            all.addAll(has);

            len = has.size();

            page++;

        } while (len == 100);

        return all;
    }
}
