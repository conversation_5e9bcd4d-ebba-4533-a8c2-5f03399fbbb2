package com.astenamic.new_discovery.biz.kitchendivision.application.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 一周日期分类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeekDatesClassification {

    /**
     * 低位日（周一到周四）
     */
    private List<String> lowDays;

    /**
     * 中位日（周五）
     */
    private List<String> medianDays;

    /**
     * 高位日（周末：周六、周日）
     */
    private List<String> highDays;
}
