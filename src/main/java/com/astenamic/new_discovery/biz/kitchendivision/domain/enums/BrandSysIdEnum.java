package com.astenamic.new_discovery.biz.kitchendivision.domain.enums;

import lombok.Getter;

@Getter
public enum BrandSysIdEnum {

    XFX("新发现", "142339402,110072107"),
    K<PERSON>("烤匠", "109897803"),
    SJF<PERSON>("四季风情", "110014854"),
    HDL("蝴蝶里", "129043320"),
    DEFAULT("默认", ""),
    ;

    private final String brandName;

    private final String brandSysId;

    BrandSysIdEnum(String brandName, String brandSysId) {
        this.brandName = brandName;
        this.brandSysId = brandSysId;
    }

    public static BrandSysIdEnum getByBrandName(String brandName) {
        for (BrandSysIdEnum brandSysIdEnum : BrandSysIdEnum.values()) {
            if (brandSysIdEnum.getBrandName().equals(brandName)) {
                return brandSysIdEnum;
            }
        }
        return DEFAULT;
    }

    public static BrandSysIdEnum getByBrandSysId(String brandSysId) {
        for (BrandSysIdEnum brandSysIdEnum : BrandSysIdEnum.values()) {
            if (brandSysIdEnum.getBrandSysId().equals(brandSysId)) {
                return brandSysIdEnum;
            }
        }
        return DEFAULT;
    }
}
