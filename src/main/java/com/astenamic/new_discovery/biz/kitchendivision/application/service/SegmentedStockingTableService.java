package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.TakeawayMealSegmentedStockingPageDto;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.SegmentedStockingTemplateConfig;
import com.astenamic.new_discovery.interfaces.rest.yida.kitchendivision.dto.PdfGenerateDto;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.SegmentedStockingPageDto;
import com.astenamic.new_discovery.biz.kitchendivision.domain.repository.BhSfmReportRepository;
import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.util.HtmlToPdfUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分段备货页面服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SegmentedStockingTableService {

    public final ShopRelationAppService shopRelationAppService;

    public final DineinStockingTableService dineinStockingTableService;

    public final TakeawayStockingTableService takeawayStockingTableService;

    /**
     * 生成分段备货表PDF
     *
     * @param sysShopId  系统门店ID
     * @param targetDate 目标日期
     */
    public PdfGenerateDto generatePdf(String sysShopId, LocalDateTime targetDate) {
        log.info("开始生成分段备货表PDF，门店ID: {}, 目标日期: {}", sysShopId, targetDate);

        try {
            // 1. 参数验证
            validateGeneratePdfParams(sysShopId, targetDate);

            // 2. 获取门店信息
            ShopRelation shopRelation = getShopRelation(sysShopId);

            long begin = System.currentTimeMillis();

            // 4. 生成PDF字节数组
            byte[] pdfBytes = generatePdfBytes(shopRelation.getShopId(), targetDate);

            long end = System.currentTimeMillis();
            System.out.println("-------------生成PDF耗时-------------: " + (end - begin)/1000 + "s");
            // 5. 生成文件名
            String fileName = generatePdfFileName(shopRelation.getShopAoqiweiName(), targetDate);

            return new PdfGenerateDto(pdfBytes, fileName, shopRelation.getShopAoqiweiName());

        } catch (BizException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("生成分段备货表PDF异常，门店ID: {}", sysShopId, e);
            throw new BizException("生成PDF失败: " + e.getMessage());
        }
    }

    /**
     * 参数验证
     */
    private void validateGeneratePdfParams(String sysShopId, LocalDateTime targetDate) {
        if (StringUtils.isBlank(sysShopId)) {
            throw new BizException("门店ID不能为空");
        }
        if (targetDate == null) {
            throw new BizException("目标日期不能为空");
        }
    }

    /**
     * 获取门店关系信息
     */
    private ShopRelation getShopRelation(String sysShopId) {
        ShopRelation shopRelation = shopRelationAppService.getRelationByShopSysId(sysShopId);
        if (shopRelation == null || StringUtils.isBlank(shopRelation.getShopId())) {
            log.error("未查询到门店信息！shopSysId={}", sysShopId);
            throw new BizException("未查询到门店信息，门店ID: " + sysShopId);
        }
        return shopRelation;
    }

    /**
     * 生成PDF字节数组
     */
    private byte[] generatePdfBytes(String shopId, LocalDateTime targetDate) {
        List<byte[]> pdfBytesList = new ArrayList<>();

        /**
         * 堂食分段备货
         */
        List<SegmentedStockingPageDto> pages = dineinStockingTableService.buildPages(shopId, targetDate);
        for (SegmentedStockingPageDto page : pages) {
            try {
                byte[] pageBytes = page.toPdf();
                pdfBytesList.add(pageBytes);
                log.debug("模板 {} 生成PDF成功，大小: {} bytes", page.getTitle(), pageBytes.length);
            } catch (Exception e) {
                log.error("生成模板 {} 的PDF失败", page.getTitle(), e);
            }
        }

        /**
         * 外卖套餐分段备货表
         */
        List<TakeawayMealSegmentedStockingPageDto> takeawayMealSegmentedStockingPageDtos = takeawayStockingTableService.buildPages(shopId, targetDate);
        for (TakeawayMealSegmentedStockingPageDto page : takeawayMealSegmentedStockingPageDtos) {
            try {
                byte[] pageBytes = page.toPdf();
                pdfBytesList.add(pageBytes);
                log.debug("模板 {} 生成PDF成功，大小: {} bytes", page.getTitle(), pageBytes.length);
            } catch (Exception e) {
                log.error("生成模板 {} 的PDF失败", page.getTitle(), e);
            }
        }
        if (pdfBytesList.isEmpty()) {
            throw new BizException("所有模板PDF生成失败");
        }
        try {
            log.info("开始合并PDF文件，文件数量: {}", pdfBytesList.size());
            return HtmlToPdfUtil.mergePdfs(pdfBytesList);
        } catch (IOException e) {
            log.error("合并PDF失败", e);
            throw new BizException("合并PDF失败: " + e.getMessage());
        }
    }


    /**
     * 生成PDF文件名
     */
    private String generatePdfFileName(String shopName, LocalDateTime targetDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateStr = targetDate.format(formatter);
        return String.format("%s_分段备货表_%s.pdf", shopName, dateStr);
    }

}
