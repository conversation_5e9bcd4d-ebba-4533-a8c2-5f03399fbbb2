package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.TakeawayMealSegmentedStockingPageDto;
import com.astenamic.new_discovery.biz.kitchendivision.domain.repository.BhSfmReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class TakeawayStockingTableService {


    public final BhSfmReportRepository bhSfmReportRepository;

    public List<TakeawayMealSegmentedStockingPageDto> buildPages(String shopId, LocalDateTime targetDate) {
        return List.of(
                buildPage(shopId, targetDate, getDishConfigs(shopId)),
                buildPage(shopId, targetDate, getSmallDishConfigs(shopId))
        );
    }

    /**
     * 构建外卖套餐分段备货表页面
     *
     * @param shopId     门店ID
     * @param targetDate 目标日期
     * @return 页面DTO
     */
    public TakeawayMealSegmentedStockingPageDto buildPage(String shopId, LocalDateTime targetDate, List<DishCache> dishConfigs) {
        log.info("开始构建外卖套餐分段备货表页面，门店ID: {}, 目标日期: {}", shopId, targetDate);

        // 创建页面DTO
        TakeawayMealSegmentedStockingPageDto pageDto = new TakeawayMealSegmentedStockingPageDto(targetDate);


        // 构建菜品表头
        buildDishHeaders(pageDto, dishConfigs);

        // 计算并填充数据矩阵
        calculateAndFillDataMatrix(pageDto, shopId, targetDate, dishConfigs);

        // 计算汇总数据
        calculateSummaryData(pageDto);

        log.info("外卖套餐分段备货表页面构建完成，菜品数量: {}", dishConfigs.size());
        return pageDto;
    }

    private List<DishCache> getDishConfigs(String shopId) {
        return List.of(
                new DishCache("2020121", "糖醋里脊（套餐）", true),
                new DishCache("2020127", "杭椒炒黄牛肉（套餐）", true),
                new DishCache("2020123", "辣椒小炒肉（套餐）", true),
                new DishCache("2020126", "豆花酸菜鱼（套餐）", true),
                new DishCache("2020120", "麻辣口水鸡（套餐）", true),
                new DishCache("2020166", "牛肉焖黄心土豆（套餐）", true),
                new DishCache("2020140", "嫩滑蒸蛋", true)
        );
    }

    private List<DishCache> getSmallDishConfigs(String shopId) {
        List<DishCache> dishConfigs = getDishConfigs(shopId);
        List<DishCache> smallDishConfigs = List.of(
                new DishCache("00088", "（小份）杭椒现炒黄牛肉", false),
                new DishCache("2010001", "豆花酸菜鱼小份", false),
                new DishCache("2010002", "干锅土豆片小份", false),
                new DishCache("2010003", "花菜炒肉片小份", false),
                new DishCache("2010004", "金针酸汤肥牛小份", false)
        );
// 合并两个list
        List<DishCache> result = new ArrayList<>(dishConfigs);
        result.addAll(smallDishConfigs);
        return result;
    }

    public record DishCache(String dishCode, String dishName, boolean isStatistics) {
    }

    ;

    /**
     * 构建菜品表头
     */
    private void buildDishHeaders(TakeawayMealSegmentedStockingPageDto pageDto, List<DishCache> dishConfigs) {
        for (DishCache entry : dishConfigs) {
            pageDto.addDishHeader(entry.dishCode, entry.dishName, entry.isStatistics);
        }
    }


    /**
     * 计算并填充数据矩阵
     */
    private void calculateAndFillDataMatrix(TakeawayMealSegmentedStockingPageDto pageDto,
                                            String shopId, LocalDateTime targetDate,
                                            List<DishCache> dishConfigs) {

        for (DishCache entry : dishConfigs) {
            String dishCode = entry.dishCode();
            String dishName = entry.dishName();

            // 计算午市数据
            calculateMealPeriodData(pageDto, shopId, targetDate, dishCode, dishName, "lunch");

            // 计算晚市数据
            calculateMealPeriodData(pageDto, shopId, targetDate, dishCode, dishName, "dinner");
        }
    }

    /**
     * 计算餐段数据
     */
    private void calculateMealPeriodData(TakeawayMealSegmentedStockingPageDto pageDto,
                                         String shopId, LocalDateTime targetDate,
                                         String dishCode, String dishName, String mealPeriod) {

        // 获取该餐段的时间段列表
        List<TakeawayMealSegmentedStockingPageDto.TimeSlotHeaderDto> timeSlots = pageDto.getTimeSlotHeaders()
                .stream()
                .filter(slot -> mealPeriod.equals(slot.getMealPeriod()))
                .toList();

        for (TakeawayMealSegmentedStockingPageDto.TimeSlotHeaderDto timeSlot : timeSlots) {
            // 计算该时间段的数量
            Integer quantity = calculateQuantity(shopId, targetDate, dishCode, timeSlot);

            // 设置到数据矩阵中
            pageDto.setData(dishCode, timeSlot.getTimeSlotId(), quantity);
        }
    }


    /**
     * 计算指定时间段的数量
     */
    private Integer calculateQuantity(String shopId, LocalDateTime targetDate, String dishCode, TakeawayMealSegmentedStockingPageDto.TimeSlotHeaderDto timeSlot) {
        String hourInterval = timeSlot.getTimeSlotText();
        String dayFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(targetDate);
        BigDecimal qtyForecast = bhSfmReportRepository.findQtyForecast(shopId, dayFormat, dishCode, hourInterval);
        if (qtyForecast == null) {
            return 0;
        }
        return qtyForecast.setScale(0, RoundingMode.CEILING).intValue();
    }

    /**
     * 计算汇总数据
     */
    private void calculateSummaryData(TakeawayMealSegmentedStockingPageDto pageDto) {
        // 计算午市汇总
        Integer lunchPeriod1 = calculatePeriodSummary(pageDto, "lunch", "period1");
        Integer lunchPeriod2 = calculatePeriodSummary(pageDto, "lunch", "period2");
        Integer lunchPeriod3 = calculatePeriodSummary(pageDto, "lunch", "period3");
        pageDto.setLunchSummary(lunchPeriod1, lunchPeriod2, lunchPeriod3);

        // 计算晚市汇总
        Integer dinnerPeriod1 = calculatePeriodSummary(pageDto, "dinner", "period1");
        Integer dinnerPeriod2 = calculatePeriodSummary(pageDto, "dinner", "period2");
        Integer dinnerPeriod3 = calculatePeriodSummary(pageDto, "dinner", "period3");
        pageDto.setDinnerSummary(dinnerPeriod1, dinnerPeriod2, dinnerPeriod3);
    }

    /**
     * 计算期间汇总
     */
    private Integer calculatePeriodSummary(TakeawayMealSegmentedStockingPageDto pageDto,
                                           String mealPeriod, String summaryGroup) {
        // 获取该期间的所有时间段
        List<TakeawayMealSegmentedStockingPageDto.TimeSlotHeaderDto> periodSlots = pageDto.getTimeSlotHeaders()
                .stream()
                .filter(slot -> mealPeriod.equals(slot.getMealPeriod()) && summaryGroup.equals(slot.getSummaryGroup()))
                .toList();

        int total = 0;
        for (TakeawayMealSegmentedStockingPageDto.DishHeaderDto dishHeader : pageDto.getDishHeaders()) {
            for (TakeawayMealSegmentedStockingPageDto.TimeSlotHeaderDto timeSlot : periodSlots) {
                if (dishHeader.isStatistics()) {
                    Integer quantity = pageDto.getData(dishHeader.getDishCode(), timeSlot.getTimeSlotId());
                    if (quantity != null && quantity > 0) {
                        total += quantity;
                    }
                }

            }
        }
        return total;
    }

}
