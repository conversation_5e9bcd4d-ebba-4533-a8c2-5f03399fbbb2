package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.ManualAdjustment;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 手动调整应用服务
 * 负责手动调整表单数据的处理和管理
 */
@Slf4j
@Service
public class ManualAdjustmentAppService extends AbstractFormService<ManualAdjustment> {

    /**
     * 构造函数
     *
     * @param yiDaSession          宜搭会话
     * @param yiDaSessionV2        宜搭会话V2
     * @param yidaConfigProperties 宜搭配置属性
     */
    public ManualAdjustmentAppService(YiDaSession yiDaSession,
                                      YiDaSessionV2 yiDaSessionV2,
                                      YidaConfigProperties yidaConfigProperties) {
        super(ManualAdjustment.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

}
