package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分段备货模板配置表单实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-A785AF2E7BF04DF0AFF3E484435790ACIPM0", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class SegmentedStockingTemplateConfig extends YidaObject {

    /**
     * 菜品物料表
     */
    @FormField("tableField_mcmq8svt")
    private List<DishMaterialInfo> dishMaterialInfoList;

    /**
     * 表单配置
     */
    @FormField("tableField_mcmq8svi")
    private List<FormConfig> formConfigList;

    /**
     * 类型
     */
    @FormField("radioField_mcmq8svk")
    private String type;

    /**
     * 名称
     */
    @FormField("textField_mcmrz9x5")
    private String name;

    /**
     * 备注
     */
    @FormField("editorField_mco61ecs")
    private String remark;

    /**
     * 菜品物料表子实体
     */
    @Data
    @FormEntity("tableField_mcmq8svt")
    public static class DishMaterialInfo {

        /**
         * 单位编码
         */
        @FormField("textField_mctz6itu")
        private String unitCode;

        /**
         * 单位名称
         */
        @FormField("textField_mctz6itt")
        private String unitName;

        /**
         * 物料组编码
         */
        @FormField("textField_mcmq8svy")
        private String materialGroupCode;

        /**
         * 菜品编码
         */
        @FormField("textField_mcmq8svx")
        private String dishCode;


        /**
         * 比例
         */
        @FormField("numberField_mcsiukgb")
        private BigDecimal ratio;

        /**
         * 菜品-物料关联
         */
        @FormField("associationFormField_mcmq8svw")
        private List<YidaAssociation> dishMaterialAssociation;

        /**
         * 物料组
         */
        @FormField("textField_mcmq8svu")
        private String materialGroup;
    }

    /**
     * 表单配置子实体
     */
    @Data
    @FormEntity("tableField_mcmq8svi")
    public static class FormConfig {

        /**
         * 系数
         */
        @FormField("numberField_mcmq8svz")
        private BigDecimal coefficient;

        /**
         * 预测数据时间段
         */
        @FormField("cascadeDateField_mcmq8svr")
        private List<LocalDateTime> forecastDataTimePeriod;

        /**
         * 使用时限
         */
        @FormField("textField_mcmq8svl")
        private String usageTimeLimit;

        /**
         * 时间节点
         */
        @FormField("textField_mcmq8svq")
        private String timeNode;
    }
}
