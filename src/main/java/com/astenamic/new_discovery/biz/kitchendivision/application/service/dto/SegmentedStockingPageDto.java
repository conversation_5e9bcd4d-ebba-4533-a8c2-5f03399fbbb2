package com.astenamic.new_discovery.biz.kitchendivision.application.service.dto;

import com.astenamic.new_discovery.common.util.HtmlToPdfUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 分段备货页面展示DTO
 */
@Data
public class SegmentedStockingPageDto {

    /**
     * 页面标题
     */
    private String title;

    /**
     * 更新时间
     */
    private String targetDate;

    /**
     * 行表头（时间配置）
     */
    private List<TimeConfigDto> rowHeaders;

    /**
     * 列表头（菜品物料信息）- 按materialGroup分组
     * Key: materialGroup名称（表头列名）
     * Value: 该物料组下的所有DishMaterialHeader列表
     */
    private Map<String, List<DishMaterialHeaderDto>> columnHeaders;

    /**
     * 数据矩阵
     */
    private BigDecimal[][] dataMatrix;

    /**
     * 页面备注
     */
    private String remark;

    /**
     * 类型（控制表头显示）
     */
    private String type;

    /**
     * 构造方法
     */
    public SegmentedStockingPageDto(LocalDateTime targetDate) {
        this.targetDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(targetDate);
    }

    /**
     * 时间配置DTO（行表头）
     */
    @Data
    public static class TimeConfigDto {
        /**
         * 时间节点
         */
        private String timeNode;

        /**
         * 使用时限
         */
        private String usageTimeLimit;

        /**
         * 预测数据时间段
         */
        private List<LocalDateTime> forecastDataTimePeriod;

        /**
         * 餐段（上午、下午等）
         */
        private String mealPeriod;

        /**
         * 是否是餐段的第一行（用于rowspan计算）
         */
        private boolean isFirstRowOfPeriod;

        /**
         * 该餐段的总行数（用于rowspan）
         */
        private int periodRowSpan;

        /**
         * 系数
         */
        private BigDecimal coefficient;
    }

    /**
     * 菜品物料表头DTO（列表头）
     */
    @Data
    public static class DishMaterialHeaderDto {
        /**
         * 物料组编码
         */
        private String materialGroupCode;

        /**
         * 菜品编码
         */
        private String dishCode;

        /**
         * 比例
         */
        private BigDecimal ratio;

        /**
         * 物料组
         */
        private String materialGroup;

        /**
         * 单位编码
         */
        private String unitCode;

        /**
         * 单位名称
         */
        private String unitName;
    }

    /**
     * 生成HTML
     */
    public String toHtml() {
        if (rowHeaders == null || rowHeaders.isEmpty()) {
            return getHtmlTemplate()
                    .replace("{{TITLE}}", title != null ? title : "分段备货表")
                    .replace("{{UPDATE_TIME}}", targetDate)
                    .replace("{{TABLE_HEADERS}}", "")
                    .replace("{{TABLE_ROWS}}", "<tr><td colspan='100%' style='text-align: center; padding: 20px; color: #999;'>暂无数据</td></tr>")
                    .replace("{{REMARK}}", remark != null ? remark : "");
        }

        String html = getHtmlTemplate();
        html = html.replace("{{TITLE}}", title != null ? title : "分段备货表");
        html = html.replace("{{UPDATE_TIME}}", targetDate);
        html = html.replace("{{TABLE_HEADERS}}", buildTableHeaders());
        html = html.replace("{{TABLE_ROWS}}", buildTableRows());
        html = html.replace("{{REMARK}}", remark != null ? remark : "");
        return html;
    }

    /**
     * 根据type字段获取时间表头文字
     */
    private String getTimeHeaderText() {
        if (type == null) {
            return "备货完成时间点"; // 默认值
        }

        return switch (type) {
            case "开始时间" -> "备货开始时间点";
            case "完成时间" -> "备货完成时间点";
            default -> "备货完成时间点"; // 默认值
        };
    }

    /**
     * HTML模板
     */
    private String getHtmlTemplate() {
        return """
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head>
                        <meta charset="UTF-8">
                        <title>{{TITLE}}</title>
                        <style>
                            .main-container {
                                font-family: Arial, sans-serif;
                                max-width: 1200px;
                                margin: 0 auto;
                                padding: 20px;
                            }
                            .main-container h2 {
                                text-align: center;
                                margin-bottom: 10px;
                                color: #333;
                                font-size: 24px;
                            }
                            .update-time {
                                text-align: right;
                                margin-bottom: 20px;
                                color: #666;
                                font-size: 14px;
                            }
                            .main-table {
                                border-collapse: collapse;
                                width: 100%;
                                margin: 20px 0;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            }
                            .remark {
                                margin-top: 20px;
                                padding: 15px;
                                background-color: #f9f9f9;
                                border-left: 4px solid #ff6b6b;
                                border-radius: 4px;
                            }
                            .th-header {
                                background-color: #e6f3ff;
                                padding: 4px;
                                text-align: center;
                                font-weight: bold;
                                border: 1px solid #111;
                                font-size: 12px;
                            }
                            .td-cell {
                                padding: 5px 4px;
                                text-align: center;
                                border: 1px solid #111;
                                font-size: 12px;
                            }
                            .td-rowspan-cell {
                                background-color: #f8f9fa;
                                font-weight: bold;
                                vertical-align: middle;
                            }
                            .td-colspan-cell {
                                font-weight: bold;
                                color: #000;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="main-container">
                            <h2>{{TITLE}}</h2>
                            <p class="update-time">数据引用日期：{{UPDATE_TIME}}</p>
                            <table class="main-table">
                                {{TABLE_HEADERS}}
                                {{TABLE_ROWS}}
                            </table>
                            <div class="remark">
                                {{REMARK}}
                            </div>
                        </div>
                    </body>
                    </html>
                
                """;
    }

    /**
     * 构建表格标题行
     */
    private String buildTableHeaders() {
        StringBuilder headers = new StringBuilder();
        headers.append("<tr>");

        // 餐段列（空表头，因为餐段会跨行显示）
        headers.append(getHeaderCellTemplate().replace("{{HEADER_TEXT}}", ""));

        // 根据type字段决定显示的表头文字
        String timeHeaderText = getTimeHeaderText();
        headers.append(getHeaderCellTemplate().replace("{{HEADER_TEXT}}", timeHeaderText));
        headers.append(getHeaderCellTemplate().replace("{{HEADER_TEXT}}", "使用时限"));

        // 遍历materialGroup作为列表头
        if (columnHeaders != null) {
            for (String materialGroup : columnHeaders.keySet()) {
                headers.append(getHeaderCellTemplate().replace("{{HEADER_TEXT}}", materialGroup));
            }
        }
        headers.append("</tr>");
        return headers.toString();
    }

    /**
     * 表头单元格模板
     */
    private String getHeaderCellTemplate() {
        return "<th class='th-header'>{{HEADER_TEXT}}</th>";
    }

    /**
     * 构建表格数据行
     */
    private String buildTableRows() {
        StringBuilder rows = new StringBuilder();

        for (int i = 0; i < rowHeaders.size(); i++) {
            rows.append("<tr>");

            // 餐段列 - 只在该餐段的第一行显示，使用rowspan
            TimeConfigDto currentRow = rowHeaders.get(i);
            if (currentRow.isFirstRowOfPeriod()) {
                String mealPeriodCell = getMealPeriodCellTemplate(currentRow.getPeriodRowSpan())
                        .replace("{{CELL_VALUE}}", currentRow.getMealPeriod() != null ? currentRow.getMealPeriod() : "");
                rows.append(mealPeriodCell);
            }

            // 时间节点
            String timeNode = currentRow.getTimeNode();
            if (timeNode != null) {
                timeNode = timeNode.replace("\n", "<br>");
            }
            rows.append(getDataCellTemplate().replace("{{CELL_VALUE}}", timeNode != null ? timeNode : ""));

            // 判断是否是"现来现备"行
            boolean isOnDemandRow = isOnDemandPreparation(currentRow);

            if (isOnDemandRow) {
                // "现来现备"行：跨所有数据列显示
                int colSpan = (columnHeaders != null ? columnHeaders.size() : 0) + 1; // +1 for 使用时限列
                String onDemandCell = "<td colspan='" + colSpan + "'class='td-cell  td-colspan-cell'>现来现备</td>";
                rows.append(onDemandCell);
            } else {
                // 正常行：显示使用时限和数据列
                String usageTimeLimit = currentRow.getUsageTimeLimit() != null ? currentRow.getUsageTimeLimit() : "";
                rows.append(getDataCellTemplate().replace("{{CELL_VALUE}}", usageTimeLimit));

                // 数据列 - 按materialGroup顺序
                if (columnHeaders != null) {
                    int j = 0;
                    for (String materialGroup : columnHeaders.keySet()) {
                        BigDecimal value = dataMatrix != null && i < dataMatrix.length && j < dataMatrix[i].length
                                ? dataMatrix[i][j] : BigDecimal.ZERO;
                        rows.append(getDataCellTemplate().replace("{{CELL_VALUE}}", value.toString()));
                        j++;
                    }
                }
            }

            rows.append("</tr>");
        }

        return rows.toString();
    }

    /**
     * 数据单元格模板
     */
    private String getDataCellTemplate() {
        return "<td class='td-cell'>{{CELL_VALUE}}</td>";
    }

    /**
     * 餐段单元格模板（带rowspan）
     *
     * @param rowSpan 跨行数
     */
    private String getMealPeriodCellTemplate(int rowSpan) {
        return "<td rowspan='" + rowSpan + "'  class='td-cell td-rowspan-cell'>{{CELL_VALUE}}</td>";
    }

    /**
     * 判断是否是"现来现备"行
     *
     * @param timeConfig 时间配置
     * @return 是否是现来现备行
     */
    private boolean isOnDemandPreparation(TimeConfigDto timeConfig) {
        // 如果使用时限为空且预测数据时间段也为空，则是"现来现备"
        boolean usageTimeLimitEmpty = timeConfig.getUsageTimeLimit() == null || timeConfig.getUsageTimeLimit().trim().isEmpty();
        boolean forecastDataEmpty = timeConfig.getForecastDataTimePeriod() == null || timeConfig.getForecastDataTimePeriod().isEmpty();

        return usageTimeLimitEmpty && forecastDataEmpty;
    }

    /**
     * 生成PDF字节数组
     *
     * @return PDF字节数组
     * @throws IOException PDF生成异常
     */
    public byte[] toPdf() throws IOException {
        String html = toHtml();
        return HtmlToPdfUtil.convertHtmlToPdfWithCss(html, getPdfOptimizedCss());
    }

    /**
     * 获取PDF优化的CSS样式
     *
     * @return CSS样式字符串
     */
    private String getPdfOptimizedCss() {
        return """
                @page {
                    size: A4;
                    margin: 10mm 8mm;
                }
                
                body {
                    font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.4;
                    margin: 0;
                    padding: 0;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 8px 0;
                    page-break-inside: avoid;
                    table-layout: fixed;
                }
                
                /* 餐段列宽度设置 */
                th:first-child, td:first-child {
                    width: 8%;
                }
                
                th, td {
                    border: 1px solid #000;
                    padding: 5px 4px;
                    text-align: center;
                    vertical-align: middle;
                    font-size: 12px;
                    word-wrap: break-word;
                    font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;
                    color: #000;
                }
                
                th {
                    background-color: #e6f3ff;
                    font-weight: bold;
                    color: #000;
                    font-size: 12px;
                    padding: 4px 20px;
                }
                
                h2 {
                    text-align: center;
                    font-size: 20px;
                    margin: 10px 0;
                    font-weight: bold;
                    color: #000;
                    font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;
                }
                
                .remark {
                    margin-top: 15px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-left: 4px solid #ff6b6b;
                    font-size: 11px;
                    page-break-inside: avoid;
                }
                
                .update-time {
                    text-align: right;
                    font-size: 12px;
                    color: #333;
                    margin-bottom: 10px;
                    font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;
                }
                
                /* 餐段列样式 */
                td[rowspan] {
                    background-color: #f8f9fa;
                    font-weight: bold;
                }
                
                /* 现来现备行样式 */
                td[colspan] {
                    font-weight: bold;
                    color: #000;
                    font-style: italic;
                }
                """;
    }
}
