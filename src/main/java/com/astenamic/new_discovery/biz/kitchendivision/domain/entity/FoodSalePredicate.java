package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity(name = "yida_food_sale_predicate")
@FormEntity(value = "FORM-59BD829C8923439A91DD0AEA7E7FAEA0A5P3", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class FoodSalePredicate extends YidaObject {

    // 数据id
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    // 门店id
    @Column(name = "shop_id")
    @FormField("textField_m5rtqrms")
    private String shopId;

    // 门店
    @FormField("departmentSelectField_m4v7elus")
    @Transient
    private List<String> shopSysId;

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    // 品牌id
    @FormField("departmentSelectField_m4w3w3ku")
    @Transient
    private List<String> brandSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    // 菜品名称
    @FormField("textField_m4m1yv3o")
    @Column(name = "food_name")
    private String foodName;
    // 菜品编码
    @Column(name = "food_code")
    @FormField("textField_m5rw5fqe")
    private String foodCode;
    // 菜品单位
    @Column(name = "food_unit")
    @FormField("textField_m7wrgg5w")
    private String foodUnit;
    // 预测销量
    @FormField("numberField_m4m1yv3q")
    @Column(name = "pred_sale")
    private Float predSale;
    // 实际销量
    @FormField("numberField_m4m1yv3r")
    @Column(name = "actual_sale")
    private Float actualSale;
    // 人工调整
    @Column(name = "manual_adj")
    private Float manualAdj;
    // 时间（日）
    @FormField("dateField_m4m1yv3n")
    @Column(name = "date")
    private LocalDateTime date;
}
