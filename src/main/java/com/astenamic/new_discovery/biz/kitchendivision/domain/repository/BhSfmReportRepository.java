package com.astenamic.new_discovery.biz.kitchendivision.domain.repository;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.BhSfmReport;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.BhSfmReportId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 备货表物料备货预估量报表表Repository
 */
@Repository
public interface BhSfmReportRepository extends JpaRepository<BhSfmReport, BhSfmReportId> {

    /**
     * 根据条件查询预测销量总和
     * time_tag固定为'15分钟'，其他条件为变量
     *
     * @param dayFormat     日期
     * @param hourIntervals 时段区间列表
     * @param shopId        门店ID
     * @param foodCode      菜品code
     * @param materialNoHb  物料编码合并
     * @return 预测销量总和
     */
    @Query("SELECT COALESCE(SUM(r.qtyForecast), 0) FROM BhSfmReport r " +
            "WHERE r.timeTag = '15分钟' " +
            "AND r.dayFormat = :dayFormat " +
            "AND r.hourInterval IN :hourIntervals " +
            "AND r.shopId = :shopId " +
            "AND r.foodCode = :foodCode " +
            "AND r.materialNoHb = :materialNoHb " +
            "AND r.foodUnitId = :unitCode"
    )
    BigDecimal sumQtyForecastByConditions(@Param("dayFormat") String dayFormat,
                                          @Param("hourIntervals") List<String> hourIntervals,
                                          @Param("shopId") String shopId,
                                          @Param("foodCode") String foodCode,
                                          @Param("materialNoHb") String materialNoHb,
                                          @Param("unitCode") String unitCode);


    @Query(value = """
                SELECT qty_forecast
                FROM bh_sfm_report
                WHERE time_tag = '15分钟'
                  AND shop_id = :shopId
                  AND day_format = :dayFormat
                  AND food_code = :foodCode
                  AND hour_interval = :hourInterval
                LIMIT 1
            """, nativeQuery = true)
    BigDecimal findQtyForecast(@Param("shopId") String shopId,
                               @Param("dayFormat") String dayFormat,
                               @Param("foodCode") String foodCode,
                               @Param("hourInterval") String hourInterval);


}
