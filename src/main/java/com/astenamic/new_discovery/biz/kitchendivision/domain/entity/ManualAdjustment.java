package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 手动调整表单实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-80170FD777224B0993075672EFAEE648WM5A", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class ManualAdjustment extends YidaObject {

    /**
     * 菜品信息表格
     */
    @FormField("tableField_m4hufj8f")
    private List<DishInfo> dishInfoList;

    /**
     * 销售对比数据日期
     */
    @FormField("dateField_m56kjqpd")
    private LocalDateTime salesComparisonDate;

    /**
     * 预估数据日期
     */
    @FormField("dateField_m4vhkry1")
    private LocalDateTime estimatedDataDate;

    /**
     * 类型
     */
    @FormField("radioField_m56kjqpe")
    private String type;

    /**
     * 门店
     */
    @FormField("departmentSelectField_m4vd1plx")
    private List<String> storeSysId;

    public void setStoreSysId(String storeSysId) {
        if (storeSysId != null) {
            this.storeSysId = List.of(storeSysId);
        }
    }

    /**
     * 品牌
     */
    @FormField("departmentSelectField_m4w3540o")
    private List<String> brandSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    /**
     * 提交日期
     */
    @FormField("dateField_m4hufj87")
    private LocalDateTime submitDate;

    /**
     * 提交人
     */
    @FormField("employeeField_m4hufj88")
    private List<String> submitter;

    /**
     * 标题
     */
    @FormField("textField_m7srpb6u")
    private String title;

    /**
     * 菜品信息表格子实体
     */
    @Data
    @FormEntity("tableField_m4hufj8f")
    public static class DishInfo {

        /**
         * 是否调整
         */
        @FormField("selectField_m56kjqpf")
        private String adjustmentStatus;

        /**
         * 分工备货量
         */
        @FormField("numberField_m4hufj8k")
        private BigDecimal divisionStockQuantity;

        /**
         * 实际销量
         */
        @FormField("numberField_m4j6vx4y")
        private BigDecimal actualSales;

        /**
         * 系统预测量
         */
        @FormField("numberField_m4hufj8i")
        private BigDecimal systemForecast;

        /**
         * 菜品单位
         */
        @FormField("textField_m7x617r2")
        private String dishUnit;

        /**
         * 菜品名称
         */
        @FormField("textField_m4hufj8h")
        private String dishName;

        /**
         * 选择菜品
         */
        @FormField("associationFormField_m4hufj8g")
        private String selectedDish;
    }
}
