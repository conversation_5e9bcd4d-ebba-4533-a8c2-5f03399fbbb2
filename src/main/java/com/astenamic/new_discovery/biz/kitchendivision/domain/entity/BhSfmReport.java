package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 备货表物料备货预估量报表表
 */
@Data
@Entity
@Table(name = "bh_sfm_report", indexes = {
    @Index(name = "idx_bh_dayformat", columnList = "day_format"),
    @Index(name = "idx_bh_shop_id", columnList = "shop_id"),
    @Index(name = "idx_bh_time_tag", columnList = "time_tag")
})
@IdClass(BhSfmReportId.class)
public class BhSfmReport {

    /**
     * 时段标签
     */
    @Column(name = "time_tag", length = 50)
    private String timeTag;

    /**
     * 日期
     */
    @Id
    @Column(name = "day_format", nullable = false)
    private String dayFormat;

    /**
     * 周数
     */
    @Column(name = "df_week_num")
    private Integer dfWeekNum;

    /**
     * 时段区间
     */
    @Id
    @Column(name = "hour_interval", nullable = false, length = 50)
    private String hourInterval;

    /**
     * 门店id
     */
    @Id
    @Column(name = "shop_id", nullable = false, length = 50)
    private String shopId;

    /**
     * 菜品id
     */
    @Id
    @Column(name = "food_id", nullable = false)
    private String foodId;

    /**
     * 菜品编码
     */
    @Column(name = "food_code", length = 200)
    private String foodCode;

    /**
     * 菜品名称
     */
    @Column(name = "food_name", length = 200)
    private String foodName;

    /**
     * 菜品类别
     */
    @Column(name = "food_type", length = 200)
    private String foodType;

    /**
     * 菜品单位id
     */
    @Id
    @Column(name = "food_unit_id", nullable = false, length = 200)
    private String foodUnitId;

    /**
     * 菜品单位
     */
    @Column(name = "food_unit", length = 200)
    private String foodUnit;

    /**
     * 菜品做法id
     */
    @Id
    @Column(name = "food_cook_id", nullable = false)
    private String foodCookId;

    /**
     * 菜品做法
     */
    @Column(name = "food_cook_name")
    private String foodCookName;

    /**
     * 物料类型
     */
    @Column(name = "material_type")
    private String materialType;

    /**
     * 物料编码合并
     */
    @Id
    @Column(name = "material_no_hb", nullable = false)
    private String materialNoHb;

    /**
     * 物料名称合并
     */
    @Column(name = "material_name_hb")
    private String materialNameHb;

    /**
     * 物料大类统一
     */
    @Column(name = "material_type_big_ty")
    private String materialTypeBigTy;

    /**
     * 物料小类统一
     */
    @Column(name = "material_type_small_ty")
    private String materialTypeSmallTy;

    /**
     * 库存单位统一
     */
    @Column(name = "kc_unit_name_hb")
    private String kcUnitNameHb;

    /**
     * 原料数量均值
     */
    @Column(name = "yl_qty_avg", precision = 10, scale = 4)
    private BigDecimal ylQtyAvg = BigDecimal.ZERO;

    /**
     * 原料数量均值_库存单位
     */
    @Column(name = "yl_qty_avg_kcu", precision = 10, scale = 4)
    private BigDecimal ylQtyAvgKcu = BigDecimal.ZERO;

    /**
     * 浮动比例_最大值
     */
    @Column(name = "float_rate_max", precision = 10, scale = 4)
    private BigDecimal floatRateMax = BigDecimal.ZERO;

    /**
     * 预测销量
     */
    @Column(name = "qty_forecast", precision = 20, scale = 4)
    private BigDecimal qtyForecast = BigDecimal.ZERO;

    /**
     * 物料备货量
     */
    @Column(name = "material_stock_qty", precision = 20, scale = 4)
    private BigDecimal materialStockQty = BigDecimal.ZERO;

    /**
     * 销量均值_近4周次逻辑
     */
    @Column(name = "sales_qty_avg_logi1", precision = 20, scale = 4)
    private BigDecimal salesQtyAvgLogi1 = BigDecimal.ZERO;

    /**
     * 销量均值_高低位逻辑
     */
    @Column(name = "sales_qty_avg_logi2", precision = 20, scale = 4)
    private BigDecimal salesQtyAvgLogi2 = BigDecimal.ZERO;

    /**
     * 流水均值_近4周次逻辑
     */
    @Column(name = "liushui_avg_logi1", precision = 20, scale = 4)
    private BigDecimal liushuiAvgLogi1 = BigDecimal.ZERO;

    /**
     * 流水均值_高低位逻辑
     */
    @Column(name = "liushui_avg_logi2", precision = 20, scale = 4)
    private BigDecimal liushuiAvgLogi2 = BigDecimal.ZERO;

    /**
     * 实收均值_近4周次逻辑
     */
    @Column(name = "shishou_avg_logi1", precision = 20, scale = 4)
    private BigDecimal shishouAvgLogi1 = BigDecimal.ZERO;

    /**
     * 实收均值_高低位逻辑
     */
    @Column(name = "shishou_avg_logi2", precision = 20, scale = 4)
    private BigDecimal shishouAvgLogi2 = BigDecimal.ZERO;

    /**
     * 门店名称
     */
    @Column(name = "shop_name")
    private String shopName;

    /**
     * 品牌
     */
    @Column(name = "brand_name")
    private String brandName;

    /**
     * 区域
     */
    @Column(name = "region_name")
    private String regionName;

    /**
     * 门店编号
     */
    @Column(name = "shop_no")
    private String shopNo;

    /**
     * 存储环境
     */
    @Column(name = "environment")
    private String environment;
}
