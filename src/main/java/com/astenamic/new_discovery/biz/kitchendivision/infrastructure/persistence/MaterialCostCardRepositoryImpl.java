package com.astenamic.new_discovery.biz.kitchendivision.infrastructure.persistence;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.MaterialCostCard;
import com.astenamic.new_discovery.biz.kitchendivision.domain.repository.MaterialCostCardRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Tuple;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;


@Slf4j
@Repository
@RequiredArgsConstructor
public class MaterialCostCardRepositoryImpl implements MaterialCostCardRepository {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final EntityManager em;

    @Override
    public List<MaterialCostCard> findAllMaterialCostCard(LocalDateTime targetDate) {
        log.info("【成本卡查询】开始，targetDate = {}", targetDate);
        if (targetDate == null) {
            log.warn("targetDate 为空，返回空列表");
            return List.of();
        }
        // 将 LocalDateTime 转为日期字符串，匹配version_date字段格式
        String formatDate = targetDate.format(DATE_FORMATTER);

        String sql = """
                 SELECT DISTINCT
                  split_part( shop_name, '-', 1 ) AS brand_name,
                   food_name,
                  food_code,
                  unit_name,
                  t1.unit_id,
                  t3.material_type_big_ty,
                  t3.material_type_small_ty,
                  t1.material_name_hb,
                  t1.material_no_hb,
                  t1.cb_unit_name AS cb_unit_name_hb,
                  yl_qty AS ll_qty_ucb\s
                FROM
                  day_shop_food_material_report_temp AS t1
                  LEFT JOIN base_unit t2 ON t1.unit_id = t2.unit_id
                  LEFT JOIN material_group_report_temp t3 ON t1.material_id = t3.material_id\s
                WHERE
                  t1.version_date = :formatDate
                """;

        log.debug("执行 SQL >>>\n{}", sql);

        @SuppressWarnings("unchecked")
        List<Tuple> tuples = em.createNativeQuery(sql, Tuple.class)
                .setParameter("formatDate", formatDate)
                .getResultList();

        List<MaterialCostCard> result = tuples.stream()
                .map(t -> {
                    MaterialCostCard mc = new MaterialCostCard();
                    // 品牌名称
                    mc.setBrandName(t.get("brand_name", String.class));
                    // 菜品信息
                    mc.setFoodName(t.get("food_name", String.class));
                    mc.setFoodCode(t.get("food_code", String.class));
                    mc.setUnitName(t.get("unit_name", String.class));
                    mc.setFoodUnitId(t.get("unit_id", String.class));
                    // 物料信息
                    mc.setMaterialNameHb(t.get("material_name_hb", String.class));
                    mc.setMaterialGroupCode(t.get("material_no_hb", String.class));
                    mc.setMaterialTypeSmallTy(t.get("material_type_small_ty", String.class));
                    mc.setMaterialTypeBigTy(t.get("material_type_big_ty", String.class));
                    // 理论用量
                    mc.setLlQtyUcb(num(t, "ll_qty_ucb"));
                    // 成本单位名称
                    mc.setCbUnitNameHb(t.get("cb_unit_name_hb", String.class));
                    return mc;
                })
                .toList();

        log.info("【成本卡查询】结束，结果条数 = {}", result.size());
        return result;
    }

    private static float num(Tuple t, String alias) {
        Number n = t.get(alias, Number.class);
        return n != null ? n.floatValue() : 0f;   // 默认 0，可改成 Float.NaN / throw 等
    }
}
