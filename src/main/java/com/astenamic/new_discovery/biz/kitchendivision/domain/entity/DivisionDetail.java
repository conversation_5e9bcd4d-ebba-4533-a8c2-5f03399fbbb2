package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaAssociation;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 分工明细表单实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-E516CB80BA3C4809928FD239E83C52369M9L", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class DivisionDetail extends YidaObject {


    /**
     * 分工信息时段2表格
     */
    @FormField("tableField_m65xm4dc")
    private List<DivisionInfoPeriod2> divisionInfoPeriod2List;

    /**
     * 分工信息时段1表格
     */
    @FormField("tableField_m5w79ie7")
    private List<DivisionInfoPeriod1> divisionInfoPeriod1List;

    /**
     * 时段二总工时
     */
    @FormField("numberField_mc2yx4gv")
    private BigDecimal period2TotalWorkHours;

    /**
     * 时段1总工时
     */
    @FormField("numberField_mc2yx4gu")
    private BigDecimal period1TotalWorkHours;

    /**
     * 关联手动调整
     */
    @FormField("associationFormField_m7sti8o0")
    private List<YidaAssociation> relatedManualAdjustment;

    /**
     * 分组
     */
    @FormField("associationFormField_m7wnclof")
    private List<YidaAssociation> grouping;

    /**
     * 类型
     */
    @FormField("selectField_m97v0i21")
    private String type;

    /**
     * 厨师长
     */
    @FormField("employeeField_m5w79ie5")
    private List<String> headChefId;

    public void setHeadChefId(String headChefId) {
        if (headChefId != null) {
            this.headChefId = List.of(headChefId);
        }
    }

    /**
     * 门店
     */
    @FormField("departmentSelectField_m5w79ie4")
    private List<String> storeSysId;

    public void setStoreSysId(String storeSysId) {
        if (storeSysId != null) {
            this.storeSysId = List.of(storeSysId);
        }
    }

    /**
     * 日期
     */
    @FormField("dateField_m5w79ie3")
    private LocalDateTime date;

    /**
     * 分工信息时段2表格子实体
     */
    @Data
    @FormEntity("tableField_m65xm4dc")
    public static class DivisionInfoPeriod2 {

        /**
         * 检查
         */
        @FormField("textField_m8mq0cn5")
        private String inspection;

        /**
         * 备注
         */
        @FormField("textareaField_m8ch6jw4")
        private String remarks;

        /**
         * 工时
         */
        @FormField("numberField_m65xm4db")
        private BigDecimal workHours;

        /**
         * 单位
         */
        @FormField("selectField_m7wwq3c2")
        private String unit;

        /**
         * 备货量
         */
        @FormField("numberField_m65xm4d9")
        private BigDecimal stockQuantity;

        /**
         * 具体事项与要求
         */
        @FormField("textField_m65xm4d8")
        private String specificRequirements;

        /**
         * 备料及工作事项
         */
        @FormField("textField_m65xm4d7")
        private String preparationAndWorkItems;

        /**
         * 排序
         */
        @FormField("numberField_m65xm4d6")
        private BigDecimal sortOrder;

        /**
         * 时段
         */
        @FormField("textField_m65xm4d5")
        private String timePeriod;
    }

    /**
     * 分工信息时段1表格子实体
     */
    @Data
    @FormEntity("tableField_m5w79ie7")
    public static class DivisionInfoPeriod1 {

        /**
         * 检查
         */
        @FormField("textField_m8mq0cn4")
        private String inspection;

        /**
         * 备注
         */
        @FormField("textareaField_m8ch6jw3")
        private String remarks;

        /**
         * 工时
         */
        @FormField("numberField_m5w79iee")
        private BigDecimal workHours;

        /**
         * 单位
         */
        @FormField("selectField_m7wwq3c1")
        private String unit;

        /**
         * 备货量
         */
        @FormField("numberField_m5w79iec")
        private BigDecimal stockQuantity;

        /**
         * 具体事项与要求
         */
        @FormField("textField_m5w79ieb")
        private String specificRequirements;

        /**
         * 备料及工作事项
         */
        @FormField("textField_m5w79iea")
        private String preparationAndWorkItems;

        /**
         * 排序
         */
        @FormField("numberField_m5w79ie9")
        private BigDecimal sortOrder;

        /**
         * 时段
         */
        @FormField("textField_m5w79ie8")
        private String timePeriod;
    }
}
