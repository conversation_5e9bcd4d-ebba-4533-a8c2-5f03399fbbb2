package com.astenamic.new_discovery.biz.kitchendivision.domain.entity;


import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 成本卡表单实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-FCBD341E63A842A29BF22057854319BD5JV7", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class MaterialCostCard extends YidaObject {

    /**
     * 品牌
     */
    @FormField("departmentSelectField_m4w3ws38")
    private List<String> brandSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    @FormField("textField_mb078r9n")
    private String brandName;

    /**
     * 菜品名称
     */
    @FormField("textField_m4m1wc98")
    private String foodName;

    /**
     * 菜品编码
     */
    @FormField("textField_mb03nh5t")
    private String foodCode;

    /**
     * 菜品单位
     */
    @FormField("textField_m7wrg0xe")
    private String unitName;

    /**
     * 菜品单位编码
     */
    @FormField("textField_mb03nh5u")
    private String foodUnitId;

    /**
     * 物料名称
     */
    @FormField("textField_m4m1wc9b")
    private String materialNameHb;

    /**
     * 物料组编码
     */
    @FormField("textField_mb03nh5v")
    private String materialGroupCode;

    /**
     * 物料小类
     */
    @FormField("textField_m4m1wc9a")
    private String materialTypeSmallTy;

    /**
     * 物料大类
     */
    @FormField("textField_m4m1wc99")
    private String materialTypeBigTy;

    /**
     * searchFlag
     */
    @FormField("textField_m50xm5kl")
    private String searchFlag;

    /**
     * 理论单位用量
     */
    @FormField("numberField_m4m1wc9d")
    private Float llQtyUcb;

    /**
     * 成本单位
     */
    @FormField("textField_m4m1wc9c")
    private String cbUnitNameHb;
}
