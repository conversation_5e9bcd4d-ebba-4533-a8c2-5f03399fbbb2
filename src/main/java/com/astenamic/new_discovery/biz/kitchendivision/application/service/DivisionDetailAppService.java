package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.kitchendivision.application.service.dto.MaterialHourDto;
import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.DivisionDetail;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.entity.ProductPricingFlow;
import com.astenamic.new_discovery.biz.purchase.domain.pricing.valueobject.ProductConfirmationItem;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 分工明细应用服务
 * 负责分工明细表单数据的处理和管理
 */
@Slf4j
@Service
public class DivisionDetailAppService extends AbstractFormService<DivisionDetail> {

    /**
     * 构造函数
     *
     * @param yiDaSession          宜搭会话
     * @param yiDaSessionV2        宜搭会话V2
     * @param yidaConfigProperties 宜搭配置属性
     */
    public DivisionDetailAppService(YiDaSession yiDaSession,
                                    YiDaSessionV2 yiDaSessionV2,
                                    YidaConfigProperties yidaConfigProperties) {
        super(DivisionDetail.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    public List<DivisionDetail> getDivisionDetailByMinstId(String instId) {
        SearchConditions cond = SearchCondition.builder()
                .textEq(DivisionDetail.class, DivisionDetail::getRelatedManualAdjustment, instId, "+")
                .get();
        List<DivisionDetail> results = super.getFormsByCond(cond);
        results.forEach(form -> {
            List<DivisionDetail.DivisionInfoPeriod1> divisionInfoPeriod1s = queryInnerList(DivisionDetail.DivisionInfoPeriod1.class, form.getObjectId());
            form.setDivisionInfoPeriod1List(divisionInfoPeriod1s);
            List<DivisionDetail.DivisionInfoPeriod2> divisionInfoPeriod2s = queryInnerList(DivisionDetail.DivisionInfoPeriod2.class, form.getObjectId());
            form.setDivisionInfoPeriod2List(divisionInfoPeriod2s);
        });
        return results;
    }
    /**
     * 根据分工明细生成MaterialHourDto列表
     *
     * @param divisionDetail 分工明细
     * @return MaterialHourDto列表
     */
    public List<MaterialHourDto> generateMaterialHourDtoList(DivisionDetail divisionDetail) {
        List<MaterialHourDto> materialHourDtoList = new ArrayList<>();

        if (divisionDetail == null) {
            return materialHourDtoList;
        }

        // 处理时段1分工信息
        if (!CollectionUtils.isEmpty(divisionDetail.getDivisionInfoPeriod1List())) {
            for (DivisionDetail.DivisionInfoPeriod1 period1 : divisionDetail.getDivisionInfoPeriod1List()) {
                MaterialHourDto dto = convertPeriod1ToMaterialHourDto(period1);
                if (dto != null) {
                    materialHourDtoList.add(dto);
                }
            }
        }

        // 处理时段2分工信息
        if (!CollectionUtils.isEmpty(divisionDetail.getDivisionInfoPeriod2List())) {
            for (DivisionDetail.DivisionInfoPeriod2 period2 : divisionDetail.getDivisionInfoPeriod2List()) {
                MaterialHourDto dto = convertPeriod2ToMaterialHourDto(period2);
                if (dto != null) {
                    materialHourDtoList.add(dto);
                }
            }
        }

        return materialHourDtoList;
    }

    /**
     * 将时段1分工信息转换为MaterialHourDto
     */
    private MaterialHourDto convertPeriod1ToMaterialHourDto(DivisionDetail.DivisionInfoPeriod1 period1) {
        if (period1 == null) {
            return null;
        }

        // 构建原料名称：备料及工作事项-具体事项与要求
        String name = buildMaterialName(period1.getPreparationAndWorkItems(), period1.getSpecificRequirements());

        // 如果没有有效的原料名称，跳过
        if (!StringUtils.hasText(name)) {
            return null;
        }

        MaterialHourDto dto = new MaterialHourDto();
        dto.setStartDate("9:00");  // 时段一对应 9:00
        dto.setEndDate("10:00");   // 时段一对应 10:00
        dto.setName(name);
        dto.setStockQuantity(convertBigDecimalToFloat(period1.getStockQuantity()));
        dto.setUnitName(period1.getUnit());

        return dto;
    }

    /**
     * 将时段2分工信息转换为MaterialHourDto
     */
    private MaterialHourDto convertPeriod2ToMaterialHourDto(DivisionDetail.DivisionInfoPeriod2 period2) {
        if (period2 == null) {
            return null;
        }

        // 构建原料名称：备料及工作事项-具体事项与要求
        String name = buildMaterialName(period2.getPreparationAndWorkItems(), period2.getSpecificRequirements());

        // 如果没有有效的原料名称，跳过
        if (!StringUtils.hasText(name)) {
            return null;
        }

        MaterialHourDto dto = new MaterialHourDto();
        dto.setStartDate("16:00"); // 时段二对应 16:00
        dto.setEndDate("17:00");   // 时段二对应 17:00
        dto.setName(name);
        dto.setStockQuantity(convertBigDecimalToFloat(period2.getStockQuantity()));
        dto.setUnitName(period2.getUnit());

        return dto;
    }

    /**
     * 构建原料名称：备料及工作事项-具体事项与要求
     */
    private String buildMaterialName(String preparationAndWorkItems, String specificRequirements) {
        StringBuilder nameBuilder = new StringBuilder();

        if (StringUtils.hasText(preparationAndWorkItems)) {
            nameBuilder.append(preparationAndWorkItems.trim());
        }

        if (StringUtils.hasText(specificRequirements)) {
            if (nameBuilder.length() > 0) {
                nameBuilder.append("-");
            }
            nameBuilder.append(specificRequirements.trim());
        }

        return nameBuilder.toString();
    }

    /**
     * 将BigDecimal转换为Float
     */
    private Float convertBigDecimalToFloat(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }
        return bigDecimal.floatValue();
    }

}
