package com.astenamic.new_discovery.biz.kitchendivision.application.service.dto;

import com.astenamic.new_discovery.common.util.HtmlToPdfUtil;
import lombok.Data;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外卖套餐分段备货表DTO
 */
@Data
public class TakeawayMealSegmentedStockingPageDto {

    /**
     * 页面标题
     */
    private String title = "外卖套餐分段备货表";

    /**
     * 更新日期
     */
    private String updateDate;

    /**
     * 列表头配置（时间段）
     */
    private List<TimeSlotHeaderDto> timeSlotHeaders;

    /**
     * 行表头配置（菜品）
     */
    private List<DishHeaderDto> dishHeaders;

    /**
     * 数据矩阵：Map<菜品编码, Map<时间段ID, 数量>>
     */
    private Map<String, Map<String, Integer>> dataMatrix;

    /**
     * 汇总数据
     */
    private List<SummaryPeriodDto> summaryPeriods;

    /**
     * 使用说明HTML
     */
    private String usageInstructionsHtml = """
            <p class="instruction">1.表格中为两个外卖高峰时段，套餐热菜每15分钟的预估量</p>
            <p class="instruction">2.品质要求较高产品，30分钟销售量大于5份方可提前备货，套餐需当餐使用完不得隔餐使用</p>
            <p class="instruction">3.表格中小菜时段备量为外卖高峰期每小时的备货量，可以根据指引提前准备好，来单时放入微波炉加热2分钟，一次不得超过3份</p>
            """;

    /**
     * 构造方法
     */
    public TakeawayMealSegmentedStockingPageDto() {
        this.updateDate = DateTimeFormatter.ofPattern("yyyy/M/d").format(LocalDateTime.now());
        initializeDefaultData();
    }

    public TakeawayMealSegmentedStockingPageDto(LocalDateTime targetDate) {
        this.updateDate = DateTimeFormatter.ofPattern("yyyy/M/d").format(targetDate);
        initializeDefaultData();
    }

    /**
     * 初始化默认数据
     */
    private void initializeDefaultData() {

        // 初始化列表头（时间段）
        this.timeSlotHeaders = new ArrayList<>();
        initializeDefaultTimeSlots();

        // 初始化行表头（菜品）
        this.dishHeaders = new ArrayList<>();

        // 初始化数据矩阵
        this.dataMatrix = new HashMap<>();

        // 初始化汇总数据
        this.summaryPeriods = new ArrayList<>();
    }

    /**
     * 时间段表头DTO（列表头）
     */
    @Data
    public static class TimeSlotHeaderDto {
        /**
         * 时间段唯一ID
         */
        private String timeSlotId;

        /**
         * 时间段文本（如：10:00-10:15）
         */
        private String timeSlotText;

        /**
         * 所属餐段（lunch/dinner）
         */
        private String mealPeriod;

        /**
         * 所属汇总期间组（period1/period2/period3）
         */
        private String summaryGroup;

        public TimeSlotHeaderDto(String timeSlotId, String timeSlotText, String mealPeriod, String summaryGroup) {
            this.timeSlotId = timeSlotId;
            this.timeSlotText = timeSlotText;
            this.mealPeriod = mealPeriod;
            this.summaryGroup = summaryGroup;
        }
    }

    /**
     * 菜品表头DTO（行表头）
     */
    @Data
    public static class DishHeaderDto {
        /**
         * 菜品编码（唯一标识）
         */
        private String dishCode;

        /**
         * 菜品名称
         */
        private String dishName;

        /**
         * 排序序号
         */
        private Integer sortOrder;

        private boolean isStatistics = true;

        public DishHeaderDto(String dishCode, String dishName) {
            this.dishCode = dishCode;
            this.dishName = dishName;
        }

        public DishHeaderDto(String dishCode, String dishName, boolean isStatistics, Integer sortOrder) {
            this.dishCode = dishCode;
            this.dishName = dishName;
            this.isStatistics = isStatistics;
            this.sortOrder = sortOrder;
        }
    }


    /**
     * 汇总期间DTO
     */
    @Data
    public static class SummaryPeriodDto {
        /**
         * 汇总期间名称（如：10:00-11:00用量）
         */
        private String periodName;

        /**
         * 所属餐段（lunch/dinner）
         */
        private String mealPeriod;

        /**
         * 汇总数量
         */
        private Integer totalQuantity;

        /**
         * 跨越的时间段数量（用于colspan）
         */
        private int spanCount;

        public SummaryPeriodDto(String periodName, String mealPeriod, Integer totalQuantity, int spanCount) {
            this.periodName = periodName;
            this.mealPeriod = mealPeriod;
            this.totalQuantity = totalQuantity;
            this.spanCount = spanCount;
        }
    }

    /**
     * 添加菜品表头
     */
    public void addDishHeader(String dishCode, String dishName, boolean isStatistics) {
        DishHeaderDto header = new DishHeaderDto(dishCode, dishName, isStatistics, dishHeaders.size());
        dishHeaders.add(header);
        // 初始化该菜品的数据行
        dataMatrix.put(dishCode, new HashMap<>());
    }


    /**
     * 添加菜品表头
     */
    public void addDishHeader(String dishCode, String dishName) {
        addDishHeader(dishCode, dishName, true);
    }

    /**
     * 设置数据矩阵中的值
     */
    public void setData(String dishCode, String timeSlotId, Integer quantity) {
        Map<String, Integer> dishData = dataMatrix.get(dishCode);
        if (dishData != null) {
            dishData.put(timeSlotId, quantity);
        }
    }

    /**
     * 获取数据矩阵中的值
     */
    public Integer getData(String dishCode, String timeSlotId) {
        Map<String, Integer> dishData = dataMatrix.get(dishCode);
        if (dishData != null) {
            return dishData.get(timeSlotId);
        }
        return null;
    }

    /**
     * 批量设置菜品在某个餐段的数据
     */
    public void setDishMealPeriodData(String dishCode, String mealPeriod, Integer... quantities) {
        List<TimeSlotHeaderDto> periodSlots = timeSlotHeaders.stream()
                .filter(slot -> mealPeriod.equals(slot.getMealPeriod()))
                .toList();

        for (int i = 0; i < periodSlots.size() && i < quantities.length; i++) {
            if (quantities[i] != null) {
                setData(dishCode, periodSlots.get(i).getTimeSlotId(), quantities[i]);
            }
        }
    }

    /**
     * 设置午市汇总
     */
    public void setLunchSummary(Integer period1Total, Integer period2Total, Integer period3Total) {
        // 清除现有午市汇总
        this.summaryPeriods.removeIf(s -> "lunch".equals(s.getMealPeriod()));

        // 添加新的午市汇总
        this.summaryPeriods.add(new SummaryPeriodDto("10:00~11:00用量", "lunch", period1Total, 4));
        this.summaryPeriods.add(new SummaryPeriodDto("11:00~12:00用量", "lunch", period2Total, 4));
        this.summaryPeriods.add(new SummaryPeriodDto("12:00~12:30用量", "lunch", period3Total, 2));
    }

    /**
     * 设置晚市汇总
     */
    public void setDinnerSummary(Integer period1Total, Integer period2Total, Integer period3Total) {
        // 清除现有晚市汇总
        this.summaryPeriods.removeIf(s -> "dinner".equals(s.getMealPeriod()));

        // 添加新的晚市汇总
        this.summaryPeriods.add(new SummaryPeriodDto("16:00~17:00用量", "dinner", period1Total, 4));
        this.summaryPeriods.add(new SummaryPeriodDto("17:00~18:00用量", "dinner", period2Total, 4));
        this.summaryPeriods.add(new SummaryPeriodDto("18:00~18:30用量", "dinner", period3Total, 2));
    }

    /**
     * 初始化默认时间段表头
     */
    public void initializeDefaultTimeSlots() {
        this.timeSlotHeaders.clear();

        // 午市时间段
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_01", "10:00~10:15", "lunch", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_02", "10:16~10:30", "lunch", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_03", "10:31~10:45", "lunch", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_04", "10:46~11:00", "lunch", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_05", "11:00~11:15", "lunch", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_06", "11:16~11:30", "lunch", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_07", "11:31~11:45", "lunch", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_08", "11:46~12:00", "lunch", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_09", "12:00~12:15", "lunch", "period3"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("lunch_10", "12:16~12:30", "lunch", "period3"));

        // 晚市时间段
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_01", "16:00~16:15", "dinner", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_02", "16:16~16:30", "dinner", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_03", "16:31~16:45", "dinner", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_04", "16:46~17:00", "dinner", "period1"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_05", "17:00~17:15", "dinner", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_06", "17:16~17:30", "dinner", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_07", "17:31~17:45", "dinner", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_08", "17:46~18:00", "dinner", "period2"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_09", "18:00~18:15", "dinner", "period3"));
        this.timeSlotHeaders.add(new TimeSlotHeaderDto("dinner_10", "18:16~18:30", "dinner", "period3"));
    }

    /**
     * 生成HTML
     */
    public String toHtml() {
        String html = getHtmlTemplate();
        html = html.replace("{{TITLE}}", title);
        html = html.replace("{{UPDATE_DATE}}", updateDate);
        html = html.replace("{{TABLE_CONTENT}}", buildTableContent());
        html = html.replace("{{INSTRUCTIONS}}", usageInstructionsHtml != null ? usageInstructionsHtml : "");
        return html;
    }

    /**
     * 生成PDF
     */
    public byte[] toPdf() throws IOException {
        String html = toHtml();
        return HtmlToPdfUtil.convertHtmlToPdfWithCss(html, getPdfOptimizedCss());
    }

    /**
     * 构建表格内容
     */
    private String buildTableContent() {
        StringBuilder content = new StringBuilder();

        // 添加列组定义
        content.append(buildColGroup());

        // 构建午市表格
        content.append(buildMealPeriodTable("lunch", "午市外卖高峰"));

        // 构建晚市表格
        content.append(buildMealPeriodTable("dinner", "晚市外卖高峰"));

        return content.toString();
    }

    /**
     * 构建列组定义
     */
    private String buildColGroup() {
        StringBuilder colGroup = new StringBuilder();
        colGroup.append("<colgroup>");
        colGroup.append("<col class=\"dish-name-col\">");  // 菜品名称列
        colGroup.append("<col class=\"unit-col\">");       // 单位列

        // 时间段列（20个）
        for (int i = 0; i < 20; i++) {
            colGroup.append("<col class=\"time-slot-col\">");
        }

        colGroup.append("</colgroup>");
        return colGroup.toString();
    }

    /**
     * 构建餐段表格
     */
    private String buildMealPeriodTable(String mealPeriod, String periodTitle) {
        StringBuilder table = new StringBuilder();

        // 获取该餐段的时间段表头
        List<TimeSlotHeaderDto> periodHeaders = timeSlotHeaders.stream()
                .filter(h -> mealPeriod.equals(h.getMealPeriod()))
                .toList();

        // 获取该餐段的汇总数据
        List<SummaryPeriodDto> periodSummaries = summaryPeriods.stream()
                .filter(s -> mealPeriod.equals(s.getMealPeriod()))
                .toList();

        // 餐段标题行
        table.append("<tr>");
        table.append("<th class=\"period-header\" colspan=\"").append(periodHeaders.size() + 2).append("\">")
                .append(periodTitle).append("</th>");
        table.append("</tr>");

        // 列表头行
        table.append("<tr>");
        table.append("<th class=\"header-cell\">菜品名称</th>");
        table.append("<th class=\"header-cell\">单位</th>");
        for (TimeSlotHeaderDto header : periodHeaders) {
            table.append("<th class=\"time-header\">").append(header.getTimeSlotText()).append("</th>");
        }
        table.append("</tr>");

        // 菜品数据行 - 使用数据矩阵
        for (DishHeaderDto dishHeader : dishHeaders) {
            table.append("<tr>");
            table.append("<td class=\"data-cell item-name\">").append(dishHeader.getDishName()).append("</td>");
            table.append("<td class=\"data-cell unit\">份</td>");

            // 根据列表头获取对应的数据
            for (TimeSlotHeaderDto timeSlot : periodHeaders) {
                Integer quantity = getData(dishHeader.getDishCode(), timeSlot.getTimeSlotId());
                String displayValue = (quantity != null && quantity > 0) ? quantity.toString() : "-";
                table.append("<td class=\"data-cell number\">").append(displayValue).append("</td>");
            }
            table.append("</tr>");
        }

        // 汇总期间表头行
        if (!periodSummaries.isEmpty()) {
            table.append("<tr>");
            table.append("<td class=\"summary-period\" colspan=\"2\"></td>");
            for (SummaryPeriodDto summary : periodSummaries) {
                table.append("<td class=\"summary-period\" colspan=\"").append(summary.getSpanCount()).append("\">")
                        .append(summary.getPeriodName()).append("</td>");
            }
            table.append("</tr>");

            // 汇总数据行
            table.append("<tr>");
            table.append("<td class=\"summary-cell\">套餐小菜</td>");
            table.append("<td class=\"summary-cell\">份</td>");
            for (SummaryPeriodDto summary : periodSummaries) {
                table.append("<td class=\"summary-cell\" colspan=\"").append(summary.getSpanCount()).append("\">")
                        .append(summary.getTotalQuantity() != null ? summary.getTotalQuantity() : 0).append("</td>");
            }
            table.append("</tr>");
        }

        return table.toString();
    }


    /**
     * HTML模板
     */
    private String getHtmlTemplate() {
        return """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
                    <title>{{TITLE}}</title>
                    <style>
                        body {
                            font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background-color: #f5f5f5;
                        }
                        .container {
                            max-width: 1200px;
                            margin: 0 auto;
                            background-color: white;
                            padding: 10px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 10px;
                        }
                        .title {
                            font-size: 24px;
                            font-weight: bold;
                            color: #333;
                            margin: 0;
                        }
                
                        .update-date {
                            font-size: 14px;
                            color: #666;
                            text-align: right;
                            margin-bottom: 4px;
                        }
                        .main-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 0 auto 5px auto;
                            background-color: white;
                            font-size: 12px;
                            table-layout: fixed;
                        }
                        .period-header {
                            background-color: #b3d9ff;
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-weight: bold;
                            font-size: 12px;
                            color: #333;
                            height: 20px;
                            vertical-align: middle;
                        }
                        .header-cell {
                            background-color: #e6f3ff;
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-weight: bold;
                            font-size: 12px;
                            color: #333;
                            height: 18px;
                            vertical-align: middle;
                        }
                        .time-header {
                            background-color: #e6f3ff;
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-weight: bold;
                            font-size: 12px;
                            color: #333;
                            writing-mode: horizontal-tb;
                            min-width: 25px;
                            height: 18px;
                            vertical-align: middle;
                        }
                        .summary-period {
                            background-color: #cce7ff;
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-size: 12px;
                            font-weight: bold;
                            height: 16px;
                            vertical-align: middle;
                        }
                        .summary-cell {
                            background-color: #f0f8ff;
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-size: 12px;
                            font-weight: bold;
                            color: #2c5aa0;
                            height: 18px;
                            vertical-align: middle;
                        }
                        .data-cell {
                            border: 1px solid #333;
                            padding: 2px;
                            text-align: center;
                            font-size: 12px;
                            background-color: white;
                            height: 18px;
                            vertical-align: middle;
                        }
                        .item-name {
                            text-align: center;
                            padding: 2px;
                            font-weight: 500;
                            min-width: 60px;
                            max-width: 60px;
                            font-size: 12px;
                            word-wrap: break-word;
                        }
                        .unit {
                            font-size: 12px;
                            color: #666;
                        }
                        .number {
                            font-weight: bold;
                        }
                        .instructions {
                            margin-top: 2px;
                            padding: 4px 10px;
                            background-color: #fff5f5;
                            border: 1px solid #ffcccc;
                            border-radius: 5px;
                        }
                        .instruction {
                            margin: 2px 0;
                            font-size: 12px;
                            color: #d32f2f;
                            line-height: 1.1;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1 class="title">{{TITLE}}</h1>
                        </div>
                        <div class="update-date">数据引用日期：{{UPDATE_DATE}}</div>
                
                        <table class="main-table">
                            {{TABLE_CONTENT}}
                        </table>
                
                        <div class="instructions">
                            {{INSTRUCTIONS}}
                        </div>
                    </div>
                </body>
                </html>
                """;
    }

    /**
     * PDF优化的CSS
     */
    private String getPdfOptimizedCss() {
        return """
                @page {
                    size: A4 landscape;
                    margin: 1mm 1mm;
                }
                * {
                    box-sizing: border-box;
                }
                
                html, body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: white !important;
                    font-size: 10px;
                    width: 100%;
                    height: 100%;
                }
                .container {
                    width: 100%;
                    max-width: 100%;
                    margin: 0 auto;
                    padding: 0;
                    background: white !important;
                    display: block;
                }
                .header {
                    text-align: center;
                    margin-bottom: 1px;
                    background: white !important;
                    margin-top: -4mm;
                }
                .title {
                    font-size: 12px;
                    font-weight: bold;
                    color: #333;
                    margin: 0 0 1px 0;
                    background: white !important;
                    line-height: 1.0;
                }
                .update-date {
                    font-size: 6px;
                    color: #666;
                    text-align: center;
                    margin-bottom: 1px;
                    background: white !important;
                    line-height: 1.0;
                }
                .main-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 auto 2px auto;
                    background: white !important;
                    font-size: 7px;
                    table-layout: fixed;
                }
                .main-table col.dish-name-col {
                    width: 18%;
                }
                .main-table col.unit-col {
                    width: 4%;
                }
                .main-table col.time-slot-col {
                    width: 3.9%;
                }
                .period-header {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 8px;
                    color: #333;
                    height: 8px;
                    vertical-align: middle;
                    line-height: 1.0;
                }
                .header-cell {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 7px;
                    color: #333;
                    height: 7px;
                    vertical-align: middle;
                    line-height: 1.0;
                }
                .time-header {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 6px;
                    color: #333;
                    height: 7px;
                    vertical-align: middle;
                    writing-mode: horizontal-tb;
                    line-height: 1.0;
                }
                .summary-period {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-size: 6px;
                    font-weight: bold;
                    height: 6px;
                    vertical-align: middle;
                    line-height: 1.0;
                }
                .summary-cell {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-size: 7px;
                    font-weight: bold;
                    color: #2c5aa0;
                    height: 7px;
                    vertical-align: middle;
                    line-height: 1.0;
                }
                .data-cell {
                    border: 1px solid #333;
                    padding: 0px;
                    text-align: center;
                    font-size: 7px;
                    background: white !important;
                    height: 7px;
                    vertical-align: middle;
                    line-height: 1.0;
                }
                .item-name {
                    text-align: center;
                    padding: 0.5px;
                    font-weight: 500;
                    font-size: 7px;
                    word-wrap: break-word;
                    background: white !important;
                }
                .unit {
                    font-size: 7px;
                    color: #666;
                    background: white !important;
                }
                .number {
                    font-weight: bold;
                    background: white !important;
                }
                .instructions {
                    margin-top: 0px;
                    padding: 0.5px;
                    background: #fff5f5 !important;
                    border: 1px solid #ffcccc;
                    border-radius: 0px;
                    text-align: left;
                }
                .instruction {
                    margin: 0;
                    font-size: 4px;
                    color: #d32f2f;
                    line-height: 0.8;
                    background: transparent !important;
                    display: inline;
                }
                """;
    }
}
