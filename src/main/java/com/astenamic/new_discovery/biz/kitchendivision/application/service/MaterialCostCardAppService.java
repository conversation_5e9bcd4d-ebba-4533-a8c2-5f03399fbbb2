package com.astenamic.new_discovery.biz.kitchendivision.application.service;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.MaterialCostCard;
import com.astenamic.new_discovery.biz.kitchendivision.domain.enums.BrandSysIdEnum;
import com.astenamic.new_discovery.biz.kitchendivision.domain.repository.MaterialCostCardRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 物料成本卡应用服务
 * 负责物料成本卡数据的同步和管理
 */
@Slf4j
@Service
public class MaterialCostCardAppService extends AbstractFormService<MaterialCostCard> {

    private final MaterialCostCardRepository materialCostCardRepository;
    private final RestTemplate restTemplate;

    /**
     * 构造函数
     *
     * @param yiDaSession                   宜搭会话
     * @param yiDaSessionV2                 宜搭会话V2
     * @param yidaConfigProperties          宜搭配置属性
     * @param materialCostCardRepository    物料成本卡仓储
     * @param restTemplate                  REST模板
     */
    @Autowired
    public MaterialCostCardAppService(YiDaSession yiDaSession,
                                      YiDaSessionV2 yiDaSessionV2,
                                      YidaConfigProperties yidaConfigProperties,
                                      MaterialCostCardRepository materialCostCardRepository,
                                      RestTemplate restTemplate) {
        super(MaterialCostCard.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
        this.materialCostCardRepository = materialCostCardRepository;
        this.restTemplate = restTemplate;
    }

    public void syncToYida(LocalDateTime targetDate) {
        log.info("同步成本卡到宜搭开始,时间: {}", targetDate);

        List<MaterialCostCard> allMaterialCostCard = materialCostCardRepository.findAllMaterialCostCard(targetDate);
        if (allMaterialCostCard.isEmpty()) {
            log.info("没有需要同步的成本卡数据");
            throw new RuntimeException("没有找到成本卡数据");
        }
        List<MaterialCostCard> oldData = super.getFormsByCond(SearchCondition.builder().get());
        log.info("所有成本卡数据: {}", oldData.size());
        while (!oldData.isEmpty()) {
            int size = Math.min(oldData.size(), 100);
            List<MaterialCostCard> delCost = oldData.subList(0, size);
            this.yiDaSession.batchDeleteByObjectId(delCost, MaterialCostCard.class);
            oldData.subList(0, size).clear();
        }
        refreshDingTalkCache();
        allMaterialCostCard.forEach(card ->
                Optional.ofNullable(BrandSysIdEnum.getByBrandName(card.getBrandName()))
                        .map(BrandSysIdEnum::getBrandSysId)
                        .flatMap(ids -> Arrays.stream(ids.split(","))
                                .findFirst())
                        .ifPresent(card::setBrandSysId)
        );
        log.info("所有成本卡数据: {}", allMaterialCostCard.size());

        /*
          3、保存成本卡
         */
        super.batchSave(allMaterialCostCard);
        refreshDingTalkCache();
    }

    /**
     * 刷新钉钉缓存
     */
    public void refreshDingTalkCache() {
        try {
            log.info("开始刷新钉钉缓存");
            String url = "https://xfx.clojve.cn/prod-api/kitchen/division/dingtalk/cache/refreshAllCache";
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            boolean success = response.getStatusCode() == HttpStatus.OK;
            log.info("刷新钉钉缓存{}，响应状态: {}", success ? "成功" : "失败", response.getStatusCode());
        } catch (Exception e) {
            log.error("刷新钉钉缓存异常", e);
        }
    }


}
