package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.Member;
import com.astenamic.new_discovery.biz.report.shop.entity.base.MemberRegisRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface MemberRegisRateRepository<T extends MemberRegisRate> extends JpaRepository<T, Long> {
    @Query(value = "SELECT a.base_shop_id AS shop_id\n" +
            "                  , ROW_NUMBER() OVER ()                                      AS id\n" +
            "                  , NULL                                                 AS object_id\n" +
            "                  -- 客流\n" +
            "                  , SUM(CASE\n" +
            "                            WHEN base_field10 = '新发现'\n" +
            "                                AND base_field13 LIKE '%上海%' THEN\n" +
            "                                round(pos_field16 / 80, 0)\n" +
            "                            WHEN base_field10 = '新发现'\n" +
            "                                AND base_field13 NOT LIKE '%上海%' THEN\n" +
            "                                round(pos_field16 / 75, 0)\n" +
            "                            WHEN base_field10 = '烤匠' THEN\n" +
            "                                round(pos_field16 / 90, 0)\n" +
            "                            WHEN base_field10 = '蝴蝶里' THEN\n" +
            "                                pos_field7\n" +
            "             END)                  AS psg_flow\n" +
            "                  -- 会员注册数\n" +
            "                  , SUM(b.mem_rsg) AS mem_rsg\n" +
            "             FROM wt_shop AS a\n" +
            "                      LEFT JOIN (SELECT base_date, shop_name, COUNT(1) AS mem_rsg\n" +
            "                                 FROM xfx_member\n" +
            "                                 GROUP BY shop_name, base_date) AS b ON b.shop_name = a.base_shop_name\n" +
            "                 AND a.base_date :: DATE = b.base_date\n" +
            "             WHERE a.base_date ~ :targetMonth\n" +
            "             GROUP BY a.base_shop_id, a.base_shop_name", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth") String targetMonth);
}
