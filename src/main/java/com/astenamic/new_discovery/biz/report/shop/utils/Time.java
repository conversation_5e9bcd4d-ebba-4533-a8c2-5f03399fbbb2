package com.astenamic.new_discovery.biz.report.shop.utils;

import org.springframework.data.util.Pair;
import lombok.Data;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data
public class Time {
    // 当前时间
    private String current;
    // 环妻
    private String last;
    // 同期
    private String samePeriodOfYear;
    // 月份
    private String month;
    // 月度或季度的一号
    private String firstDayOf;
    // 第几季度
    private String quarters;
    // 周
    private String week;

    private final static DateTimeFormatter YMFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
    private final static DateTimeFormatter YMFDFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final ZoneId BUSINESS_ZONE = ZoneId.of("Asia/Shanghai");

    private Time(String current, String last, String samePeriodOfYear, String month, String firstDayOf, String quarters) {
        this(current, last, samePeriodOfYear, month, firstDayOf, quarters, null);
    }

    private Time(String current, String last, String samePeriodOfYear, String month, String firstDayOf, String quarters, String week) {
        String regexPattern = "^(?)";

        current = regexPattern.replace("?", current);
        last = regexPattern.replace("?", last);
        samePeriodOfYear = regexPattern.replace("?", samePeriodOfYear);
        month = regexPattern.replace("?", month);
        quarters = regexPattern.replace("?", quarters);
        week = week == null ? "" : regexPattern.replace("?", week);

        this.current = current;
        this.last = last;
        this.samePeriodOfYear = samePeriodOfYear;
        this.month = month;
        this.firstDayOf = firstDayOf;
        this.quarters = quarters;
        this.week = week;
    }

    public static Time day(LocalDateTime target) {
        // 天
        String current = YMFDFormatter.format(target);
        String last = YMFDFormatter.format(target.minusDays(1));
        String samePeriodOfYear = YMFDFormatter.format(target.minusYears(1));
        String month = YMFormatter.format(target);
        int monthValue = target.getMonthValue();
        String quarters = (monthValue / 3 + 1) + "";

        return new Time(current, last, samePeriodOfYear, month, current, quarters);
    }

    public static Time month(LocalDateTime target) {

        String current = YMFormatter.format(target);
        String last = YMFormatter.format(target.minusMonths(1));
        String samePeriodOfYear = YMFormatter.format(target.minusYears(1));
        String month = YMFormatter.format(target);
        String flagDay = YMFDFormatter.format(target.withDayOfMonth(1));

        int monthValue = target.getMonthValue();
        String quarters = (monthValue / 3 + 1) + "";

        return new Time(current, last, samePeriodOfYear, month, flagDay, quarters);
    }

    public static Time quarter(LocalDateTime target) {

        List<String> targets = getQuarterFirstDays(target).stream().map(YMFormatter::format).toList();

        String current = String.join("|", targets);

        String last = getQuarterFirstDays(target.minusMonths(3)).stream().map(YMFormatter::format).collect(Collectors.joining("|"));

        String samePeriodOfYear = getQuarterFirstDays(target.minusYears(1)).stream().map(YMFormatter::format).collect(Collectors.joining("|"));

        String flagDay = targets.get(0) + "-01";

        int monthValue = target.getMonthValue();
        String quarters = ((monthValue - 1) / 3 + 1) + "";

        return new Time(current, last, samePeriodOfYear, current, flagDay, quarters);
    }

    public static Time week(LocalDateTime target) {
        ZonedDateTime zonedTarget = target.atZone(BUSINESS_ZONE).with(LocalTime.MIN);
        Pair<ZonedDateTime, List<ZonedDateTime>> currentWeek = getWeekDates(zonedTarget);
        // 1. 当前周计算
        String current = formatDates(currentWeek.getSecond());
        // 2. 环期（上周）
        String last = formatDates(getWeekDates(currentWeek.getFirst().minusWeeks(1)).getSecond());
        // 3. 同期（去年同一 ISO 周）
        String samePeriodOfYear = formatDates(getSamePeriodLastYear(zonedTarget).getSecond());
        // 4. 月份（基于当前周所在月份）
        String month = currentWeek.getSecond().stream()
                .map(date -> date.format(YMFormatter))
                .distinct()  // 去重
                .collect(Collectors.joining("|"));
        // 5. 周的第一天（周一）
        String firstDayOf = currentWeek.getFirst().format(YMFDFormatter);
        // 6. 季度计算（Q1-Q4）
        String quarter = (currentWeek.getFirst().getMonthValue() - 1) / 3 + 1 + "";
        // 8. ISO 周数
        String week = String.valueOf(zonedTarget.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
        return new Time(current, last, samePeriodOfYear, month, firstDayOf, quarter, week);
    }

    // 获取周的日期范围
    private static Pair<ZonedDateTime, List<ZonedDateTime>> getWeekDates(ZonedDateTime dateTime) {
        ZonedDateTime monday = dateTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        List<ZonedDateTime> weekDates = IntStream.range(0, 7)
                .mapToObj(monday::plusDays)
                .collect(Collectors.toList());
        return Pair.of(monday, weekDates);
    }

    // 计算去年同期周
    private static Pair<ZonedDateTime, List<ZonedDateTime>> getSamePeriodLastYear( ZonedDateTime baseDate) {
        int currentIsoWeek = baseDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        int currentIsoYear = baseDate.get(IsoFields.WEEK_BASED_YEAR);
        int targetIsoYear = currentIsoYear - 1;
        // 尝试构造去年同一周
        ZonedDateTime tentativeDate = baseDate
                .with(IsoFields.WEEK_BASED_YEAR, targetIsoYear)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, currentIsoWeek)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 验证周是否有效
        if (tentativeDate.get(IsoFields.WEEK_BASED_YEAR) != targetIsoYear) {
            // 超出范围，获取去年最后一周
            tentativeDate = getLastWeekOfYear(targetIsoYear);
        }
        return getWeekDates(tentativeDate);
    }

    // 获取指定 ISO 年的最后一周
    private static ZonedDateTime getLastWeekOfYear(int isoYear) {
        // 找到属于该 ISO 年的最后一个周四
        LocalDate probeDate = LocalDate.of(isoYear, 12, 31);
        while (probeDate.get(IsoFields.WEEK_BASED_YEAR) != isoYear) {
            probeDate = probeDate.minusDays(1);
        }
        // 获取该周周一
        return probeDate
                .atStartOfDay(BUSINESS_ZONE)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    }

    // 格式化日期列表
    private static String formatDates(List<ZonedDateTime> dates) {
        return dates.stream()
                .map(dt -> dt.format(YMFDFormatter))
                .collect(Collectors.joining("|"));
    }

    // 获取一个日期所在季度每个月的1号（以 LocalDateTime 表示）
    private static List<LocalDateTime> getQuarterFirstDays(LocalDateTime date) {
        int month = date.getMonthValue();
        int year = date.getYear();
        List<LocalDateTime> quarterFirstDays = new ArrayList<>();

        if (month <= 3) { // 第一季度
            quarterFirstDays.add(LocalDateTime.of(year, Month.JANUARY, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.FEBRUARY, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.MARCH, 1, 0, 0));
        } else if (month <= 6) { // 第二季度
            quarterFirstDays.add(LocalDateTime.of(year, Month.APRIL, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.MAY, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.JUNE, 1, 0, 0));
        } else if (month <= 9) { // 第三季度
            quarterFirstDays.add(LocalDateTime.of(year, Month.JULY, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.AUGUST, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.SEPTEMBER, 1, 0, 0));
        } else { // 第四季度
            quarterFirstDays.add(LocalDateTime.of(year, Month.OCTOBER, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.NOVEMBER, 1, 0, 0));
            quarterFirstDays.add(LocalDateTime.of(year, Month.DECEMBER, 1, 0, 0));
        }

        return quarterFirstDays;
    }
}
