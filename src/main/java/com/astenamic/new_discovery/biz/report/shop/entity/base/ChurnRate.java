package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

// 等位流失率
@Data
@MappedSuperclass
public abstract class ChurnRate extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 流失人数
    @Column(name = "ls_number")
    private Float lsNumber;

    //等位流失率
    @FormField("numberField_m3s5kpdk")
    private Float eqLossRate() {
        return MathUtil.divide(this.lsNumber, this.qhNumber);
    }

    // 取号人数
    @Column(name = "qh_number")
    private Float qhNumber;

}
