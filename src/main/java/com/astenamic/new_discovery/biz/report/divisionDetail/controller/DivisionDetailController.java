package com.astenamic.new_discovery.biz.report.divisionDetail.controller;

import com.astenamic.new_discovery.biz.report.divisionDetail.entity.DivisionDetail;
import com.astenamic.new_discovery.biz.report.divisionDetail.service.DivisionDetailService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/biz/report")
@AllArgsConstructor
public class DivisionDetailController {

    private final DivisionDetailService divisionDetailService;

    @GetMapping("/division/getData")
    public List<DivisionDetail> getData() {
        return this.divisionDetailService.getDivisionDetailByYiDa();
    }
}
