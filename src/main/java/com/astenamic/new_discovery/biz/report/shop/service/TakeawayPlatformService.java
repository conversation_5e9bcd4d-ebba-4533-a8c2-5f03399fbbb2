package com.astenamic.new_discovery.biz.report.shop.service;

import com.alibaba.fastjson2.JSON;
import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayTakeawayPlatform;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthTakeawayPlatform;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterTakeawayPlatform;
import com.astenamic.new_discovery.biz.report.shop.entity.week.WeekTakeawayPlatform;
import com.astenamic.new_discovery.biz.report.shop.repository.base.TakeawayPlatformRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class TakeawayPlatformService extends RangeDataService {
    private final TakeawayPlatformRepository<MonthTakeawayPlatform> monthRepository;
    private final TakeawayPlatformRepository<QuarterTakeawayPlatform> quarterRepository;
    private final TakeawayPlatformRepository<DayTakeawayPlatform> dayRepository;
    private final TakeawayPlatformRepository<WeekTakeawayPlatform> weekRepository;


    private static final Logger logger = LoggerFactory.getLogger(TakeawayPlatformService.class);

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthTakeawayPlatform.class,
                this.monthRepository.findByTime(time.getCurrent(), time.getLast())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterTakeawayPlatform.class,
                this.quarterRepository.findByTime(time.getCurrent(), time.getLast())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayTakeawayPlatform.class,
                this.dayRepository.findByTime(time.getCurrent(), time.getLast())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getWeekData(Time time) {
        logger.info("获取外卖经营指标周数据,{}", JSON.toJSONString(time));
        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = Pair.of(
                WeekTakeawayPlatform.class,
                this.weekRepository.findByTime(time.getCurrent(), time.getLast())
        );
        logger.info("获取外卖经营指标周数据,{}", pair.getSecond().size());
        return pair;
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayTakeawayPlatform.class;
    }
}
