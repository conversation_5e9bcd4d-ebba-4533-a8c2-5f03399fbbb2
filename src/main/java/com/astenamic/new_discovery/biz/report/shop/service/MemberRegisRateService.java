package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayMemberRegisRate;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthMemberRegisRate;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterMemberRegisRate;
import com.astenamic.new_discovery.biz.report.shop.repository.base.MemberRegisRateRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class MemberRegisRateService extends RangeDataService {
    private final MemberRegisRateRepository<MonthMemberRegisRate> monthRepository;
    private final MemberRegisRateRepository<QuarterMemberRegisRate> quarterRepository;
    private final MemberRegisRateRepository<DayMemberRegisRate> dayRepository;

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthMemberRegisRate.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterMemberRegisRate.class,
                this.quarterRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayMemberRegisRate.class,
                this.dayRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayMemberRegisRate.class;
    }
}
