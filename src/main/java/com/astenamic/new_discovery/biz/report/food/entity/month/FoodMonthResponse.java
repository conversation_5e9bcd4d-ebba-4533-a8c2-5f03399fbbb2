package com.astenamic.new_discovery.biz.report.food.entity.month;

import lombok.Data;

@Data
public class FoodMonthResponse {
    // 年月
    private String yearMonth;

    // 门店编码
    private String shopNo;

    // MD5
    private String md5;

    // 门店ID
    private String shopId;

    // 门店名称
    private String shopName;

    // 品牌
    private String brandName;

    // 区域
    private String regionName;

    // 菜品id
    private String foodId;

    // 菜品编码
    private String foodCode;

    // 菜品名称
    private String foodName;

    // 菜品类型
    private String foodStyleName;

    // 菜品做法ID
    private String foodCookId;

    // 菜品做法名称
    private String foodCookName;

    // 菜品做法ID
    private String foodUnitId;

    // 菜品做法名称
    private String foodUnitName;

    // 理论成本合计
    private Float llCost;

    // 实际成本合计
    private Float sjCost;

    // 理论成本率
    private Float llCostRate;

    // 实际成本率
    private Float sjCostRate;

    // 菜品流水
    private Float liushui;
}
