package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayChurnRate;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthChurnRate;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterChurnRate;
import com.astenamic.new_discovery.biz.report.shop.repository.base.ChurnRateRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ChurnRateService extends RangeDataService {
    private final ChurnRateRepository<MonthChurnRate> monthRepository;
    private final ChurnRateRepository<QuarterChurnRate> churnRateRepository;
    private final ChurnRateRepository<DayChurnRate> dayChurnRateRepository;
    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthChurnRate.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterChurnRate.class,
                this.churnRateRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayChurnRate.class,
                this.dayChurnRateRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayChurnRate.class;
    }
}
