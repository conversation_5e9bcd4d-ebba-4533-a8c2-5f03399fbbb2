package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.ReviewEl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface ReviewElRepository<T extends ReviewEl> extends JpaRepository<T, Long> {

    @Query(value = "SELECT b.shop_id\n" +
            "                        , ROW_NUMBER() OVER ()                                      AS id\n" +
            "     , NULL                                                 AS object_id\n" +
            "                        -- 饿了么昨天评价总数\n" +
            "                        , COUNT(1)                                                  AS total_review_el\n" +
            "                        -- 饿了么昨日差评数\n" +
            "                        , SUM(CASE WHEN a.总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_el\n" +
            "                   FROM xfx_comment_elm AS a\n" +
            "                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.elm_id\n" +
            "                        --替换昨日参数\n" +
            "                   WHERE a.评价时间 ~ :targetMonth\n" +
            "                   GROUP BY b.shop_id", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth") String targetMonth);
}
