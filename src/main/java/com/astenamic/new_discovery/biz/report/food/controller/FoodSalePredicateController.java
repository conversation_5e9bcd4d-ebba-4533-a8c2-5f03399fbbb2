package com.astenamic.new_discovery.biz.report.food.controller;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.FoodSalePredicate;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.FoodSalePredicateService;
import com.astenamic.new_discovery.biz.report.food.service.month.FoodMonthService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 菜品销售预测
 */
@RestController
@RequestMapping("/biz/report")
@AllArgsConstructor
public class FoodSalePredicateController {

    private final FoodSalePredicateService foodSalePredicateService;

    private final FoodMonthService foodMonthService;


    //    预测数据更新到宜搭
    @PostMapping("/food/cost/yida")
    public List<FoodSalePredicate> foodCost(@RequestBody Param param) {

        String dateStr = param.date;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.foodSalePredicateService.foodSaleToYiDa(target);

    }

    @PostMapping("/foodMonth")
    public void getFoodMonthData(@RequestBody Param param) {
        String dateStr = param.date;
        String shopNo = param.shopNo;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        foodMonthService.getFoodMonthData(target, shopNo);
    }

    @Data
    private static class Param {
        private String date;
        private String shopNo;
    }
}
