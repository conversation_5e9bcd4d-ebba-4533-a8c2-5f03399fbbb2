package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.Comments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.util.List;

@NoRepositoryBean
public interface CommentsRepository<T extends Comments> extends JpaRepository<T, Long> {
    @Query(value = "SELECT b.shop_id\n" +
            "                  , ROW_NUMBER() OVER ()                                      AS id\n" +
            "                  , NULL                                                 AS object_id\n" +
            "                 -- 点评店铺分\n" +
            "                 , AVG(a.美团星级)   AS biz_rating_dp\n" +
            "                 , SUM(a.新评价数)   AS total_review_dp\n" +
            "                 , SUM(a.新中差评数) AS neg_review_dp\n" +
            "            FROM xfx_dianping AS a\n" +
            "                     LEFT JOIN xfx_shop_map b ON a.美团门店ID = b.dp_id\n" +
            "            WHERE TO_CHAR(TO_DATE(a.日期, 'YYYYMMDD'), 'YYYY-MM-DD') ~ :targetMonth\n" +
            "            GROUP BY b.shop_id", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth")String targetMonth);
}
