package com.astenamic.new_discovery.biz.report.material.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.YidaObject;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

// 成本卡
@FormEntity(value = "FORM-FCBD341E63A842A29BF22057854319BD5JV7", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
@Data
@Entity(name = "yida_material_cost_report")
public class MaterialCostReport extends YidaObject {
    // 数据id
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "shop_id")
    private String shopId;
    // 品牌名称
    @Column(name = "brand_name")
    private String brandName;

    @FormField("departmentSelectField_m4w3ws38")
    @Transient
    private List<String> brandSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    // 菜品名称
    @FormField("textField_m4m1wc98")
    @Column(name = "food_name")
    private String foodName;
    // 物料大类
    @FormField("textField_m4m1wc99")
    @Column(name = "material_type_big_ty")
    private String materialTypeBigTy;
    // 物料小类
    @FormField("textField_m4m1wc9a")
    @Column(name = "material_type_small_ty")
    private String materialTypeSmallTy;
    // 物料名称
    @FormField("textField_m4m1wc9b")
    @Column(name = "material_name_hb")
    private String materialNameHb;
    // 成本单位
    @FormField("textField_m4m1wc9c")
    @Column(name = "cb_unit_name_hb")
    private String cbUnitNameHb;
    // 理论单位用量
    @FormField("numberField_m4m1wc9d")
    @Column(name = "ll_qty_ucb")
    private Float llQtyUcb;
    // 菜品单位
    @FormField("textField_m7wrg0xe")
    @Column(name = "unit_name")
    private String unitName;
}
