package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

@Data
@MappedSuperclass
public abstract class TakeawayPlatform extends RangeData{

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 店铺ID
    @Column(name = "shop_id")
    private String shopId;

    //本期曝光人数(饿了么)
    @Column(name = "exposure_people_num_el")
    @FormField("numberField_m3y4i3l3")
    private Float exposurePeopleNumEl;

    //本期曝光人数(美团)
    @Column(name = "exposure_people_num_mt")
    @FormField("numberField_m3o3hk3w")
    private Float exposurePeopleNumMt;

    //环期曝光人数(饿了么)
    @Column(name = "exposure_people_num_el_l")
    @FormField("numberField_m3y4i3l4")
    private Float exposurePeopleNumElL;

    //环期曝光人数(美团)
    @Column(name = "exposure_people_num_mt_l")
    @FormField("numberField_m3s5kpds")
    private Float exposurePeopleNumMtL;

    //曝光人数(美团)环比
    @FormField("numberField_m3o3hk3x")
    public Float exposurePeopleNumMtRate() {
        return MathUtil.increaseRate(this.exposurePeopleNumMt, this.exposurePeopleNumMtL);
    }

    //曝光人数(美团)环比
    @FormField("numberField_m3y4i3l5")
    public Float exposurePeopleNumElRate() {
        return MathUtil.increaseRate(this.exposurePeopleNumEl, this.exposurePeopleNumElL);
    }

    //进店转化率(饿了么)
    @Column(name = "shop_conversion_rate_el")
    @FormField("numberField_m3y4i3l6")
    private Float shopConversionRateEl;

    //进店转化率(美团)
    @Column(name = "shop_conversion_rate_mt")
    @FormField("numberField_m3o3hk3y")
    private Float shopConversionRateMt;

    //下单转化率(饿了么)
    @Column(name = "conversion_rate_order_el")
    @FormField("numberField_m3y4i3l8")
    private Float conversionRateOrderEl;

    //下单转化率(美团)
    @Column(name = "conversion_rate_order_mt")
    @FormField("numberField_m3o3hk3z")
    private Float conversionRateOrderMt;

    //下单新客(饿了么)
    @Column(name = "new_customers_order_num_el")
    @FormField("numberField_m3y4i3l7")
    private Float newCustomersOrderNumEl;

    //下单新客（美团）
    @Column(name = "new_customers_order_num_mt")
    @FormField("numberField_m3o3hk40")
    private Float newCustomersOrderNumMt;

    //下单人数(饿了么)
    @Column(name = "order_people_num_el")
    @FormField("numberField_m3y4i3l9")
    private Float orderPeopleNumEl;

    //下单人数(美团)
    @Column(name = "order_people_num_mt")
    @FormField("numberField_m3s5kpdu")
    private Float orderPeopleNumMt;

    //店铺星级评分(饿了么)
    @Column(name = "biz_rating_el")
    @FormField("numberField_m3y4i3lc")
    private Float bizRatingEl;

    //店铺星级评分(美团)
    @Column(name = "biz_rating_mt")
    @FormField("numberField_m3o3hk41")
    private Float bizRatingMt;

    //出餐上报率
    @Column(name = "meal_report_rate")
    @FormField("numberField_m3o3hk42")
    private String mealReportRate;

    //出餐超时率(饿了么)
    @Column(name = "merchant_overtime_rate_el")
    @FormField("numberField_m3o3hk44")
    private Float merchantOvertimeRateEL;

    //出餐超时率(美团)
    @Column(name = "merchant_overtime_rate_mt")
    @FormField("numberField_m3y4i3ld")
    private Float merchantOvertimeRateMt;

    //菜品下架清单
    @Column(name = "off_shelves")
    private Float offShelves;

    //外卖营业时长(饿了么)
    @Column(name = "basic_operating_hours_el")
    @FormField("numberField_m3y4i3lb")
    private Float basicOperatingHoursEl;

    //外卖营业时长(美团)
    @Column(name = "basic_operating_hours_mt")
    @FormField("numberField_m3sc02zq")
    private Float basicOperatingHoursMt;

    // 下单新客占比（美团）
    @FormField("numberField_m3s5kpdt")
    public Float newCusOrderNumMtRate() {
        return MathUtil.divide(this.newCustomersOrderNumMt, this.orderPeopleNumMt);
    }

    // 下单新客占比（饿了么）
    @FormField("numberField_m3y4i3la")
    public Float newCusOrderNumElRate() {
        return MathUtil.divide(this.newCustomersOrderNumEl, this.orderPeopleNumEl);
    }

}
