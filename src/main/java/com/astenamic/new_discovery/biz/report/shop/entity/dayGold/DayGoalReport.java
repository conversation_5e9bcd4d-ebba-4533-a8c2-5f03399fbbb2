package com.astenamic.new_discovery.biz.report.shop.entity.dayGold;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Transient;
import lombok.Data;

import java.util.List;

@FormEntity("FORM-E6987FF6AAB941048E22ED68498A6279XKT2")
@Data
public class DayGoalReport extends BaseEntity {

    // 门店ID
    @FormField("textField_m5nqp8ne")
    private String shopId;

    // 门店
    @FormField("departmentSelectField_m70dsvsg")
    @Transient
    private List<String> shopSysId;

    // 区域
    @FormField("departmentSelectField_m5mb0oe8")
    @Transient
    private List<String> areaSysId;

    // 督导
    @FormField("employeeField_m5mb0oe9")
    @Transient
    private List<String> supervisorSysId;

    // 战区id
    @FormField("departmentSelectField_m5mb0oea")
    @Transient
    private List<String> batAreaSysId;

    // 战区司令id
    @FormField("employeeField_m5mb0oeb")
    @Transient
    private List<String> batAreaPeopleSysId;

    // 年份
    @FormField("numberField_m5mb0oec")
    private String year;

    // 月份
    @FormField("numberField_m5mb0oed")
    private String month;

    // 月营收目标
    @FormField("numberField_m5mb0oev")
    private Float monthRevenue;

    // 月堂食目标
    @FormField("numberField_m5mb0of0")
    private Float monthDineInRevenue;

    // 月外卖目标
    @FormField("numberField_m5mb0of1")
    private Float monthTakeOutRevenue;

    // 日目标
    @FormField("tableField_m5mb0oeq")
    private List<DayGoal> dayGoals;

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

}
