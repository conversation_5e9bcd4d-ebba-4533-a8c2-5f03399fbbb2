package com.astenamic.new_discovery.biz.report.shop.entity.day;

import com.astenamic.new_discovery.biz.report.shop.entity.base.TakeawayPlatform;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import jakarta.persistence.Entity;
import lombok.Data;

@Data
@Entity(name = "yida_day_sa")
@FormEntity("FORM-1BB30970AA174858932487B079A59184IRTU")
public class DayTakeawayPlatform extends TakeawayPlatform {
}
