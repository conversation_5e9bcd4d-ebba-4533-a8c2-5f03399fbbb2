package com.astenamic.new_discovery.biz.report.shop.entity.day;

import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Data;

@Data
@Entity(name = "yida_day_row_number")
@FormEntity("FORM-1BB30970AA174858932487B079A59184IRTU")
public class DayRowNumber extends RowNumber {


    // 门店id
    @Column(name = "base_shop_id")
    @FormField("textField_m3o3hk0q")
    private String shopId;

//    @Override
//    @FormField("numberField_m3o3hk2n")
//    public Float budgetRevenueR(){
//        return MathUtil.divide(this.getAcuRevenue(), this.getBudgetRevenue());
//    }

    // 日营收目标
    @Transient
    @FormField("numberField_m6w1pvb7")
    private Float dayBudgetRevenue;

    // 营收目标达成率
    @FormField("numberField_m3o3hk2n")
    public Float dayBudgetRevenue(){
        if(dayBudgetRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getRevenue(), this.dayBudgetRevenue);
    }

//    @Override
//    @FormField("numberField_m3s5kpdl")
//    public Float deliveryTargetAchievementRate(){
//        return MathUtil.divide(this.getTakeOutAcuRevenue(), this.getTakeOutBudget());
//    }

    // 日堂食营收目标
    @Transient
    @FormField("numberField_m6w1pvb9")
    private Float dayBudgetDineInRevenue;

    @FormField("numberField_m3s5kpe4")
    private Float dayBudgetDineInRevenue(){
        if(dayBudgetDineInRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getDineInRec(), this.dayBudgetDineInRevenue);
    }

    //堂食目标达成率
//    @Override
//    @FormField("numberField_m3s5kpe4")
//    public Float dineInAchRate() {
//        return MathUtil.divide(this.getDineInAcuRevenue(), this.getBudgetDineInRevenue());
//    }


    // 日外卖营收目标
    @Transient
    @FormField("numberField_m6w1pvba")
    private Float dayBudgetTakeOutAcuRevenue;

    // 外卖目标达成率
    @FormField("numberField_m3s5kpdl")
    public Float dayBudgetTakeOutAcuRevenue(){
        if(dayBudgetTakeOutAcuRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getTakeOutRevenue(), this.dayBudgetTakeOutAcuRevenue);
    }


    // 单据初始营业额
    @Transient
    @FormField("numberField_m8fdcrmt")
    private Double initPredictAmount;

    //    预估营业额
    @Transient
    @FormField("numberField_m8fdcrmu")
    private Double predictAmount;

    //    预估偏差
    @Transient
    @FormField("numberField_m8fdcrmv")
    private Double predictDeviation;

    //智能叫货预警
    @Transient
    @FormField("textField_m8fdcrms")
    private String smartWarning;



}
