package com.astenamic.new_discovery.biz.report.shop.repository;

import com.astenamic.new_discovery.biz.report.shop.entity.ShopDailyReport;
import com.astenamic.new_discovery.biz.report.shop.entity.ShopDailyReportForQuarter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShopDailyReportForQuarterRepository extends JpaRepository<ShopDailyReportForQuarter,Long>, JpaSpecificationExecutor<ShopDailyReportForQuarter> {
    @Modifying
    void deleteAllByDate(LocalDateTime dateTime);

    List<ShopDailyReportForQuarter> findAllByDate(LocalDateTime dateTime);

}
