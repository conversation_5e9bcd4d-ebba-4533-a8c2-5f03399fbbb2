package com.astenamic.new_discovery.biz.report.divisionDetail.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m65xm4dk")
public class NightClean {
    // 时段
    @FormField("textField_m65xm4dh")
    private String timePart;

    // 工作
    @FormField("textField_m65xm4di")
    private String workContent;

    // 事项
    @FormField("textField_m65xm4dj")
    private String matter;
}
