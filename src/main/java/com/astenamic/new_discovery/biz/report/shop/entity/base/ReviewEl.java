package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

// 饿了么上月差评率
@Data
@MappedSuperclass
public abstract class ReviewEl extends RangeData{

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 饿了么昨天评价总数
    @Column(name = "total_review_el")
    @FormField("numberField_m50palir")
    private Float totalReviewEl;

    // 饿了么昨日差评数
    @Column(name = "neg_review_el")
    @FormField("numberField_m50paliq")
    private Float negReviewEl;

    //外卖差评率(饿了么)
    @FormField("numberField_m3ybvg3g")
    private Float negRateEl() {
        return MathUtil.divide(this.negReviewEl, this.totalReviewEl);
    }


}
