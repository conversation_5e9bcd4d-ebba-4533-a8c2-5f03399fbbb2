package com.astenamic.new_discovery.biz.report.shop.entity.base;


import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import jakarta.persistence.*;
import lombok.Data;

@Data
@MappedSuperclass
public abstract class SmartWarning extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 店铺ID
    @Column(name = "shop_id")
    private String shopId;

}
