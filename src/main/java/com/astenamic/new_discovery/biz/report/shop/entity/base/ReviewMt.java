package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

// 美团外卖上月差评率
@Data
@MappedSuperclass
public abstract class ReviewMt extends RangeData{

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 美团昨天评价总数
    @Column(name = "total_review_mt")
    @FormField("numberField_m50palip")
    private Float totalReviewMt;

    // 美团昨日差评数
    @Column(name = "neg_review_mt")
    @FormField("numberField_m50palio")
    private Float negReviewMt;

    //外卖差评率(美团)
    @FormField("numberField_m3xwif92")
    private Float negRateMt() {
        return MathUtil.divide(this.negReviewMt, this.totalReviewMt);
    }

}
