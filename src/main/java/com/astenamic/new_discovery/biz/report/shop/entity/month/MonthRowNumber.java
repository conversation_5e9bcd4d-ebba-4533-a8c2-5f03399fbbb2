package com.astenamic.new_discovery.biz.report.shop.entity.month;

import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;

@Data
@Entity(name = "yida_month_row_number")
@FormEntity("FORM-6D07F8FB24D941F8B52CBC56535A31D3Q70R")
public class MonthRowNumber extends RowNumber {


    // 门店id
    @Column(name = "base_shop_id")
    @FormField("textField_m3o3hk0q")
    private String shopId;

    // 营收目标达成率
    @FormField("numberField_m3o3hk2n")
    public Float budgetRevenueR(){
        return MathUtil.divide(this.getRevenue(), this.getBudgetRevenue());
    }

    //堂食目标达成率
    @FormField("numberField_m3s5kpe4")
    public Float dineInAchRate() {
        return MathUtil.divide(this.getDineInRec(), this.getBudgetDineInRevenue());
    }

    // 外卖目标达成率
    @FormField("numberField_m3s5kpdl")
    public Float deliveryTargetAchievementRate(){
        return MathUtil.divide(this.getTakeOutRevenue(), this.getTakeOutBudget());
    }

}
