package com.astenamic.new_discovery.biz.report.shop.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;

import java.time.YearMonth;

@Data
@Entity(name = "yida_shop_daily_report")
@FormEntity("FORM-1BB30970AA174858932487B079A59184IRTU")
public class ShopDailyReport extends ShopReport {

    @Column(name = "shop_no")
    private String shopNo;

    // 本期累计至当前日期的总营收
    @Column(name = "acu_revenue")
    private Float acuRevenue;

    // 日营收目标
    @FormField("numberField_m4qjgx1s")
    private Float dailyBudgetRevenue(){
        return divide(this.getBudgetRevenue(), this.getMonthDays()) / 100;
    };

    //总实收达成率
    @FormField("numberField_m3o3hk2n")
    public Float revenueAchRate() {
        return divide(this.acuRevenue, this.getBudgetRevenue());
    }

    // 日外卖目标
    @FormField("numberField_m4qsk0o0")
    private Float dailyBudgetTakeOutRevenue(){
        return divide(this.getTakeOutBudget(), this.getMonthDays()) / 100;
    };

    // 本期累计至当前日期的外卖营收
    @Column(name = "take_out_acu_revenue")
    private Float takeOutAcuRevenue;

    //外卖达成率
    @FormField("numberField_m3s5kpdl")
    public Float takeOutAchRate() {
        return divide(this.takeOutAcuRevenue, this.getTakeOutBudget());
    }

    // 日堂食目标
    @FormField("numberField_m4qsk0o5")
    private Float dailyBudgetDineInRevenue(){
        return divide(this.getBudgetDineInRevenue(), this.getMonthDays()) / 100;
    };

    // 本期累计至当前日期的堂食营收
    @Column(name = "dine_in_acu_revenue")
    private Float dineInAcuRevenue;

    //堂食达成率
    @FormField("numberField_m3s5kpe4")
    public Float dineInAchRate() {
        return divide(this.dineInAcuRevenue, this.getBudgetDineInRevenue());
    }

    private float getMonthDays(){
        return YearMonth.from(this.getDate()).lengthOfMonth();
    }



}
