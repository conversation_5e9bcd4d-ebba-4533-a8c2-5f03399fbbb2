package com.astenamic.new_discovery.biz.report.shop.entity.week;

import com.astenamic.new_discovery.biz.report.shop.entity.base.TakeawayPlatform;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import jakarta.persistence.Entity;
import lombok.Data;

@Data
@Entity(name = "yida_week_sa")
@FormEntity("FORM-74EF5E78FBDF46CB9FD5BBB1924B91E8MWB5")
public class WeekTakeawayPlatform extends TakeawayPlatform {
}
