package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.Member;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface MemberRepository<T extends Member> extends JpaRepository<T, Long> {

    @Query(value = "SELECT b.shop_id\n" +
            "                  , ROW_NUMBER() OVER ()                                      AS id\n" +
            "     , NULL                                                 AS object_id\n" +
            "                  -- 券拉动金额\n" +
            "                  , SUM(CAST(a.pull_amount AS FLOAT)) AS member_drive_funds\n" +
            "                FROM xfx_coupon_summer AS a\n" +
            "                         LEFT JOIN xfx_shop_map b ON a.shop_name = b.crm_name\n" +
            "                WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') ~ :targetMonth\n" +
            "                GROUP BY b.shop_id, a.shop_name", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth") String targetMonth);
}
