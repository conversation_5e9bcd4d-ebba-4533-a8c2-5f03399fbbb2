package com.astenamic.new_discovery.biz.report.pricing.controller;

import com.astenamic.new_discovery.biz.report.pricing.entity.data_base.YiDaBase;
import com.astenamic.new_discovery.biz.report.pricing.service.PricingService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/biz/pricing")
@AllArgsConstructor
public class PricingController {

    private final PricingService pricingService;

    @PostMapping("/price/syncToYiDa")
    public void priceSyncToYiDa(@RequestBody Map<String, Object> map){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String targetTime = map.get("targetTime").toString();
        String CprocessCode = map.get("CprocessCode").toString();
        String FprocessCode = map.get("FprocessCode").toString();
        LocalDateTime target = LocalDateTime.parse(targetTime, formatter);
        this.pricingService.syncToYiDa(target, CprocessCode, FprocessCode);
    }

    @PostMapping("/price/saveYiDaBase")
    public List<YiDaBase> saveYiDaBase(@RequestBody Map<String, Object> map){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String targetTime = map.get("targetTime").toString();
        LocalDateTime target = LocalDateTime.parse(targetTime, formatter);
        return this.pricingService.saveYiDaBase(target, 1, 100);
    }

    @PostMapping("/price/add")
    public String priceAdd(@RequestBody Map<String, Object> map){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String targetTime = map.get("targetTime").toString();
        LocalDateTime target = LocalDateTime.parse(targetTime, formatter);
        return this.pricingService.addSupplierPricingSheet(target);
    }
}
