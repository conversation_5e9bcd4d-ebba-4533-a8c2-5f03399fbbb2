package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayReviewEl;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthReviewEl;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterReviewEl;
import com.astenamic.new_discovery.biz.report.shop.repository.base.ReviewElRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ReviewElService extends RangeDataService {
    private final ReviewElRepository<MonthReviewEl> monthRepository;
    private final ReviewElRepository<QuarterReviewEl> quarterRepository;
    private final ReviewElRepository<DayReviewEl> dayRepository;

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthReviewEl.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterReviewEl.class,
                this.quarterRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayReviewEl.class,
                this.dayRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayReviewEl.class;
    }

}
