package com.astenamic.new_discovery.biz.report.pricing.repository;

import com.astenamic.new_discovery.biz.report.pricing.entity.data_base.YiDaBase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface YiDaBaseRepository extends JpaRepository<YiDaBase, Long> {

    @Query(value = "select * from yida_pricing where enddate  BETWEEN :start and :end", nativeQuery = true)
    List<YiDaBase> getTodayData(@Param("start") LocalDateTime start,
                                @Param("end") LocalDateTime end);
}
