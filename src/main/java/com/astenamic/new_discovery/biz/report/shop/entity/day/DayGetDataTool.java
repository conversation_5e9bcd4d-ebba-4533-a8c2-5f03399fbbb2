package com.astenamic.new_discovery.biz.report.shop.entity.day;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("FORM-1BB30970AA174858932487B079A59184IRTU")
public class DayGetDataTool extends YidaObject {

    @FormField("textField_m3o3hk0q")
    private String shopId;
}
