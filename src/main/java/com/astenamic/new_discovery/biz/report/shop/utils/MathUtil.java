package com.astenamic.new_discovery.biz.report.shop.utils;

public class MathUtil {

    public static Float increaseRate(Float c, Float l) {

        if (l == null || l == 0) {
            return 0f;
        }

        if (c == null || c == 0) {
            return 0f;
        }

        return ((c - l) / l) * 100;

    }

    public static Float divide(Float a, Float b) {

        if (b == null || b == 0) {
            return 0f;
        }

        if (a == null) {
            a = 0f;
        }

        return (a / b) * 100;

    }

    public static Float multiply(Float a, Float b) {

        if (a == null || a == 0) {
            return 0f;
        }

        if (b == null || b == 0) {
            return 0f;
        }

        return a * b;

    }
}
