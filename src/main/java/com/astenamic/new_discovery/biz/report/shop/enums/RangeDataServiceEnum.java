package com.astenamic.new_discovery.biz.report.shop.enums;

import lombok.Getter;

@Getter
public enum RangeDataServiceEnum {

    RANGE_DATA_SERVICE("RangeDataService", "报表"),
    CHURN_RATE_SERVICE("ChurnRateService", "流失率"),
    COMMENTS_SERVICE("CommentsService", "点评星级、点评中差评率"),
    MEAL_SERVICE("MealService", "菜品出餐速度"),
    MEMBER_SERVICE("MemberService", "券拉动金额"),
    MEMBER_REGIS_RATE_SERVICE("MemberRegisRateService", "会员注册率"),
    REVIEW_EL_SERVICE("ReviewElService", "饿了么上月差评率"),
    REVIEW_MT_SERVICE("ReviewMtService", "美团外卖上月差评率"),
    TAKEAWAY_PLATFORM_SERVICE("TakeawayPlatformService", "外卖平台指标"),
    ;

    private final String className;

    private final String desc;

    RangeDataServiceEnum(String className, String desc) {
        this.className = className;
        this.desc = desc;
    }

    public static RangeDataServiceEnum getByClassName(String className) {
        for (RangeDataServiceEnum value : RangeDataServiceEnum.values()) {
            if (value.getClassName().equals(className)) {
                return value;
            }
        }
        return RangeDataServiceEnum.RANGE_DATA_SERVICE;
    }

}
