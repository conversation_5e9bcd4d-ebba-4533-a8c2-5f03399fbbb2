package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

// 点评星级、点评中差评率
@Data
@MappedSuperclass
public abstract class Comments extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 美团星级
    @Column(name = "biz_rating_dp")
    @FormField("numberField_m3o3hk49")
    private Float bizRatingDp;

    // 新评价数
    @Column(name = "total_review_dp")
    @FormField("numberField_m3s5kpdy")
    private Float totalReviewDp;

    // 新中差评数
    @Column(name = "neg_review_dp")
    @FormField("numberField_m3o3hk48")
    private Float negReviewDp;

    @FormField("numberField_m3s5kpdx")
    private Float getNegRateDp() {
        return MathUtil.divide(negReviewDp, totalReviewDp);
    }
}
