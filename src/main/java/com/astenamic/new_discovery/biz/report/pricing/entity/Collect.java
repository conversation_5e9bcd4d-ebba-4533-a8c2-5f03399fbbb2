package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.ace.scm.supplier.entity.SaveSupplier;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity("tableField_m52hzd3h")
public class Collect extends BaseEntity {

    // 货品名称
    @FormField("textField_m52hzd3i")
    private String good_name;

    // 规格
    @FormField("textField_m52hzd3j")
    private String std;

    // 货品编码
    @FormField("textField_m5fcn8e1")
    private String good_sno;

    // 货品大类
    @FormField("textField_m52hzd3m")
    private String fgoodtype_name;

    // 货品小类
    @FormField("textField_m52hzd3l")
    private String goodtype_name;

    // 上月采购数量
    @FormField("numberField_m5f3ycgo")
    private String applyamount;

    // 供应商(关联)
    @FormField("associationFormField_m5ayvu59")
    private List<SaveSupplier> suppliers;

    // 供应商名称
//    @FormField("textField_m5c40s5d")
    @FormField("selectField_m5waz1kd")
    private String supplier_name;

    // 供应商编码
    @FormField("textField_m64iaaso")
    private String supplier_sno;

    // 价格执行门店名称(关联)
    @FormField("multiSelectField_m5avvix4")
    private List<String> shopNames;

    // 门店编码
    @FormField("textField_m5c52xov")
    private String shop_sno;

    // 价格执行门店ID列表
    @FormField("textField_m5c52xow")
    private String shop_id;

    // 价格结束日期
    @FormField("dateField_m52iq0hf")
    private LocalDateTime enddate;

    // 下次定价
    @FormField("numberField_m5upvi5n")
    private Float uprice;

    // 下月采购金额预估
    @FormField("numberField_m5fd1t6f")
    private Float estmoney;

    // 去年同期平均单价
    @FormField("numberField_m5fd1t6d")
    private String lastyprice;

    // 上期平均采购单价
    @FormField("numberField_m5fd1t6e")
    private String lastprice;

    // 当前执行价格
    @FormField("numberField_m5upvi5m")
    private Float nowprice;

    // 价格开始生效日期
    @FormField("dateField_m5upvi5l")
    private LocalDateTime startdate;

    // 环比调价比率
    @FormField("numberField_m5upvi5o")
    private String momratio;

    // 供应商ID
    @FormField("numberField_m3pde3sz")
    private String supplier_id;

    // 供应商属性， 0 普通供应商 1 门店自购供应商 2 总部代采供应商
    @FormField("textField_m5za6o6n")
    private String bselfpur;

    // 结算方式(1 货到付款 2 预付 7 周付 10 十天结算 14 半月付 28 月付 60 双月付 3 季度付 6 半年付 12 年付 29 压批结算)
    @FormField("textField_m5m3jlfm")
    private String accountdatetype;

    // 发票方式(1.增值税专用发票；2.农副产品收购发票；3.农产品增值税普通发票；4.增值税普通发票；5.无发票；)
    @FormField("textField_m5m3jlfn")
    private String invoicetype;

    // 联系人手机
    @FormField("textField_m5m3ntrg")
    private String phoneNumber;


    public void setSuppliers(List<SaveSupplier> suppliers) {
        if(suppliers != null) {
            this.suppliers = suppliers;
        }
    }

    public void setShopNames(List<String> shopNames) {
        if (shopNames != null) {
            this.shopNames = shopNames;
        }
    }

}
