package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.ChurnRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.util.List;

@NoRepositoryBean
public interface ChurnRateRepository<T extends ChurnRate> extends JpaRepository<T, Long> {

    @Query(value = "SELECT b.shop_id\n" +
            "                  , ROW_NUMBER() OVER ()                                      AS id\n" +
            "     , NULL                                                 AS object_id\n" +
            "                         -- 流失人数\n" +
            "                         , SUM(a.ls_number) AS ls_number\n" +
            "                         -- 取号人数\n" +
            "                         , SUM(a.qh_number) AS qh_number\n" +
            "                    FROM xfx_churn_rate AS a\n" +
            "                             LEFT JOIN xfx_shop_map AS b ON SUBSTRING(a.shop_name FROM '^(.*\\))') = b.meal_name\n" +
            "                    WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') ~ :targetMonth\n" +
            "                    GROUP BY b.shop_id", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth")String targetMonth);
}

