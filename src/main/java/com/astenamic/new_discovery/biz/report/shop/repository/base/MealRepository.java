package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.Meal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface MealRepository<T extends Meal> extends JpaRepository<T, Long> {
    @Query(value = "SELECT b.shop_id\n" +
            "                  , ROW_NUMBER() OVER ()                                      AS id\n" +
            "                  , NULL                                                 AS object_id\n" +
            "                   -- 菜品总出餐时长\n" +
            "                   , SUM(a.total_meal_time) AS total_meal_time\n" +
            "                   -- 出餐量\n" +
            "                   , SUM(a.meal_num)        AS meal_num\n" +
            "              FROM xfx_meal_speed AS a\n" +
            "                       LEFT JOIN xfx_shop_map AS b ON a.thirds_shop_id = b.pingzhi_id\n" +
            "              WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') ~ :targetMonth\n" +
            "              GROUP BY b.shop_id", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth")String targetMonth);

}
