package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayComments;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthComments;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterComments;
import com.astenamic.new_discovery.biz.report.shop.repository.base.CommentsRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class CommentsService extends RangeDataService {

    private final CommentsRepository<MonthComments> monthRepository;
    private final CommentsRepository<QuarterComments> quarterRepository;
    private final CommentsRepository<DayComments> commentsRepository;

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthComments.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterComments.class,
                this.quarterRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayComments.class,
                this.commentsRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayComments.class;
    }
}
