package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.TakeawayPlatform;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface TakeawayPlatformRepository<T extends TakeawayPlatform> extends JpaRepository<T, Long> {

    @Query(value = "WITH last AS (SELECT a.shop_id\n" +
            "                   , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.exposure_people_num END) AS exposure_people_num_el\n" +
            "                   , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.exposure_people_num END)   AS exposure_people_num_mt\n" +
            "              FROM wm_shop_analysis AS a\n" +
            "              WHERE a.snapshot_date ~ :lastMonth\n" +
            "              GROUP BY a.shop_id),\n" +
            "     current AS (SELECT a.shop_id\n" +
            "                      -- 月本期曝光人数\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.exposure_people_num END)     AS exposure_people_num_el\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.exposure_people_num END)       AS exposure_people_num_mt\n" +
            "                      -- 进店转化率\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.shop_conversion_rate END)*100    AS shop_conversion_rate_el\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.shop_conversion_rate END)*100      AS shop_conversion_rate_mt\n" +
            "                      -- 下单转化率\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.conversion_rate_order END)*100   AS conversion_rate_order_el\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.conversion_rate_order END)*100     AS conversion_rate_order_mt\n" +
            "                      -- 下单新客\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.new_customers_order_num END) AS new_customers_order_num_el\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.new_customers_order_num END)   AS new_customers_order_num_mt\n" +
            "                      -- 下单人数\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.order_people_num END)        AS order_people_num_el\n" +
            "                      , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.order_people_num END)          AS order_people_num_mt\n" +
            "                      -- 店铺星级评分\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.biz_rating END)              AS biz_rating_el\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.biz_rating END)                AS biz_rating_mt\n" +
            "                      -- 出餐上报率\n" +
            "                      , max(CASE WHEN a.channel_name LIKE '%美团%' THEN a.ontime_delivery_rate END)      AS meal_report_rate\n" +
            "                      -- 出餐超时率\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.merchant_overtime_rate END)*100  AS merchant_overtime_rate_el\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.merchant_overtime_rate END)*100    AS merchant_overtime_rate_mt\n" +
            "                      -- 外卖营业时长\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.basic_operating_hours END)   AS basic_operating_hours_el\n" +
            "                      , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.basic_operating_hours END)     AS basic_operating_hours_mt\n" +
            "                 FROM wm_shop_analysis AS a\n" +
            "                 WHERE a.snapshot_date ~ :targetMonth\n" +
            "                 GROUP BY a.shop_id)\n" +
            "\n" +
            "SELECT a.shop_id\n" +
            "     , ROW_NUMBER() OVER ()      AS id\n" +
            "     , NULL                      AS object_id\n" +
            "     -- 月本期曝光人数\n" +
            "     , a.exposure_people_num_el\n" +
            "     , a.exposure_people_num_mt\n" +
            "     -- 月环期曝光人数\n" +
            "     , la.exposure_people_num_el AS exposure_people_num_el_l\n" +
            "     , la.exposure_people_num_mt AS exposure_people_num_mt_l\n" +
            "     -- 进店转化率\n" +
            "     , a.shop_conversion_rate_el\n" +
            "     , a.shop_conversion_rate_mt\n" +
            "     -- 下单转化率\n" +
            "     , a.conversion_rate_order_el\n" +
            "     , a.conversion_rate_order_mt\n" +
            "     -- 下单新客\n" +
            "     , a.new_customers_order_num_el\n" +
            "     , a.new_customers_order_num_mt\n" +
            "     -- 下单人数\n" +
            "     , a.order_people_num_el\n" +
            "     , a.order_people_num_mt\n" +
            "     -- 店铺星级评分\n" +
            "     , a.biz_rating_el\n" +
            "     , a.biz_rating_mt\n" +
            "     -- 出餐上报率\n" +
            "     , a.meal_report_rate\n" +
            "     -- 出餐超时率\n" +
            "     , a.merchant_overtime_rate_el\n" +
            "     , a.merchant_overtime_rate_mt\n" +
            "     -- 菜品下架清单\n" +
            "     , NULL::NUMERIC             AS off_shelves\n" +
            "     -- 外卖营业时长\n" +
            "     , a.basic_operating_hours_el\n" +
            "     , a.basic_operating_hours_mt\n" +
            "FROM current AS a\n" +
            "         LEFT JOIN last AS la ON a.shop_id = la.shop_id\n", nativeQuery = true)
            List<T> findByTime(@Param("targetMonth")String targetMonth,
                                                              @Param("lastMonth") String lastMonth);
}