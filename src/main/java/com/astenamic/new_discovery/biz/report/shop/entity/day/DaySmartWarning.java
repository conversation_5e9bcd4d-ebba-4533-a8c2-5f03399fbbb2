package com.astenamic.new_discovery.biz.report.shop.entity.day;


import com.astenamic.new_discovery.biz.report.shop.entity.base.SmartWarning;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;

@Data
@Entity(name = "yida_day_smart")
@FormEntity("FORM-B4561FE865B04C2D8712D5F7E95CC95D9BNF")
public class DaySmartWarning extends SmartWarning {

    // 单据初始营业额
    @Column(name = "init_predict_amount")
    @FormField("numberField_m8fdcrmt")
    private Double initPredictAmount;

    //    预估营业额
    @Column(name = "predict_amount")
    @FormField("numberField_m8fdcrmu")
    private Double predictAmount;

    //    预估偏差
    @Column(name = "predict_deviation")
    @FormField("numberField_m8fdcrmv")
    private Double predictDeviation;

    //智能叫货预警
    @Column(name = "smart_warning")
    @FormField("textField_m8fdcrms")
    private String smartWarning;

}
