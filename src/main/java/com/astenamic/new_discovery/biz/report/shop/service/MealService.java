package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayMeal;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthMeal;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterMeal;
import com.astenamic.new_discovery.biz.report.shop.repository.base.MealRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class MealService extends RangeDataService {
    private final MealRepository<MonthMeal> monthRepository;
    private final MealRepository<QuarterMeal> mealRepository;
    private final MealRepository<DayMeal> dayMealRepository;

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthMeal.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterMeal.class,
                this.mealRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayMeal.class,
                this.mealRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayMeal.class;
    }
}
