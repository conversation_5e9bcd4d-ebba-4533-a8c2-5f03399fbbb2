package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.YidaObject;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity(value = "FORM-A26D7A4D6C164F7DBEF76BA80DB4360DHCWL", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class GetPricingFromYida extends YidaObject {

    // 定价类型(0:按供应商门店货品定价、1:按货品门店供应商定价、3:按供应商定价、4:按货品定价)
    @FormField("textField_m596at7w")
    private Integer type;

    // 供应商编号(0-不审核，1-审核)
    @FormField("numberField_m596at7r")
    private String supplier_sno;

    // 门店
//    @FormField("tableField_m5f1bzd1")
//    private List<AddPriceShop> shops;

    @FormField("multiSelectField_m60giawv")
    private List<String> shops;

    // 货品名称
    @FormField("textField_m596at7t")
    private String items;

    // 货品编码
    @FormField("textField_m60rhn3d")
    private String good_sno;

    // 货品规格
    @FormField("textField_m596at7u")
    private String std;

    // 定价
    @FormField("numberField_m596at7y")
    private Float uprice;

    // 价格生效日期
    @FormField("dateField_m596at80")
    private LocalDateTime startdate;

    // 价格生效结束时期
    @FormField("dateField_m596at81")
    private LocalDateTime enddate;

    // 更新时间
    private LocalDateTime utime;

    // 创建时间
    private LocalDateTime ctime;
}
