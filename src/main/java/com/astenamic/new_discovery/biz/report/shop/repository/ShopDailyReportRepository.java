package com.astenamic.new_discovery.biz.report.shop.repository;

import com.astenamic.new_discovery.biz.report.shop.entity.ShopDailyReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShopDailyReportRepository extends JpaRepository<ShopDailyReport,Long>, JpaSpecificationExecutor<ShopDailyReport> {

    @Modifying
    @Transactional
    void deleteAllByDate(LocalDateTime dateTime);

    List<ShopDailyReport> findAllByDate(LocalDateTime dateTime);


}
