package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

// 菜品出餐速度
@Data
@MappedSuperclass
public abstract class Meal extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 菜品总出餐时长
    @Column(name = "total_meal_time")
    private Float totalMealTime;

    // 出餐量
    @Column(name = "meal_num")
    private Float mealNum;

    //出餐速度
    @Column(name = "meal_spd")
    @FormField("numberField_m3o3hk3a")
    private Float mealSpd() {
        return MathUtil.divide(this.totalMealTime, this.mealNum) / 100;
    }


}
