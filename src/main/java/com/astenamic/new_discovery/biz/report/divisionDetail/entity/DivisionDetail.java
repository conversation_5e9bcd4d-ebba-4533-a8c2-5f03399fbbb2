package com.astenamic.new_discovery.biz.report.divisionDetail.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.biz.report.divisionDetail.entity.son.DivisionInfoTimeOne;
import com.astenamic.new_discovery.biz.report.divisionDetail.entity.son.DivisionInfoTimeTwo;
import com.astenamic.new_discovery.biz.report.divisionDetail.entity.son.LunchClean;
import com.astenamic.new_discovery.biz.report.divisionDetail.entity.son.NightClean;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@FormEntity(value = "FORM-77168353E80C47A4974E234E41803E37ZF6I", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class DivisionDetail extends BaseEntity {

    // 日期
    @FormField("dateField_m5w79ie3")
    private LocalDateTime date;

    // 门店
    @FormField("departmentSelectField_m5w79ie4")
    private List<String> shopSysid;

    // 厨师长
    @FormField("employeeField_m5w79ie5")
    private List<String> chiefSysId;

    // 分组
    @FormField("associationFormField_m5w79ie6")
    private String groupSysid;

    // 分组
    @FormField("textField_m5w7k6de")
    private String group;

    // 分工信息时段1
    @FormField("tableField_m5w79ie7")
    private List<DivisionInfoTimeOne> divisionInfoTimeOne;

    // 分工信息时段2
    @FormField("tableField_m65xm4dc")
    private List<DivisionInfoTimeTwo> divisionInfoTimeTwo;

    // 午市清洁
    @FormField("tableField_m65xm4dd")
    private List<LunchClean> lunchClean;

    // 晚市清洁
    @FormField("tableField_m65xm4dk")
    private List<NightClean> nightClean;

    public void setShopSysid(String shopSysid) {
        if(shopSysid != null) {
            this.shopSysid = List.of(shopSysid);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if(chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }

//    public void setGroupSysid(String groupSysid) {
//        if(groupSysid != null) {
//            this.groupSysid = List.of(groupSysid);
//        }
//    }


}
