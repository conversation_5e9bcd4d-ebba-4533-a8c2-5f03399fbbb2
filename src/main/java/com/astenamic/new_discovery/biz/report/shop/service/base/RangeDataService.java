package com.astenamic.new_discovery.biz.report.shop.service.base;

import com.alibaba.fastjson2.JSON;
import com.astenamic.new_discovery.biz.report.shop.annotation.RangeDataServiceLog;
import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.util.List;

public abstract class RangeDataService {

    private static final Logger logger = LoggerFactory.getLogger(RangeDataService.class);

    @Autowired
    protected YiDaSession yiDaSession;

    protected abstract Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time);

    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getWeekData(Time time) {
        return Pair.of(RangeData.class, List.of());
    }

    protected abstract Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time);

    protected abstract Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time);

    public abstract Class<? extends RangeData> getClaz();


    @RangeDataServiceLog("每日经营")
    public List<? extends RangeData> day(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.day(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getDayData(time);

        return this.daySave(mains, pair.getSecond(), pair.getFirst());
    }

    @RangeDataServiceLog("每周经营")
    public List<? extends RangeData> week(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.week(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getWeekData(time);

        return this.daySave(mains, pair.getSecond(), pair.getFirst());
    }

    @RangeDataServiceLog("每日经营更新")
    public void dayUpdate(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.day(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getDayData(time);

        this.update(mains, pair.getSecond(), pair.getFirst());
    }

    @RangeDataServiceLog("每周经营更新")
    public void weekUpdate(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.week(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getWeekData(time);

        this.update(mains, pair.getSecond(), pair.getFirst());
    }

    @RangeDataServiceLog("每月经营")
    public void month(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.month(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getMonthData(time);

        this.update(mains, pair.getSecond(), pair.getFirst());
    }

    @RangeDataServiceLog("每季经营")
    public void quarter(List<? extends RangeData> mains, LocalDateTime targetMonth) {
        Time time = Time.quarter(targetMonth);

        Pair<Class<? extends RangeData>, List<? extends RangeData>> pair = this.getQuarterData(time);

        this.update(mains, pair.getSecond(), pair.getFirst());
    }

    private void update(List<? extends RangeData> mains, List<? extends RangeData> data, Class<? extends RangeData> rangeDataClass) {
        data = data
                .stream()
                .filter(d -> {
                    for (RangeData main : mains) {
                        if (main.getShopId().equals(d.getShopId())) {
                            try {
                                logger.info("门店：{}，{}" +
                                                "更新数据: {},{}" +
                                                "历史数据：{}",
                                        d.getShopId(), System.lineSeparator(), JSON.toJSONString(d), System.lineSeparator(), JSON.toJSONString(main));
                            } catch (Exception e) {
                                logger.error("日志打印异常；");
                            }
                            d.setObjectId(main.getObjectId());
                            return true;
                        }
                    }
                    return false;
                })
                .toList();

        List<? extends List<? extends RangeData>> groups = ArrayUtils.splitCollection(data, 1);

        try {
            for (List<? extends RangeData> group : groups) {
                this.yiDaSession.batchUpdateDataByObjectId((List<RangeData>) group, (Class<RangeData>) rangeDataClass);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
            }
        } catch (Exception e) {
            logger.error("{}更新数据异常：", this.getClass().getSimpleName(), e);
        }
    }

    private List<? extends RangeData> daySave(List<? extends RangeData> mains, List<? extends RangeData> data, Class<? extends RangeData> rangeDataClass) {
        return data.stream()
                .filter(d -> {
                    for (RangeData main : mains) {
                        if (main.getShopId().equals(d.getShopId())) {
                            d.setObjectId(main.getObjectId());
                            return true;
                        }
                    }
                    return false;
                })
                .toList();
    }

}
