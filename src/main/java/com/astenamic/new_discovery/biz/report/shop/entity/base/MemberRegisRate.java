package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;

@Data
@MappedSuperclass
public abstract class MemberRegisRate extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 客流
    @Column(name = "psg_flow")
    private Float psgFlow;

    // 会员注册数
    @Column(name = "mem_rsg")
    @FormField("numberField_m3o3hk3b")
    private Float memRsg;

    //会员注册率
    @FormField("numberField_m3s5kpdj")
    private Float memberRegRate() {
        return MathUtil.divide(this.memRsg, this.psgFlow);
    }
}
