package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.YidaObject;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity(value = "FORM-88028C54935741CC89CF1A73C9B621D7LJAM", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class FreshForm extends YidaObject {

    // 日期
    @FormField("dateField_m53qednm")
    private LocalDateTime date;

    // 生鲜定价表单
    @FormField("tableField_m5bxvpci")
    private List<Fresh> freshs;
}
