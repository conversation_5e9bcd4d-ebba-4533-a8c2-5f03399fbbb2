package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.ReviewMt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface ReviewMtRepository<T extends ReviewMt> extends JpaRepository<T, Long> {

    @Query(value = "SELECT b.shop_id\n" +
            "                        , ROW_NUMBER() OVER () AS id\n" +
            "                        , NULL                                           AS object_id\n" +
            "                        -- 美团昨天评价总数\n" +
            "                        , COUNT(1)                                                AS total_review_mt\n" +
            "                        -- 美团昨日差评数\n" +
            "                        , SUM(CASE WHEN 总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_mt\n" +
            "                   FROM xfx_comment_mt AS a\n" +
            "                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.mt_id\n" +
            "                        -- 替换昨日参数\n" +
            "                   WHERE a.评价时间 ~ :targetMonth\n" +
            "                   GROUP BY b.shop_id", nativeQuery = true)
    List<T> findByTime(@Param("targetMonth") String targetMonth);

}
