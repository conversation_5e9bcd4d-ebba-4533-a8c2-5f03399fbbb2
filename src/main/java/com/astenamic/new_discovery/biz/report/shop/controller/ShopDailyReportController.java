package com.astenamic.new_discovery.biz.report.shop.controller;

import com.astenamic.new_discovery.biz.report.shop.service.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/biz/report")
@AllArgsConstructor
public class ShopDailyReportController {

    private ShopMonthReportService shopMonthReportService;

    @RequestMapping("/shop/sync/month/report/yida")
    public Object SaveMonthReport(@RequestBody Param param) {
        String dateStr = param.date;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.shopMonthReportService.month(target, 2);
    }

    @RequestMapping("/shop/sync/week/report/yida")
    public Object SaveWeekReport(@RequestBody Param param) {
        String dateStr = param.date;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.shopMonthReportService.month(target, 4);
    }

    @RequestMapping("/shop/sync/quarter/report/yida")
    public Object SaveQuarterReport(@RequestBody Param param) {
        String dateStr = param.date;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.shopMonthReportService.month(target, 3);
    }

    @RequestMapping("/shop/sync/day/report/yida")
    public Object SaveDayReport(@RequestBody Param param) {
        String dateStr = param.date;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.shopMonthReportService.month(target, 1);
    }

    // 获取该日期时间所在月份的所有天数
    public static List<LocalDateTime> getAllDaysInMonth(LocalDateTime dateTime) {
        List<LocalDateTime> days = new ArrayList<>();
        int year = dateTime.getYear();
        Month month = dateTime.getMonth();
        int daysInMonth = month.length(dateTime.toLocalDate().isLeapYear()); // 获取月份的天数

        for (int day = 1; day <= daysInMonth; day++) {
            days.add(LocalDateTime.of(year, month, day, 0, 0));
        }
        return days;
    }

    // 获取该日期时间所在季度的所有天数
    public static List<LocalDateTime> getAllDaysInQuarter(LocalDateTime dateTime) {
        List<LocalDateTime> days = new ArrayList<>();
        int year = dateTime.getYear();
        int quarter = (dateTime.getMonthValue() - 1) / 3 + 1; // 计算季度
        int startMonth = (quarter - 1) * 3 + 1; // 计算季度的起始月份

        for (int month = startMonth; month < startMonth + 3; month++) {
            Month currentMonth = Month.of(month);
            int daysInMonth = currentMonth.length(dateTime.toLocalDate().isLeapYear()); // 获取月份的天数
            for (int day = 1; day <= daysInMonth; day++) {
                days.add(LocalDateTime.of(year, month, day, 0, 0));
            }
        }
        return days;
    }

    public static List<LocalDateTime> getQuarterFirstDays(LocalDateTime dateTime) {
        int month = dateTime.getMonthValue();
        int year = dateTime.getYear();

        // 确定当前季度的第一个月
        int firstMonthOfQuarter = ((month - 1) / 3) * 3 + 1;

        List<LocalDateTime> firstDays = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LocalDateTime firstDay = LocalDateTime.of(year, firstMonthOfQuarter + i, 1, 0, 0);
            firstDays.add(firstDay);
        }
        return firstDays;
    }

    @Data
    private static class Param {
        private String date;
    }
}
