package com.astenamic.new_discovery.biz.report.shop.entity;

import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@MappedSuperclass
@Data
public abstract class ShopReport extends YidaObject {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 品牌id
    @FormField("departmentSelectField_m41d4qnv")
    @Transient
    private List<String> brandSysId;

    // 战区id
    @FormField("departmentSelectField_m419c2zv")
    @Transient
    private List<String> batAreaSysId;

    // 战区司令id
    @FormField("employeeField_m3xtfto9")
    @Transient
    private List<String> batAreaPeopleSysId;

    // 区域
    @FormField("departmentSelectField_m419c2zu")
    @Transient
    private List<String> areaSysId;

    // 督导
    @FormField("employeeField_m3xtfto6")
    @Transient
    private List<String> supervisorSysId;

    // 门店
    @FormField("departmentSelectField_m419c2zt")
    @Transient
    private List<String> shopSysId;

    // 门店经理id
    @FormField("employeeField_m3xtfto8")
    @Transient
    private List<String> shopManagerSysId;

    // 厨师长
    @FormField("employeeField_m42llkno")
    @Transient
    private List<String> chiefSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setShopManagerSysId(String shopManagerSysId) {
        if (shopManagerSysId != null) {
            this.shopManagerSysId = List.of(shopManagerSysId);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }
    // 群id
    @FormField("textField_m3zs7kq9")
    @Column(name = "group_id")
    private String groupId;

    // 门店id
    @Column(name = "base_shop_id")
    @FormField("textField_m3o3hk0q")
    private String baseShopId;

    // 时间
    @Column(name = "date")
    @FormField("dateField_m3weopav")
    private LocalDateTime date;

    //门店桌数
    @Column(name = "table_num")
    @FormField("numberField_m3o3hk39")
    private Float tableNum;

    //本期总实收
    @Column(name = "revenue")
    @FormField("numberField_m3s5kpbj")
    private Float revenue;

    //环期总实收
    @Column(name = "revenue_l")
    @FormField("numberField_m3s5kpbn")
    private Float revenueL;

    //同期总实收
    @Column(name = "revenue_p")
    @FormField("numberField_m3s5kpbz")
    private Float revenueP;

    // 本期总应收
    @Column(name = "ar")
    @FormField("numberField_m3zhztda")
    private Float ar;

    // 环期总应收
    @Column(name = "ar_l")
    @FormField("numberField_m3zhztdc")
    private Float arL;

    // 同期总应收
    @Column(name = "ar_p")
    @FormField("numberField_m3zhztdb")
    private Float arP;

    //营收目标额
    @Column(name = "budget_revenue")
    @FormField("numberField_m3s5kpci")
    private Float budgetRevenue;

    //退菜金额
    @Column(name = "retreat_money")
    @FormField("numberField_m3s5kpck")
    private Float retreatMoney;

    //折扣总金额
    @Column(name = "discount_amount")
    @FormField("numberField_m3s5kpcs")
    private Float discountAmount;

    //本期堂食应收
    @Column(name = "dine_in_revenue")
    @FormField("numberField_m3sc02zt")
    private Float dineInRevenue;

    //本期堂食实收（含自提）
    @Column(name = "dine_in_rec")
    @FormField("numberField_m3s5kpbt")
    private Float dineInRec;

    //环期堂食实收（含自提）
    @Column(name = "dine_in_rec_l")
    @FormField("numberField_m3s5kpbw")
    private Float dineInRecL;

    //同期堂食实收（含自提）
    @Column(name = "dine_in_rec_p")
    @FormField("numberField_m3s5kpbv")
    private Float dineInRecP;

    // 本期堂食应收（含自提）
    @Column(name = "dine_in_ar")
    @FormField("numberField_m3sc02zt")
    private Float dineInAr;

    // 环期堂食应收（含自提）
    @Column(name = "dine_in_ar_l")
    @FormField("numberField_m3zhztdi")
    private Float dineInArL;

    // 同期堂食应收（含自提）
    @Column(name = "dine_in_ar_p")
    @FormField("numberField_m3zhztdh")
    private Float dineInArP;

    // 堂食折扣金额
    @Column(name = "dine_in_discount_amount")
    @FormField("numberField_m3s5kpcs")
    private Float dineInDiscountAmount;

    // 堂食营收目标额
    @Column(name = "budget_dine_in_revenue")
    @FormField("numberField_m3o3hk33")
    private Float budgetDineInRevenue;

    //本期开桌数
    @Column(name = "tables_open")
    @FormField("numberField_m3o3hk35")
    private Float tablesOpen;

    //环期开桌数
    @Column(name = "tables_open_l")
    @FormField("numberField_m3s5kpcz")
    private Float tablesOpenL;

    //同期开桌数
    @Column(name = "tables_open_p")
    @FormField("numberField_m3s5kpcy")
    private Float tablesOpenP;

    //桌均本期
    @FormField("numberField_m3s9pzpn")
    public Float tablesAvg() {
        return divide(this.dineInRec, this.tablesOpen) / 100;
    }

    //桌均环期
    @FormField("numberField_m3s9pzpp")
    public Float tablesAvgL() {
        return divide(this.dineInRecL, this.tablesOpenL) / 100;
    }

    //桌均同期
    @FormField("numberField_m3s9pzpo")
    public Float tablesAvgP() {
        return divide(this.dineInRecP, this.tablesOpenP) / 100;
    }

    //桌均同比
    @FormField("numberField_m3s5kpd5")
    public Float avgTableCoy() {
        return increaseRate(this.tablesAvg(), this.tablesAvgP());
    }

    //桌均环比
    @FormField("numberField_m3s5kpdc")
    public Float avgTableCol() {
        return increaseRate(this.tablesAvg(), this.tablesAvgL());
    }

    //本期来客数
    @Column(name = "cus_num")
    @FormField("numberField_m3o3hk36")
    private Float cusNum;

    //环期来客数
    @Column(name = "cus_num_l")
    @FormField("numberField_m3s5kpd2")
    private Float cusNumL;

    //同期来客数
    @Column(name = "cus_num_p")
    @FormField("numberField_m3s5kpd1")
    private Float cusNumP;

    //本期堂食客流（含自提）
    @Column(name = "cus_flow")
    @FormField("numberField_m3o3hk38")
    private Float cusFlow;

    //环期堂食客流（含自提）
    @Column(name = "cus_flow_l")
    @FormField("numberField_m3s5kpd9")
    private Float cusFlowL;

    //同期堂食客流（含自提）
    @Column(name = "cus_flow_p")
    @FormField("numberField_m3s5kpd8")
    private Float cusFlowP;

    // 流失人数
    @Column(name = "ls_number")
    private Float lsNumber;

    // 取号人数
    @Column(name = "qh_number")
    private Float qhNumber;

    //等位流失率
    @FormField("numberField_m3s5kpdk")
    private Float eqLossRate() {
        return divide(this.lsNumber, this.qhNumber);
    }

    // 菜品总出餐时长
    @Column(name = "total_meal_time")
    private Float totalMealTime;

    // 出餐量
    @Column(name = "meal_num")
    private Float mealNum;

    //出餐速度
    @Column(name = "meal_spd")
    @FormField("numberField_m3o3hk3a")
    private Float mealSpd() {
        return divide(this.totalMealTime, this.mealNum) / 100;
    }

    //客流
    @Column(name = "psg_flow")
    private Float psgFlow;

    //注册会员数
    @Column(name = "mem_rsg")
    private Float memRsg;

    //会员注册率
    @FormField("numberField_m3s5kpdj")
    private Float memberRegRate() {
        return divide(this.memRsg, this.psgFlow);
    }

    //会员拉动资金
    @Column(name = "member_drive_funds")
    @FormField("numberField_m3o3hk3c")
    private Float memberDriveFunds;

    //本期外卖实收
    @Column(name = "take_out_revenue")
    @FormField("numberField_m3o3hk2l")
    private Float takeOutRevenue;

    //环期外卖实收
    @Column(name = "take_out_revenue_l")
    @FormField("numberField_m3s5kpcb")
    private Float takeOutRevenueL;

    //外卖实收环比
    @FormField("numberField_m3s5kpe2")
    private Float takeOutRevenueLRate() {
        return increaseRate(this.takeOutRevenue, this.takeOutRevenueL);
    }

    //同期外卖实收
    @Column(name = "take_out_revenue_p")
    @FormField("numberField_m3s5kpca")
    private Float takeOutRevenueP;

    //外卖实收同比
    @FormField("numberField_m3o3hk3i")
    private Float takeOutRevenueRRate() {
        return increaseRate(this.takeOutRevenue, this.takeOutRevenueP);
    }

    // 本期外卖应收
    @Column(name = "take_out_ar")
    @FormField("numberField_m3zhztdl")
    private Float takeOutAr;

    //  环期外卖应收
    @Column(name = "take_out_ar_l")
    @FormField("numberField_m3zhztdn")
    private Float takeOutArL;

    //  外卖应收环比
    @FormField("numberField_m3zhztdp")
    private Float takeOutArLRate() {
        return increaseRate(this.takeOutAr, this.takeOutArL);
    }

    //  外卖应收同比
    @Column(name = "take_out_ar_p")
    @FormField("numberField_m3zhztdm")
    private Float takeOutArP;

    //  外卖应收环比
    @FormField("numberField_m3zhztdo")
    private Float takeOutArPRate() {
        return increaseRate(this.takeOutAr, this.takeOutArP);
    }

    //  外卖折扣金额
    @Column(name = "take_out_discount_amount")
    @FormField("numberField_m3zhztd9")
    private Float takeOutDiscountAmount;

    //外卖实收目标额
    @Column(name = "take_out_budget")
    @FormField("numberField_m3o3hk3h")
    private Float takeOutBudget;

    //本期外卖订单数
    @Column(name = "take_out_num")
    @FormField("numberField_m3o3hk3j")
    private Float takeOutNum;

    //环期外卖订单数
    @Column(name = "take_out_num_l")
    @FormField("numberField_m3s5kpdp")
    private Float takeOutNumL;

    //外卖订单数环比
    @FormField("numberField_m3s5kpdq")
    public Float takeOutNumLRate() {
        return increaseRate(this.takeOutNum, this.takeOutNumL);
    }

    //同期外卖订单数
    @Column(name = "take_out_num_p")
    @FormField("numberField_m3s5kpdo")
    private Float takeOutNumP;

    //外卖订单数同比
    @FormField("numberField_m3s5kpdn")
    public Float takeOutNumPRate() {
        return increaseRate(this.takeOutNum, this.takeOutNumP);
    }

    //本期曝光人数(饿了么)
    @Column(name = "exposure_people_num_el")
    @FormField("numberField_m3y4i3l3")
    private Float exposurePeopleNumEl;

    //本期曝光人数(美团)
    @Column(name = "exposure_people_num_mt")
    @FormField("numberField_m3o3hk3w")
    private Float exposurePeopleNumMt;

    //环期曝光人数(饿了么)
    @Column(name = "exposure_people_num_el_l")
    @FormField("numberField_m3y4i3l4")
    private Float exposurePeopleNumElL;

    //环期曝光人数(美团)
    @Column(name = "exposure_people_num_mt_l")
    @FormField("numberField_m3s5kpds")
    private Float exposurePeopleNumMtL;

    //曝光人数(美团)环比
    @FormField("numberField_m3o3hk3x")
    public Float exposurePeopleNumMtRate() {
        return increaseRate(this.exposurePeopleNumMt, this.exposurePeopleNumMtL);
    }

    //曝光人数(美团)环比
    @FormField("numberField_m3y4i3l5")
    public Float exposurePeopleNumElRate() {
        return increaseRate(this.exposurePeopleNumEl, this.exposurePeopleNumElL);
    }

    //进店转化率(饿了么)
    @Column(name = "shop_conversion_rate_el")
    @FormField("numberField_m3y4i3l6")
    private Float shopConversionRateEl;

    //进店转化率(美团)
    @Column(name = "shop_conversion_rate_mt")
    @FormField("numberField_m3o3hk3y")
    private Float shopConversionRateMt;

    //下单转化率(饿了么)
    @Column(name = "conversion_rate_order_el")
    @FormField("numberField_m3y4i3l8")
    private Float conversionRateOrderEl;

    //下单转化率(美团)
    @Column(name = "conversion_rate_order_mt")
    @FormField("numberField_m3o3hk3z")
    private Float conversionRateOrderMt;

    //下单新客(饿了么)
    @Column(name = "new_customers_order_num_el")
    @FormField("numberField_m3y4i3l7")
    private Float newCustomersOrderNumEl;

    //下单新客（美团）
    @Column(name = "new_customers_order_num_mt")
    @FormField("numberField_m3o3hk40")
    private Float newCustomersOrderNumMt;

    //下单人数(饿了么)
    @Column(name = "order_people_num_el")
    @FormField("numberField_m3y4i3l9")
    private Float orderPeopleNumEl;

    //下单人数(美团)
    @Column(name = "order_people_num_mt")
    @FormField("numberField_m3s5kpdu")
    private Float orderPeopleNumMt;

    //店铺星级评分(饿了么)
    @Column(name = "biz_rating_el")
    @FormField("numberField_m3y4i3lc")
    private Float bizRatingEl;

    //店铺星级评分(美团)
    @Column(name = "biz_rating_mt")
    @FormField("numberField_m3o3hk41")
    private Float bizRatingMt;

    //出餐上报率
    @Column(name = "meal_report_rate")
    @FormField("numberField_m3o3hk42")
    private Float mealReportRate;

    // 饿了么昨天评价总数
    @Column(name = "total_review_el")
    private Float totalReviewEl;

    // 饿了么昨日差评数
    @Column(name = "neg_review_el")
    private Float negReviewEl;

    //外卖差评率(饿了么)
    @FormField("numberField_m3ybvg3g")
    private Float negRateEl() {
        return divide(this.negReviewEl, this.totalReviewEl);
    }

    // 美团昨天评价总数
    @Column(name = "total_review_mt")
    private Float totalReviewMt;

    // 美团昨日差评数
    @Column(name = "neg_review_mt")
    private Float negReviewMt;

    //外卖差评率(美团)
    @FormField("numberField_m3xwif92")
    private Float negRateMt() {
        return divide(this.negReviewMt, this.totalReviewMt);
    }

    //出餐超时率(饿了么)
    @Column(name = "merchant_overtime_rate_el")
    @FormField("numberField_m3o3hk44")
    private Float merchantOvertimeRateEL;

    //出餐超时率(美团)
    @Column(name = "merchant_overtime_rate_mt")
    @FormField("numberField_m3y4i3ld")
    private Float merchantOvertimeRateMt;

    //菜品下架清单
    @Column(name = "off_shelves")
    private Float offShelves;

    //外卖营业时长(饿了么)
    @Column(name = "basic_operating_hours_el")
    @FormField("numberField_m3y4i3lb")
    private Float basicOperatingHoursEl;

    //外卖营业时长(美团)
    @Column(name = "basic_operating_hours_mt")
    @FormField("numberField_m3sc02zq")
    private Float basicOperatingHoursMt;

    //点评分
    @Column(name = "biz_rating_dp")
    @FormField("numberField_m3o3hk49")
    private Float bizRatingDp;

    //点评差评率
    @Column(name = "neg_rate_dp")
    @FormField("numberField_m3s5kpdx")
    private Float negRateDp;

    //点评差评数
    @Column(name = "neg_review_dp")
    @FormField("numberField_m3o3hk48")
    private Float negReviewDp;

    //点评总评数
    @Column(name = "total_review_dp")
    @FormField("numberField_m3s5kpdy")
    private Float totalReviewDp;

    // 以下为计算字段

    // 总实收同比
    @FormField("numberField_m3s9pzpj")
    public Float revenueCoy() {
        return increaseRate(this.revenue, this.revenueP);
    }

    // 总实收环比
    @FormField("numberField_m3s9pzpl")
    public Float revenueCol() {
        return increaseRate(this.revenue, this.revenueL);
    }

    // 总应收同比
    @FormField("numberField_m3zhztde")
    public Float arCoy() {
        return increaseRate(this.ar, this.arP);
    }

    // 总应收环比
    @FormField("numberField_m3zhztdf")
    public Float arCol() {
        return increaseRate(this.ar, this.arL);
    }

    //退菜率
    @FormField("numberField_m3o3hk2q")
    public Float retreatRat() {
        return divide(this.retreatMoney, this.ar);
    }

    // 堂食折扣率
    @FormField("numberField_m3o3hk2p")
    public Float dineInDiscountRate() {
        return divide(this.dineInDiscountAmount, this.dineInAr);
    }

    // 外卖折扣率
    @FormField("numberField_m3zhztdg")
    public Float takeOutDiscountRate() {
        return divide(this.takeOutDiscountAmount, this.takeOutAr);
    }

    //堂食目标达成率
    @FormField("numberField_m3s5kpe4")
    public Float dineInAchRate() {
        return divide(this.dineInRevenue, this.budgetDineInRevenue);
    }

    //堂食实收同比
    @FormField("numberField_m3s5kpdz")
    public Float dineInCoy() {
        return increaseRate(this.dineInRec, this.dineInRecP);
    }

    //堂食实收环比
    @FormField("numberField_m3s5kpe0")
    public Float dineInCol() {
        return increaseRate(this.dineInRec, this.dineInRecL);
    }

    //堂食应收同比
    @FormField("numberField_m3zhztdj")
    public Float dineInArCoy() {
        return increaseRate(this.dineInAr, this.dineInArP);
    }

    //堂食应收环比
    @FormField("numberField_m3zhztdk")
    public Float dineInArCol() {
        return increaseRate(this.dineInAr, this.dineInArL);
    }

    // 翻台率
    @FormField("numberField_m3s5kpdd")
    public Float tableReopenRate() {
        return increaseRate(this.tablesOpen, this.tableNum);
    }

    // 翻台率同比
    @FormField("numberField_m3zs7kqb")
    public Float tableReopenRateCoy() {
        // 同期翻台率
        Float l = increaseRate(this.tablesOpenP, this.tableNum);

        return increaseRate(this.tableReopenRate(), l);
    }

    //开桌数同比
    @FormField("numberField_m3s5kpcx")
    public Float tablesOpenCoy() {
        return increaseRate(this.tablesOpen, this.tablesOpenP);
    }

    //开桌数环比
    @FormField("numberField_m3s5kpd3")
    public Float tablesOpenCol() {
        return increaseRate(this.tablesOpen, this.tablesOpenL);
    }

    //来客数同比
    @FormField("numberField_m3s5kpd0")
    public Float cusNumCoy() {
        return increaseRate(this.cusNum, this.cusNumP);
    }

    //来客数环比
    @FormField("numberField_m3s5kpd4")
    public Float cusNumCol() {
        return increaseRate(this.cusNum, this.cusNumL);
    }

    //客单价本期
    @FormField("numberField_m3s9pzpq")
    public Float aov() {
        return divide(this.dineInRec, this.cusFlow) / 100;
    }

    //客单价同期
    @FormField("numberField_m3s9pzpr")
    public Float aovP() {
        return divide(this.dineInRecP, this.cusFlowP) / 100;
    }

    //客单价环期
    @FormField("numberField_m3s9pzps")
    public Float aovL() {
        return divide(this.dineInRecL, this.cusFlowL) / 100;
    }

    //客单价同比
    @FormField("numberField_m3s5kpda")
    public Float aovCoy() {
        return increaseRate(this.aov(), this.aovP());
    }

    //客单价环比
    @FormField("numberField_m3s5kpdb")
    public Float aovCol() {
        return increaseRate(this.aov(), this.aovL());
    }

    //外卖收入占比(实收)
    @FormField("numberField_m3o3hk3k")
    public Float takeOutRePerRate() {
        return divide(this.takeOutRevenue, this.revenue);
    }

    //外卖收入占比(应收)
    @FormField("numberField_m3zs7kqc")
    public Float takeOutAePerRate() {
        return divide(this.takeOutAr, this.ar);
    }

    // 外卖单均本期
    @FormField("numberField_m3s5kpdr")
    public Float takeOutPerRe() {
        return divide(this.takeOutRevenue, this.takeOutNum) / 100;
    }

    // 外卖单均同期
    @FormField("numberField_m410cbe6")
    public Float takeOutPerReP() {
        return divide(this.takeOutRevenueP, this.takeOutNumP) / 100;
    }

    // 外卖单均环期
    @FormField("numberField_m410cbe4")
    public Float takeOutPerReL() {
        return divide(this.takeOutRevenueL, this.takeOutNumL) / 100;
    }

    // 外卖单均环比
    @FormField("numberField_m410cbe5")
    public Float takeOutPerReCol() {
        return increaseRate(this.takeOutPerRe(), this.takeOutPerReL());
    }

    // 外卖单均同比
    @FormField("numberField_m3o3hk3l")
    public Float takeOutPerReCoy() {
        return increaseRate(this.takeOutPerRe(), this.takeOutPerReP());
    }

    // 下单新客占比（美团）
    @FormField("numberField_m3s5kpdt")
    public Float newCusOrderNumMtRate() {
        return divide(this.newCustomersOrderNumMt, this.orderPeopleNumMt);
    }

    // 下单新客占比（饿了么）
    @FormField("numberField_m3y4i3la")
    public Float newCusOrderNumElRate() {
        return divide(this.newCustomersOrderNumEl, this.orderPeopleNumEl);
    }

    public ShopReport() {
    }

    public ShopReport(LocalDateTime date) {
        this.date = date;
    }


    protected static Float increaseRate(Float c, Float l) {

        if (l == null || l == 0) {
            return 0f;
        }

        if (c == null || c == 0) {
            return 0f;
        }

        return ((c - l) / l) * 100;

    }

    protected static Float divide(Float a, Float b) {

        if (b == null || b == 0) {
            return 0f;
        }

        if (a == null) {
            a = 0f;
        }

        return (a / b) * 100;

    }
}
