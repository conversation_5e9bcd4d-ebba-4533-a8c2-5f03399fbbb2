package com.astenamic.new_discovery.biz.report.shop.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Entity;

@Entity(name = "yida_shop_daily_report_for_quarter")
@FormEntity("FORM-5776B43BEA8A436097DC735771D6963FLR2E")
public class ShopDailyReportForQuarter extends ShopReport {


    @FormField("textField_m48am7oz")
    public Integer getQuarter() {
        // 获取月份
        int month = this.getDate().getMonthValue();
        // 通过月份计算季度 (1-3月为Q1, 4-6月为Q2, 依此类推)
        return (month - 1) / 3 + 1;
    }
}
