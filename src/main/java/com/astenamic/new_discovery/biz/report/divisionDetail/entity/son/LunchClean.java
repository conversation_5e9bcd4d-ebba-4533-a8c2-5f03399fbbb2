package com.astenamic.new_discovery.biz.report.divisionDetail.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m65xm4dd")
public class LunchClean {

    // 时段
    @FormField("textField_m65xm4de")
    private String timePart;

    // 工作
    @FormField("textField_m65xm4df")
    private String workContent;

    // 事项
    @FormField("textField_m65xm4dg")
    private String matter;

}
