package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.base.SmartWarning;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DaySmartWarning;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;


@NoRepositoryBean
public interface SmartWarningRepository <T extends SmartWarning> extends JpaRepository<T, Long> {

    @Query(value = "SELECT A.shop_id AS shop_id,  \n" +
            "        ROW_NUMBER() OVER () AS id, \n" +
            "        NULL  AS object_id,\n" +
            "        sum(b.init_predict_amount) AS init_predict_amount,  \n" +
            "        sum(b.predict_amount) AS predict_amount,  \n" +
            "        round((sum(c.pos_field9) - sum(b.predict_amount)) / sum(c.pos_field9),2) AS predict_deviation,  \n" +
            "        CASE WHEN sum(b.init_predict_amount) IS NULL  \n" +
            "        THEN '未使用智能叫货'  \n" +
            "        WHEN sum(b.init_predict_amount) IS NOT NULL  \n" +
            "        AND ((sum(c.pos_field9) - sum(b.predict_amount)) / sum(c.pos_field9) >0.1 or (sum(c.pos_field9) - sum(b.predict_amount)) / sum(c.pos_field9) <-0.1)\n" +
            "        THEN '营业额偏差过高'  \n" +
            "        ELSE '正常' END AS smart_warning  \n" +
            "        FROM base_shop A  \n" +
            "        LEFT JOIN xfx_lgt_applygood b ON A.shop_id = b.shop_id  \n" +
            "        AND DATE(b.predict_start_date) = :target\n" +
            "\t\t\t\tleft join wt_shop c on a.shop_id=c.base_shop_id and c.base_date::date = DATE(b.predict_start_date)\n" +
            "        WHERE A.shop_name NOT IN ('新发现-来福士店','烤匠-来福士店','新发现-欢乐城店','烤匠-欢乐城店','烤匠-上亿店','新发现-上亿店','新发现-绍兴上虞万悦城店','新发现-绍兴镜湖天街店','新发现-桐乡万象汇店','新发现-上海崇明万达店')  \n" +
            "        AND (A.shop_name NOT LIKE '%毛菜%' AND A.shop_name NOT LIKE '%停用%')  \n" +
            "        AND A.status = 0  \n" +
            "        GROUP BY A.shop_id, A.shop_name,DATE(b.predict_start_date);", nativeQuery = true)
    List<T> findByTime(@Param("target") LocalDate target);
}
