package com.astenamic.new_discovery.biz.report.divisionDetail.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m65xm4dc")
public class DivisionInfoTimeTwo {

    // 时段
    @FormField("textField_m65xm4d5")
    private String timePart;

    // 排序
    @FormField("numberField_m65xm4d6")
    private Integer sort;

    // 备料及工作事项
    @FormField("textField_m65xm4d7")
    private String workMatters;

    // 具体事项与要求
    @FormField("textField_m65xm4d8")
    private String specificMatters;

    // 备货量
    @FormField("numberField_m65xm4d9")
    private Integer stockNum;

    // 单位
    @FormField("associationFormField_m65xm4da")
    private String unitSysId;

    // 工时
    @FormField("numberField_m65xm4db")
    private Double workHours;

//    public void setUnitSysId(String unitSysId) {
//        if(unitSysId != null) {
//            this.unitSysId = List.of(unitSysId);
//        }
//    }

}
