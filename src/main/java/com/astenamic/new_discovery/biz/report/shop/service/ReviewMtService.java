package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayReviewMt;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthReviewMt;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterReviewMt;
import com.astenamic.new_discovery.biz.report.shop.repository.base.ReviewMtRepository;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ReviewMtService extends RangeDataService {
    private final ReviewMtRepository<MonthReviewMt> monthRepository;
    private final ReviewMtRepository<QuarterReviewMt> quarterRepository;
    private final ReviewMtRepository<DayReviewMt> dayRepository;

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getMonthData(Time time) {
        return Pair.of(
                MonthReviewMt.class,
                this.monthRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getQuarterData(Time time) {
        return Pair.of(
                QuarterReviewMt.class,
                this.quarterRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    protected Pair<Class<? extends RangeData>, List<? extends RangeData>> getDayData(Time time) {
        return Pair.of(
                DayReviewMt.class,
                this.dayRepository.findByTime(time.getCurrent())
        );
    }

    @Override
    public Class<? extends RangeData> getClaz() {
        return DayReviewMt.class;
    }
}
