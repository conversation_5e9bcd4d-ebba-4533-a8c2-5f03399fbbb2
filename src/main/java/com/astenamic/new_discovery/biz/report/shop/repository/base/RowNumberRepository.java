package com.astenamic.new_discovery.biz.report.shop.repository.base;

import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@NoRepositoryBean
public interface RowNumberRepository<T extends RowNumber> extends JpaRepository<T, Long> {
    @Query(value = "WITH last AS (SELECT a.base_shop_id                                   AS base_shop_id\n" +
            "                   -- 门店桌数\n" +
            "                   , SUM(a.base_field19)                              AS table_num\n" +
            "                   -- 本期总实收\n" +
            "                   , SUM(a.pos_field16)                               AS revenue\n" +
            "                   -- 本期累计至当前日期的总营收\n" +
            "                   , SUM(a.pos_field16)                               AS acu_revenue\n" +
            "                   -- 本期总应收\n" +
            "                   , SUM(a.pos_field9)                                AS ar\n" +
            "                   -- 折扣总金额\n" +
            "                   , SUM(a.pos_field21)                               AS discount_amount\n" +
            "                   -- 本期堂食实收\n" +
            "                   , SUM(a.pos_field17)                               AS dine_in_revenue\n" +
            "                   -- 本期堂食实收（含自提）\n" +
            "                   , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec\n" +
            "                   -- 本期堂食应收（含自提）\n" +
            "                   , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar\n" +
            "                   -- 堂食折扣金额\n" +
            "                   , SUM(a.pos_field22)                               AS dine_in_discount_amount\n" +
            "                   -- 本期开桌数\n" +
            "                   , SUM(a.pos_field2)                                AS tables_open\n" +
            "                   -- 本期来客数\n" +
            "                   , SUM(a.pos_field7)                                AS cus_num\n" +
            "                   -- 本期堂食客流（含自提）\n" +
            "                   , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow\n" +
            "                   -- 本期外卖实收\n" +
            "                   , SUM(a.pos_field18)                               AS take_out_revenue\n" +
            "                   -- 本期外卖应收\n" +
            "                   , SUM(a.pos_field11)                               AS take_out_ar\n" +
            "                   -- 外卖折扣金额\n" +
            "                   , SUM(a.pos_field23)                               AS take_out_discount_amount\n" +
            "                   -- 本期外卖订单数\n" +
            "                   , SUM(a.pos_field4)                                AS take_out_num\n" +
            "              FROM wt_shop AS a\n" +
            "              WHERE a.base_date ~ :last\n" +
            "              GROUP BY a.base_shop_id),\n" +
            "\n" +
            "     pcp AS (SELECT a.base_shop_id                                   AS base_shop_id\n" +
            "                  -- 门店桌数\n" +
            "                  , SUM(a.base_field19)                              AS table_num\n" +
            "                  -- 本期总实收\n" +
            "                  , SUM(a.pos_field16)                               AS revenue\n" +
            "                  -- 本期累计至当前日期的总营收\n" +
            "                  , SUM(a.pos_field16)                               AS acu_revenue\n" +
            "                  -- 本期总应收\n" +
            "                  , SUM(a.pos_field9)                                AS ar\n" +
            "                  -- 折扣总金额\n" +
            "                  , SUM(a.pos_field21)                               AS discount_amount\n" +
            "                  -- 本期堂食实收\n" +
            "                  , SUM(a.pos_field17)                               AS dine_in_revenue\n" +
            "                  -- 本期堂食实收（含自提）\n" +
            "                  , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec\n" +
            "                  -- 本期堂食应收（含自提）\n" +
            "                  , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar\n" +
            "                  -- 堂食折扣金额\n" +
            "                  , SUM(a.pos_field22)                               AS dine_in_discount_amount\n" +
            "                  -- 本期开桌数\n" +
            "                  , SUM(a.pos_field2)                                AS tables_open\n" +
            "                  -- 本期来客数\n" +
            "                  , SUM(a.pos_field7)                                AS cus_num\n" +
            "                  -- 本期堂食客流（含自提）\n" +
            "                  , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow\n" +
            "                  -- 本期外卖实收\n" +
            "                  , SUM(a.pos_field18)                               AS take_out_revenue\n" +
            "                  -- 本期外卖应收\n" +
            "                  , SUM(a.pos_field11)                               AS take_out_ar\n" +
            "                  -- 外卖折扣金额\n" +
            "                  , SUM(a.pos_field23)                               AS take_out_discount_amount\n" +
            "                  -- 本期外卖订单数\n" +
            "                  , SUM(a.pos_field4)                                AS take_out_num\n" +
            "             FROM wt_shop AS a\n" +
            "             WHERE a.base_date ~ :sameMonthLastYear\n" +
            "             GROUP BY a.base_shop_id),\n" +
            "\n" +
            "     ret AS (SELECT a.shop_id\n" +
            "                  -- 退菜金额\n" +
            "                  , SUM(ABS(a.retreat_money)) AS retreat_money\n" +
            "             FROM retreat_statistics_shop_retreat_rate_report AS a\n" +
            "             WHERE a.retreat_range = '5分钟以上'\n" +
            "               AND a.account_date ~ :target\n" +
            "             GROUP BY a.shop_id),\n" +
            "\n" +
            "     bdg AS (SELECT a.base_shop_id\n" +
            "                  , SUM(a.pos_field16) AS budget_revenue\n" +
            "                  , SUM(a.pos_field44) AS budget_dine_in_revenue\n" +
            "                  , SUM(a.pos_field18) AS take_out_budget\n" +
            "             FROM data_budget_month AS a\n" +
            "             WHERE a.base_month ~ :months\n" +
            "             GROUP BY a.base_shop_id),\n" +
            "\n" +
            "    acu AS (SELECT a.base_shop_id     AS shop_id\n" +
            "                  -- 门店累计总应收\n" +
            "                  , SUM(a.pos_field16) AS acu_revenue\n" +
            "                  -- 堂食累计总应收\n" +
            "                  , SUM(a.pos_field17) AS dine_in_acu_revenue\n" +
            "                  -- 外卖累计总应收\n" +
            "                  , SUM(a.pos_field18) AS take_out_acu_revenue\n" +
            "             FROM wt_shop AS a\n" +
            "             WHERE a.base_date ~ :months\n" +
            "               AND a.base_date:: DATE >= :firstDayOfMonth :: DATE\n" +
            "               AND a.base_date:: DATE <= :flagDay  :: DATE\n" +
            "             GROUP BY a.base_shop_id),\n" +
            "     current AS (\n" +
            "         -- 门店id\n" +
            "         SELECT a.base_shop_id                                   AS base_shop_id\n" +
            "              -- 门店桌数\n" +
            "              , SUM(a.base_field19)                              AS table_num\n" +
            "              -- 本期总实收\n" +
            "              , SUM(a.pos_field16)                               AS revenue\n" +
            "              -- 本期累计至当前日期的总营收\n" +
            "              , SUM(a.pos_field16)                               AS acu_revenue\n" +
            "              -- 本期总应收\n" +
            "              , SUM(a.pos_field9)                                AS ar\n" +
            "              -- 折扣总金额\n" +
            "              , SUM(a.pos_field21)                               AS discount_amount\n" +
            "              -- 本期堂食实收\n" +
            "              , SUM(a.pos_field17)                               AS dine_in_revenue\n" +
            "              -- 本期堂食实收（含自提）\n" +
            "              , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec\n" +
            "              -- 本期堂食应收（含自提）\n" +
            "              , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar\n" +
            "              -- 堂食折扣金额\n" +
            "              , SUM(a.pos_field22)                               AS dine_in_discount_amount\n" +
            "              -- 本期开桌数\n" +
            "              , SUM(a.pos_field2)                                AS tables_open\n" +
            "              -- 本期来客数\n" +
            "              , SUM(a.pos_field7)                                AS cus_num\n" +
            "              -- 本期堂食客流（含自提）\n" +
            "              , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow\n" +
            "              -- 本期外卖实收\n" +
            "              , SUM(a.pos_field18)                               AS take_out_revenue\n" +
            "              -- 本期外卖应收\n" +
            "              , SUM(a.pos_field11)                               AS take_out_ar\n" +
            "              -- 外卖折扣金额\n" +
            "              , SUM(a.pos_field23)                               AS take_out_discount_amount\n" +
            "              -- 本期外卖订单数\n" +
            "              , SUM(a.pos_field4)                                AS take_out_num\n" +
            "         FROM wt_shop AS a\n" +
            "         WHERE base_date ~ :target\n" +
            "         GROUP BY a.base_shop_id)\n" +
            "\n" +
            "SELECT ROW_NUMBER() OVER ()            AS id\n" +
            "     , NULL                            AS object_id\n" +
            "     , NULL                            AS group_id\n" +
            "     -- 门店id\n" +
            "     , a.base_shop_id                  AS base_shop_id\n" +
            "     -- 门店编号\n" +
            "     , s.shop_no                       AS shop_no\n" +
            "     -- 时间\n" +
            "     , :flagDay                        AS date\n" +
            "     -- 本期门店桌数\n" +
            "     , SUM(a.table_num)                AS table_num\n" +
            "     -- 上期门店桌数\n" +
            "     , SUM(la.table_num)               AS table_num_l\n" +
            "     -- 同期门店桌数\n" +
            "     , SUM(pa.table_num)               AS table_num_p\n" +
            "     -- 本期总实收\n" +
            "     , SUM(a.revenue)                  AS revenue\n" +
            "     -- 环期总实收\n" +
            "     , SUM(la.revenue)                 AS revenue_l\n" +
            "     -- 同期总实收\n" +
            "     , SUM(pa.revenue)                 AS revenue_p\n" +
            "     -- 本期累计至当前日期的总营收\n" +
            "     , SUM(acu.acu_revenue)              AS acu_revenue\n" +
            "     --本期累计至当前日期的堂食累计总营收\n                " +
            "     , SUM(acu.dine_in_acu_revenue)\n    AS dine_in_acu_revenue" +
            "     -- 本期累计至当前日期的外卖累计总营收\n               " +
            "     , SUM(acu.take_out_acu_revenue)\n   AS take_out_acu_revenue" +
            "     -- 本期总应收\n" +
            "     , SUM(a.ar)                       AS ar\n" +
            "     -- 环期总应收\n" +
            "     , SUM(la.ar)                      AS ar_l\n" +
            "     -- 同期总应收\n" +
            "     , SUM(pa.ar)                      AS ar_p\n" +
            "     -- 营收目标额\n" +
            "     , SUM(b.budget_revenue)           AS budget_revenue\n" +
            "     -- 退菜金额\n" +
            "     , SUM(ret.retreat_money)          AS retreat_money\n" +
            "     -- 折扣总金额\n" +
            "     , SUM(a.discount_amount)          AS discount_amount\n" +
            "     -- 本期堂食实收\n" +
            "     , SUM(a.dine_in_revenue)          AS dine_in_revenue\n" +
            "     -- 本期堂食实收（含自提）\n" +
            "     , SUM(a.dine_in_rec)              AS dine_in_rec\n" +
            "     -- 环期堂食实收（含自提）\n" +
            "     , SUM(la.dine_in_rec)             AS dine_in_rec_l\n" +
            "     -- 同期堂食实收（含自提）\n" +
            "     , SUM(pa.dine_in_rec)             AS dine_in_rec_p\n" +
            "     -- 本期堂食应收（含自提）\n" +
            "     , SUM(a.dine_in_ar)               AS dine_in_ar\n" +
            "     -- 环期堂食应收（含自提）\n" +
            "     , SUM(la.dine_in_ar)              AS dine_in_ar_l\n" +
            "     -- 同期堂食应收（含自提）\n" +
            "     , SUM(pa.dine_in_ar)              AS dine_in_ar_p\n" +
            "     -- 堂食折扣金额\n" +
            "     , SUM(a.dine_in_discount_amount)  AS dine_in_discount_amount\n" +
            "     -- 堂食营收目标额\n" +
            "     , SUM(b.budget_dine_in_revenue)   AS budget_dine_in_revenue\n" +
            "     -- 本期开桌数\n" +
            "     , SUM(a.tables_open)              AS tables_open\n" +
            "     -- 环期开桌数\n" +
            "     , SUM(la.tables_open)             AS tables_open_l\n" +
            "     -- 同期开桌数\n" +
            "     , SUM(pa.tables_open)             AS tables_open_p\n" +
            "     -- 本期来客数\n" +
            "     , SUM(a.cus_num)                  AS cus_num\n" +
            "     -- 环期来客数\n" +
            "     , SUM(la.cus_num)                 AS cus_num_l\n" +
            "     -- 同期来客数\n" +
            "     , SUM(pa.cus_num)                 AS cus_num_p\n" +
            "     -- 本期堂食客流（含自提）\n" +
            "     , SUM(a.cus_flow)                 AS cus_flow\n" +
            "     -- 环期堂食客流（含自提）\n" +
            "     , SUM(la.cus_flow)                AS cus_flow_l\n" +
            "     -- 同期堂食客流（含自提）\n" +
            "     , SUM(pa.cus_flow)                AS cus_flow_p\n" +
            "     -- 本期外卖实收\n" +
            "     , SUM(a.take_out_revenue)         AS take_out_revenue\n" +
            "     -- 环期外卖实收\n" +
            "     , SUM(la.take_out_revenue)        AS take_out_revenue_l\n" +
            "     -- 同期外卖实收\n" +
            "     , SUM(pa.take_out_revenue)        AS take_out_revenue_p\n" +
            "     -- 本期外卖应收\n" +
            "     , SUM(a.take_out_ar)              AS take_out_ar\n" +
            "     -- 环期外卖应收\n" +
            "     , SUM(la.take_out_ar)             AS take_out_ar_l\n" +
            "     -- 同期外卖应收\n" +
            "     , SUM(pa.take_out_ar)             AS take_out_ar_p\n" +
            "     -- 外卖折扣金额\n" +
            "     , SUM(a.take_out_discount_amount) AS take_out_discount_amount\n" +
            "     -- 外卖实收目标额\n" +
            "     , SUM(b.take_out_budget)          AS take_out_budget\n" +
            "     -- 本期外卖订单数\n" +
            "     , SUM(a.take_out_num)             AS take_out_num\n" +
            "     -- 环期外卖订单数\n" +
            "     , SUM(la.take_out_num)            AS take_out_num_l\n" +
            "     -- 同期外卖订单数\n" +
            "     , SUM(pa.take_out_num)            AS take_out_num_p\n" +
            "FROM current AS a\n" +
            "         LEFT JOIN base_shop AS s ON s.shop_id = a.base_shop_id\n" +
            "         LEFT JOIN last AS la ON la.base_shop_id = a.base_shop_id\n" +
            "         LEFT JOIN pcp AS pa ON pa.base_shop_id = a.base_shop_id\n" +
            "         LEFT JOIN bdg AS b ON b.base_shop_id = a.base_shop_id\n" +
            "         LEFT JOIN ret ON ret.shop_id = a.base_shop_id\n" +
            "         LEFT JOIN acu ON acu.shop_id = a.base_shop_id\n" +
            "GROUP BY a.base_shop_id, s.shop_no;\n", nativeQuery = true)
    List<T> findByTime(@Param("target") String target,
                               @Param("last") String last,
                               @Param("sameMonthLastYear") String sameMonthLastYear,
                               @Param("months") String months,
                               @Param("flagDay") String flagDay,
                               @Param("firstDayOfMonth") String firstDayOfMonth);
}

