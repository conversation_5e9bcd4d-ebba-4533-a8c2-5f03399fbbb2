package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.ace.scm.supplier.entity.SaveSupplier;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity("tableField_m5bxvpci")
public class Fresh extends BaseEntity {

    // 物料名称
    @FormField("textField_m53qedne")
    private String good_name;

    // 物料编码
    @FormField("textField_m5fcpknd")
    private String good_sno;

    // 货品大类
    @FormField("textField_m5fb6rfh")
    private String fgoodtype_name;

    // 货品小类
    @FormField("textField_m5fb6rfi")
    private String goodtype_name;

    // 规格
    @FormField("textField_m53qednk")
    private String std;

    // 上月采购数量
    @FormField("numberField_m5c0jmpx")
    private String applyamount;

    // 下次定价
    @FormField("numberField_m53qedno")
    private Float uprice;

    // 供应商(关联)
    @FormField("associationFormField_m5f571it")
    private List<SaveSupplier> suppliers;

    // 供应商编码
    @FormField("textField_m64iihr4")
    private String supplier_sno;

    // 价格执行门店名称(关联)
    @FormField("multiSelectField_m5c3myxb")
    private List<String> shopNames;

    // 价格执行门店ID列表
    @FormField("textField_m5c3myxc")
    private String shop_id;

    // 门店编码
    @FormField("textField_m5f571io")
    private String shop_sno;

    // 下月采购金额预估
    @FormField("numberField_m5fd1t64")
    private Float estmoney;

    // 去年同期平均单价
    @FormField("numberField_m5fd1t6b")
    private String lastyprice;

    // 上期平均采购单价
    @FormField("numberField_m5fd1t6c")
    private String lastprice;

    // 当前执行价格
    @FormField("numberField_m5uqf90h")
    private Float nowprice;

    // 价格开始生效日期
    @FormField("dateField_m5uqf90i")
    private LocalDateTime startdate;

    // 环比调价比率
    @FormField("numberField_m5uqf90k")
    private String momratio;

    // 供应商ID
    @FormField("textField_m5f571is")
    private String supplier_id;

    // 供应商属性， 0 普通供应商 1 门店自购供应商 2 总部代采供应商
    @FormField("textField_m60j299c")
    private String bselfpur;

    // 结算方式(1 货到付款 2 预付 7 周付 10 十天结算 14 半月付 28 月付 60 双月付 3 季度付 6 半年付 12 年付 29 压批结算)
    @FormField("textField_m60j299e")
    private String accountdatetype;

    // 发票方式(1.增值税专用发票；2.农副产品收购发票；3.农产品增值税普通发票；4.增值税普通发票；5.无发票；)
    @FormField("textField_m60j299d")
    private String invoicetype;

    // 联系人手机
    @FormField("textField_m60j299f")
    private String phoneNumber;

    @FormField("selectField_m60l1i1a")
    private String supplier_name;

    public void setSuppliers(List<SaveSupplier> suppliers) {
        if(suppliers != null) {
            this.suppliers = suppliers;
        }
    }

    public void setShopNames(List<String> shopNames) {
        if (shopNames != null) {
            this.shopNames = shopNames;
        }
    }

}
