package com.astenamic.new_discovery.biz.report.divisionDetail.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m5w79ie7")
public class DivisionInfoTimeOne {

    // 时段
    @FormField("textField_m5w79ie8")
    private String timePart;

    // 排序
    @FormField("numberField_m5w79ie9")
    private Integer sort;

    // 备料及工作事项
    @FormField("textField_m5w79iea")
    private String workMatters;

    // 具体事项与要求
    @FormField("textField_m5w79ieb")
    private String specificMatters;

    // 备货量
    @FormField("numberField_m5w79iec")
    private Integer stockNum;

    // 单位
    @FormField("associationFormField_m5w79ied")
    private String unitSysId;

    // 工时
    @FormField("numberField_m5w79iee")
    private Double workHours;

//    public void setUnitSysId(String unitSysId) {
//        if(unitSysId != null) {
//            this.unitSysId = List.of(unitSysId);
//        }
//    }

}
