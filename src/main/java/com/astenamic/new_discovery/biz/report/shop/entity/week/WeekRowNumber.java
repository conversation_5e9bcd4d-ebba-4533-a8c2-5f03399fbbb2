package com.astenamic.new_discovery.biz.report.shop.entity.week;

import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Data;

@Data
@Entity(name = "yida_week_row_number")
@FormEntity("FORM-74EF5E78FBDF46CB9FD5BBB1924B91E8MWB5")
public class WeekRowNumber extends RowNumber {

    // 门店id
    @Column(name = "base_shop_id")
    @FormField("textField_m3o3hk0q")
    private String shopId;

    // 周营收目标
    @Transient
    @FormField("numberField_m8o2kw6r")
    private Float weekBudgetRevenue;

    // 营收目标达成率
    @FormField("numberField_m3o3hk2n")
    public Float dayBudgetRevenue() {
        if (weekBudgetRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getRevenue(), this.weekBudgetRevenue);
    }

    // 周堂食营收目标
    @Transient
    @FormField("numberField_m8o2kw6u")
    private Float weekBudgetDineInRevenue;

    // 堂食营收目标达成率
    @FormField("numberField_m3s5kpe4")
    private Float weekBudgetDineInRevenue() {
        if (weekBudgetDineInRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getDineInRec(), this.weekBudgetDineInRevenue);
    }

    // 周外卖营收目标
    @Transient
    @FormField("numberField_m8o2kw74")
    private Float weekBudgetTakeOutRevenue;

    // 外卖营收目标达成率
    @FormField("numberField_m3s5kpdl")
    private Float weekBudgetTakeOutRevenue() {
        if (weekBudgetTakeOutRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getTakeOutRevenue(), this.weekBudgetTakeOutRevenue);
    }

    // 周
    @Transient
    @FormField("textField_m8qrlq43")
    private String week;

}
