package com.astenamic.new_discovery.biz.report.shop.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class ArrayUtils {

    public static <T> List<List<T>> splitCollection(Collection<T> collection, int size) {
        List<List<T>> result = new ArrayList<>();
        List<T> currentList = new ArrayList<>(size);

        for (T element : collection) {
            currentList.add(element);
            if (currentList.size() == size) {
                result.add(new ArrayList<>(currentList));
                currentList.clear();
            }
        }

        // 如果最后的子集合不为空，将其加入结果列表
        if (!currentList.isEmpty()) {
            result.add(new ArrayList<>(currentList));
        }

        return result;
    }
}
