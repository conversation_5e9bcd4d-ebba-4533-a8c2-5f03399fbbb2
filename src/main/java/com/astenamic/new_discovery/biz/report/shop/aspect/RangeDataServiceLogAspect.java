package com.astenamic.new_discovery.biz.report.shop.aspect;


import com.alibaba.fastjson2.JSON;
import com.astenamic.new_discovery.biz.report.shop.annotation.RangeDataServiceLog;
import com.astenamic.new_discovery.biz.report.shop.enums.RangeDataServiceEnum;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Aspect
@Component
public class RangeDataServiceLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(RangeDataServiceLogAspect.class);
    private static final DateTimeFormatter YMDFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Around("@annotation(com.astenamic.new_discovery.biz.report.shop.annotation.RangeDataServiceLog)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;

        try {
            // 获取方法签名及注解
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            RangeDataServiceLog logAnnotation = method.getAnnotation(RangeDataServiceLog.class);

            // 获取目标对象类名
            String className = joinPoint.getTarget().getClass().getSimpleName();

            // 获取服务名称
            String serviceName = Optional.ofNullable(RangeDataServiceEnum.getByClassName(className))
                    .map(RangeDataServiceEnum::getDesc)
                    .orElse(className);

            // 获取注解中的额外信息（如"每日经营"/"每周经营"）
            String operationType = logAnnotation.value();

            // 从参数获取日期
            LocalDateTime targetDate = null;
            for (Object arg : joinPoint.getArgs()) {
                if (arg instanceof LocalDateTime) {
                    targetDate = (LocalDateTime) arg;
                    break;
                }
            }

            String dateStr = targetDate != null ? targetDate.format(YMDFormatter) : "";

            try {
                logger.info("{}{}{} 数据获取开始", dateStr, operationType, serviceName);
            } catch (Exception e) {
                logger.error("记录开始日志异常", e);
            }
        } catch (Exception e) {
            logger.error("切面前置处理异常", e);
        }

        // 无论发生什么异常，都确保原方法被执行
        result = joinPoint.proceed();

        try {
            // 获取方法签名及注解（重新获取，避免前面异常导致数据丢失）
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            RangeDataServiceLog logAnnotation = method.getAnnotation(RangeDataServiceLog.class);

            String className = joinPoint.getTarget().getClass().getSimpleName();
            String serviceName = Optional.ofNullable(RangeDataServiceEnum.getByClassName(className))
                    .map(RangeDataServiceEnum::getDesc)
                    .orElse(className);
            String operationType = logAnnotation.value();

            // 再次从参数获取日期
            LocalDateTime targetDate = null;
            for (Object arg : joinPoint.getArgs()) {
                if (arg instanceof LocalDateTime) {
                    targetDate = (LocalDateTime) arg;
                    break;
                }
            }

            String dateStr = targetDate != null ? targetDate.format(YMDFormatter) : "";

            try {
                logger.info("{}{}{} 数据获取结束:{}", dateStr, operationType, serviceName, JSON.toJSONString(result));
            } catch (Exception e) {
                logger.error("记录结束日志异常", e);
            }
        } catch (Exception e) {
            logger.error("切面后置处理异常", e);
        }

        return result;
    }
}
