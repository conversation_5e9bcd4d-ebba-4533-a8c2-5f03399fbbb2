package com.astenamic.new_discovery.biz.report.shop.entity.dayGold;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import lombok.Data;

import java.time.LocalDateTime;

@FormEntity("tableField_m5mb0oeq")
@Data
public class DayGoal {

    @FormField("dateField_m5mb0oer")
    private LocalDateTime date;

    // 日营收目标
    @FormField("numberField_m5mb0oes")
    @Column(name = "day_revenue")
    private Float dayRevenue;

    // 日堂食目标
    @FormField("textField_m5mb0oet")
    @Column(name = "day_dine_in_revenue")
    private Float dayDineInRevenue;

    // 日外卖目标
    @FormField("numberField_m5mb0oeu")
    @Column(name = "day_take_out_revenue")
    private Float dayTakeOutRevenue;
}
