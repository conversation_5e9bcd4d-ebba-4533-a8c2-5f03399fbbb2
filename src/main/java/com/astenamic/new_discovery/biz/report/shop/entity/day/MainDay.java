package com.astenamic.new_discovery.biz.report.shop.entity.day;

import com.astenamic.new_discovery.biz.report.shop.utils.MathUtil;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class MainDay {
    // 门店id
    @FormField("textField_m3o3hk0q")
    private String shopId;

    @Transient
    private Float dayBudgetRevenue;

    // 营收目标达成率
    @FormField("numberField_m3o3hk2n")
    public Float dayBudgetRevenue(){
        if(dayBudgetRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getRevenue(), this.dayBudgetRevenue);
    }

    @Transient
    private Float dayBudgetDineInRevenue;

    @FormField("numberField_m3s5kpe4")
    private Float dayBudgetDineInRevenue(){
        if(dayBudgetDineInRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getDineInRec(), this.dayBudgetDineInRevenue);
    }


    @Transient
    private Float dayBudgetTakeOutAcuRevenue;

    // 外卖目标达成率
    @FormField("numberField_m3s5kpdl")
    public Float dayBudgetTakeOutAcuRevenue(){
        if(dayBudgetTakeOutAcuRevenue == null) {
            return 0.00F;
        }
        return MathUtil.divide(this.getTakeOutRevenue(), this.dayBudgetTakeOutAcuRevenue);
    }

    // 品牌id
    @FormField("departmentSelectField_m41d4qnv")
    @Transient
    private List<String> brandSysId;

    // 战区id
    @FormField("departmentSelectField_m419c2zv")
    @Transient
    private List<String> batAreaSysId;

    // 战区司令id
    @FormField("employeeField_m3xtfto9")
    @Transient
    private List<String> batAreaPeopleSysId;

    // 区域
    @FormField("departmentSelectField_m419c2zu")
    @Transient
    private List<String> areaSysId;

    // 督导
    @FormField("employeeField_m3xtfto6")
    @Transient
    private List<String> supervisorSysId;

    // 门店
    @FormField("departmentSelectField_m419c2zt")
    @Transient
    private List<String> shopSysId;

    // 门店经理id
    @FormField("employeeField_m3xtfto8")
    @Transient
    private List<String> shopManagerSysId;

    // 厨师长
    @FormField("employeeField_m42llkno")
    @Transient
    private List<String> chiefSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setShopManagerSysId(String shopManagerSysId) {
        if (shopManagerSysId != null) {
            this.shopManagerSysId = List.of(shopManagerSysId);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }

    private String shopNo;

    // 群id
    @FormField("textField_m3zs7kq9")
    private String groupId;

    // 时间
    @FormField("dateField_m3weopav")
    private LocalDateTime date;

    // 门店桌数
    @FormField("numberField_m3o3hk39")
    private Float tableNum;

    // 固定桌数 环期
    private Float tableNumL;

    // 固定桌数 同期
    @FormField("numberField_m3zs7kqa")
    private Float tableNumP;

    // 开台率环期
    @FormField("numberField_m410cbe7")
    public Float tableReopenRateR(){
        return MathUtil.divide(tablesOpenL, tableNumL*2);
    }

    // 本期总实收
    @FormField("numberField_m3s5kpbj")
    private Float revenue;

    // 环期总实收
    @FormField("numberField_m3s5kpbn")
    private Float revenueL;

    // 同期总实收
    @FormField("numberField_m3s5kpbz")
    private Float revenueP;

    // 总实收同比
    @FormField("numberField_m3s9pzpj")
    public Float revenueCoy() {
        return MathUtil.increaseRate(this.revenue, this.revenueP);
    }

    // 总实收环比
    @FormField("numberField_m3s9pzpl")
    public Float revenueCol() {
        return MathUtil.increaseRate(this.revenue, this.revenueL);
    }

    //同期堂食实收（含自提）
    @FormField("numberField_m3s5kpbv")
    private Float dineInRecP;

    //堂食实收同比
    @FormField("numberField_m3s5kpdz")
    public Float dineInCoy() {
        return MathUtil.increaseRate(this.dineInRec, this.dineInRecP);
    }

    // 本期累计至当前日期的总营收
    private Float acuRevenue;

    // 本期累计至当前日期的堂食累计总营收
    private Float dineInAcuRevenue;

    // 本期累计至当前日期的外卖累计总营收
    private Float takeOutAcuRevenue;


    // 总应收同比
    @FormField("numberField_m3zhztde")
    public Float arCoy() {
        return MathUtil.increaseRate(this.ar, this.arP);
    }

    // 总应收环比
    @FormField("numberField_m3zhztdf")
    public Float arCol() {
        return MathUtil.increaseRate(this.ar, this.arL);
    }

    // 本期总应收
    @FormField("numberField_m3zhztda")
    private Float ar;

    // 环期总应收
    @FormField("numberField_m3zhztdc")
    private Float arL;

    // 同期总应收
    @FormField("numberField_m3zhztdb")
    private Float arP;

    // 营收目标额
    @FormField("numberField_m3s5kpci")
    private Float budgetRevenue;

    // 折扣总金额
    private Float discountAmount;

    // 本期堂食实收
    private Float dineInRevenue;

    // 本期堂食实收（含自提）
    @FormField("numberField_m3s5kpbt")
    private Float dineInRec;

    //堂食应收同比
    @FormField("numberField_m3zhztdj")
    public Float dineInArCoy() {
        return MathUtil.increaseRate(this.dineInAr, this.dineInArP);
    }

    // 本期堂食应收（含自提）
    @FormField("numberField_m3sc02zt")
    private Float dineInAr;

    // 环期堂食应收（含自提）
    @FormField("numberField_m3zhztdi")
    private Float dineInArL;

    // 同期堂食应收（含自提）
    @FormField("numberField_m3zhztdh")
    private Float dineInArP;

    // 堂食折扣金额
    @FormField("numberField_m3s5kpcs")
    private Float dineInDiscountAmount;

    // 堂食营收目标额
    @FormField("numberField_m3o3hk33")
    private Float budgetDineInRevenue;

    // 本期开桌数
    @FormField("numberField_m3o3hk35")
    private Float tablesOpen;

    // 环期开桌数
    @FormField("numberField_m3s5kpcz")
    private Float tablesOpenL;

    // 同期开桌数
    @FormField("numberField_m3s5kpcy")
    private Float tablesOpenP;

    // 本期来客数
    @FormField("numberField_m3o3hk36")
    private Float cusNum;

    // 环期来客数
    @FormField("numberField_m3s5kpd2")
    private Float cusNumL;

    // 同期来客数
    @FormField("numberField_m3s5kpd1")
    private Float cusNumP;

    // 本期堂食客流（含自提）
    @FormField("numberField_m3o3hk38")
    private Float cusFlow;

    // 环期堂食客流（含自提）
    @FormField("numberField_m3s5kpd9")
    private Float cusFlowL;

    // 同期堂食客流（含自提）
    @FormField("numberField_m3s5kpd8")
    private Float cusFlowP;

    // 本期外卖实收
    @FormField("numberField_m3o3hk2l")
    private Float takeOutRevenue;

    // 环期外卖实收
    @FormField("numberField_m3s5kpcb")
    private Float takeOutRevenueL;

    // 同期外卖实收
    @FormField("numberField_m3s5kpca")
    private Float takeOutRevenueP;

    //外卖实收同比
    @FormField("numberField_m3o3hk3i")
    private Float takeOutRevenueRRate() {
        return MathUtil.increaseRate(this.takeOutRevenue, this.takeOutRevenueP);
    }

    //外卖实收环比
    @FormField("numberField_m3s5kpe2")
    private Float takeOutRevenueLRate() {
        return MathUtil.increaseRate(this.takeOutRevenue, this.takeOutRevenueL);
    }

    //  外卖应收环比
    @FormField("numberField_m3zhztdp")
    private Float takeOutArLRate() {
        return MathUtil.increaseRate(this.takeOutAr, this.takeOutArL);
    }

    //外卖订单数同比
    @FormField("numberField_m3s5kpdn")
    public Float takeOutNumPRate() {
        return MathUtil.increaseRate(this.takeOutNum, this.takeOutNumP);
    }

    //外卖订单数环比
    @FormField("numberField_m3s5kpdq")
    public Float takeOutNumLRate() {
        return MathUtil.increaseRate(this.takeOutNum, this.takeOutNumL);
    }

    // 本期外卖应收
    @FormField("numberField_m3zhztdl")
    private Float takeOutAr;

    // 环期外卖应收
    @FormField("numberField_m3zhztdn")
    private Float takeOutArL;

    // 同期外卖应收
    @FormField("numberField_m3zhztdm")
    private Float takeOutArP;

    // 外卖折扣金额
    @FormField("numberField_m3zhztd9")
    private Float takeOutDiscountAmount;

    // 外卖实收目标额
    @FormField("numberField_m3o3hk3h")
    private Float takeOutBudget;

    // 本期外卖订单数
    @FormField("numberField_m3o3hk3j")
    private Float takeOutNum;

    // 环期外卖订单数
    @FormField("numberField_m3s5kpdp")
    private Float takeOutNumL;

    // 外卖单均本期
    @FormField("numberField_m3s5kpdr")
    public Float takeOutPerRe() {
        return MathUtil.divide(this.takeOutRevenue, this.takeOutNum) / 100;
    }

    // 外卖单均同期
    @FormField("numberField_m410cbe6")
    public Float takeOutPerReP() {
        return MathUtil.divide(this.takeOutRevenueP, this.takeOutNumP) / 100;
    }

    // 外卖单均同比
    @FormField("numberField_m3o3hk3l")
    public Float takeOutPerReCoy() {
        return MathUtil.increaseRate(this.takeOutPerRe(), this.takeOutPerReP());
    }

    // 外卖单均环期
    @FormField("numberField_m410cbe4")
    public Float takeOutPerReL() {
        return MathUtil.divide(this.takeOutRevenueL, this.takeOutNumL) / 100;
    }

    //外卖收入占比(应收)
    @FormField("numberField_m3zs7kqc")
    public Float takeOutAePerRate() {
        return MathUtil.divide(this.takeOutAr, this.ar);
    }

    //外卖收入占比(实收)
    @FormField("numberField_m3o3hk3k")
    public Float takeOutRePerRate() {
        return MathUtil.divide(this.takeOutRevenue, this.revenue);
    }

    // 外卖单均环比
    @FormField("numberField_m410cbe5")
    public Float takeOutPerReCol() {
        return MathUtil.increaseRate(this.takeOutPerRe(), this.takeOutPerReL());
    }

    //  外卖应收环比
    @FormField("numberField_m3zhztdo")
    private Float takeOutArPRate() {
        return MathUtil.increaseRate(this.takeOutAr, this.takeOutArP);
    }

    // 同期外卖订单数
    @Column(name = "take_out_num_p")
    @FormField("numberField_m3s5kpdo")
    private Float takeOutNumP;

    // 堂食折扣率
    @FormField("numberField_m3o3hk2p")
    public Float dineInDiscountRate() {
        return MathUtil.divide(this.dineInDiscountAmount, this.dineInAr);
    }

    // 外卖折扣率
    @FormField("numberField_m3zhztdg")
    public Float takeOutDiscountRate() {
        return MathUtil.divide(this.takeOutDiscountAmount, this.takeOutAr);
    }

    //开桌数同比
    @FormField("numberField_m3s5kpcx")
    public Float tablesOpenCoy() {
        return MathUtil.increaseRate(this.tablesOpen, this.tablesOpenP);
    }

    //开桌数环比
    @FormField("numberField_m3s5kpd3")
    public Float tablesOpenCol() {
        return MathUtil.increaseRate(this.tablesOpen, this.tablesOpenL);
    }

    // 开台率
    @FormField("numberField_m3s5kpdd")
    public Float tableReopenRate() {
        return MathUtil.divide(this.tablesOpen, this.tableNum*2);
    }

    // 开台率同比
    @FormField("numberField_m3zs7kqb")
    public Float tableReopenRateCoy() {

        if(this.tablesOpenP == null){
            return (float) 0;
        }

        // 同期开台率
        Float l = MathUtil.increaseRate(this.tablesOpenP, this.tableNumP*2);

        return MathUtil.increaseRate(this.tableReopenRate(), l);
    }

    //来客数同比
    @FormField("numberField_m3s5kpd0")
    public Float cusNumCoy() {
        return MathUtil.increaseRate(this.cusNum, this.cusNumP);
    }

    //来客数环比
    @FormField("numberField_m3s5kpd4")
    public Float cusNumCol() {
        return MathUtil.increaseRate(this.cusNum, this.cusNumL);
    }

    //桌均环期
    @FormField("numberField_m3s9pzpp")
    public Float tablesAvgL() {
        return MathUtil.divide(this.dineInRecL, this.tablesOpenL) / 100;
    }

    //桌均同期
    @FormField("numberField_m3s9pzpo")
    public Float tablesAvgP() {
        return MathUtil.divide(this.dineInRecP, this.tablesOpenP) / 100;
    }

    //桌均同比
    @FormField("numberField_m3s5kpd5")
    public Float avgTableCoy() {
        return MathUtil.increaseRate(this.tablesAvg(), this.tablesAvgP());
    }

    //桌均环比
    @FormField("numberField_m3s5kpdc")
    public Float avgTableCol() {
        return MathUtil.increaseRate(this.tablesAvg(), this.tablesAvgL());
    }

    //桌均本期
    @FormField("numberField_m3s9pzpn")
    public Float tablesAvg() {
        return MathUtil.divide(this.dineInRec, this.tablesOpen) / 100;
    }

    //客单价本期
    @FormField("numberField_m3s9pzpq")
    public Float aov() {
        return MathUtil.divide(this.dineInRec, this.cusFlow) / 100;
    }

    //客单价同期
    @FormField("numberField_m3s9pzpr")
    public Float aovP() {
        return MathUtil.divide(this.dineInRecP, this.cusFlowP) / 100;
    }

    //客单价环期
    @FormField("numberField_m3s9pzps")
    public Float aovL() {
        return MathUtil.divide(this.dineInRecL, this.cusFlowL) / 100;
    }

    //客单价同比
    @FormField("numberField_m3s5kpda")
    public Float aovCoy() {
        return MathUtil.increaseRate(this.aov(), this.aovP());
    }

    //客单价环比
    @FormField("numberField_m3s5kpdb")
    public Float aovCol() {
        return MathUtil.increaseRate(this.aov(), this.aovL());
    }

    //退菜金额
    @FormField("numberField_m3s5kpck")
    private Float retreatMoney;

    //退菜率
    @FormField("numberField_m3o3hk2q")
    public Float retreatRat() {
        return MathUtil.divide(this.retreatMoney, this.ar);
    }

    //环期堂食实收（含自提）
    @FormField("numberField_m3s5kpbw")
    private Float dineInRecL;

    //堂食实收环比
    @FormField("numberField_m3s5kpe0")
    public Float dineInCol() {
        return MathUtil.increaseRate(this.dineInRec, this.dineInRecL);
    }

    //堂食应收环比
    @FormField("numberField_m3zhztdk")
    public Float dineInArCol() {
        return MathUtil.increaseRate(this.dineInAr, this.dineInArL);
    }

    // 流失人数
    private Float lsNumber;

    //等位流失率
    @FormField("numberField_m3s5kpdk")
    private Float eqLossRate() {
        return MathUtil.divide(this.lsNumber, this.qhNumber);
    }

    // 取号人数
    private Float qhNumber;

}
