package com.astenamic.new_discovery.biz.report.material.controller;

import com.astenamic.new_discovery.biz.report.material.entity.MaterialCostReport;
import com.astenamic.new_discovery.biz.report.material.service.MaterialCostReportService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/biz/report")
@AllArgsConstructor
public class MaterialCostReportController {

    private MaterialCostReportService materialCostReportService;

    @PostMapping("/material/cost/yida")
    public List<MaterialCostReport> cost(@RequestBody Param param) {
        String dateStr = param.date;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime target = LocalDateTime.parse(dateStr, formatter);

        return this.materialCostReportService.saveCurrentToYiDa(target);
    }

    @Data
    private static class Param {
        private String date;
    }
}
