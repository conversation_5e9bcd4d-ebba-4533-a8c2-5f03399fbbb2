package com.astenamic.new_discovery.biz.report.food.entity.month;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@FormEntity(value = "FORM-4028115551B9490FBC609B29FED00077Q9BM")
@Getter
@Setter
public class FoodMonth extends BaseEntity {

    // 年月
    @FormField("dateField_m5otglj3")
    private LocalDateTime yearMonth;

    // 门店编码
    @FormField("textField_m5otgli4")
    private String shopNo;

    // MD5
    private String md5;

    // 门店ID
    @FormField("textField_m5otgli8")
    private String shopId;

    @FormField("departmentSelectField_m5ozoolm")
    private List<String> shopSysId;

    // 门店名称
    private String shopName;

    // 品牌
    @FormField("departmentSelectField_m5ozoolo")
    private List<String> brandSysId;

    // 区域
    @FormField("departmentSelectField_m5ozooln")
    private List<String> areaSysId;

    // 菜品
    @FormField("tableField_m5oyw36b")
    private List<FoodInfo> foods;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

}
