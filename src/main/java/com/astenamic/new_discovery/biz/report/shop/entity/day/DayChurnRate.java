package com.astenamic.new_discovery.biz.report.shop.entity.day;

import com.astenamic.new_discovery.biz.report.shop.entity.base.ChurnRate;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import jakarta.persistence.Entity;
import lombok.Data;

@Data
@Entity(name = "yida_day_churn_rate")
@FormEntity("FORM-1BB30970AA174858932487B079A59184IRTU")
public class DayChurnRate extends ChurnRate {
}
