package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity("tableField_m5f1bzd1")
public class AddPriceShop {

    // 门店名称
    @FormField("textField_m5f1bzd4")
    private String shopName;

    // 门店编码
    @FormField("numberField_m5f1bzd2")
    private String shop_sno;

}
