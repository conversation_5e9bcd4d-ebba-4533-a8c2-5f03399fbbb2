package com.astenamic.new_discovery.biz.report.shop.entity.dayGold;

import jakarta.persistence.Column;
import lombok.Data;

import java.time.LocalDateTime;

@Data

public class GetMonthRevenue {

    private LocalDateTime date;

    @Column(name = "shop_id")
    private String shopId;

    @Column(name = "day_acu_revenue")
    private Float dayRevenue;

    @Column(name = "day_dine_in_acu_revenue")
    private Float dayDineInRevenue;

    @Column(name = "day_take_out_acu_revenue")
    private Float dayTakeOutRevenue;
}
