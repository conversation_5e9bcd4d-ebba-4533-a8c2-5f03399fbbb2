package com.astenamic.new_discovery.biz.report.material.service;

import com.astenamic.new_discovery.biz.relation.shop.entity.ShopRelation;
import com.astenamic.new_discovery.biz.relation.shop.service.ShopRelationService;
import com.astenamic.new_discovery.biz.report.material.entity.MaterialCostReport;
import com.astenamic.new_discovery.biz.report.material.repository.MaterialCostReportRepository;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
public class MaterialCostReportService {

    private final Logger logger = LoggerFactory.getLogger(MaterialCostReportService.class);

    private final YiDaSession yiDaSession;
    private final MaterialCostReportRepository materialCostReportRepository;
    private final DateTimeFormatter dateTimeFormatter;
    private final ShopRelationService shopRelationService;

    private final RestTemplate restTemplate;

    public MaterialCostReportService(YiDaSession yiDaSession,
                                     MaterialCostReportRepository materialCostReportRepository,
                                     @Qualifier("DateTimeYMDFormatter")
                                     DateTimeFormatter dateTimeFormatter,
                                     ShopRelationService shopRelationService) {
        this.yiDaSession = yiDaSession;
        this.materialCostReportRepository = materialCostReportRepository;
        this.dateTimeFormatter = dateTimeFormatter;
        this.shopRelationService = shopRelationService;
        this.restTemplate = new RestTemplate();
    }

    public List<MaterialCostReport> saveCurrentToYiDa(LocalDateTime day) {

        logger.info("同步成本卡数据: {}", day);
        String dayStr = dateTimeFormatter.format(day);
        List<MaterialCostReport> oldData = this.findMaterialCostReportByDate(day);
        logger.info("所有成本卡数据: {}", oldData.size());
        if (oldData != null && !oldData.isEmpty()) {
            while (!oldData.isEmpty()) {
                int size = Math.min(oldData.size(), 100);
                List<MaterialCostReport> delCost = oldData.subList(0, size);
                this.yiDaSession.batchDeleteByObjectId(delCost, MaterialCostReport.class);
                oldData.subList(0, size).clear();
            }
        }
        refreshDingTalkCache();
        List<MaterialCostReport> newData = this.materialCostReportRepository.findCurrent(dayStr);
        logger.info("新成本卡数据: {}", newData.size());
        List<ShopRelation> relations = this.shopRelationService.getRelations();

        for (MaterialCostReport materialCostReport : newData) {
            for (ShopRelation relation : relations) {
                if (materialCostReport.getShopId().equals(relation.getShopId())) {
                    materialCostReport.setBrandSysId(relation.getBrandSysId());
                    break;
                }
            }
        }
        List<List<MaterialCostReport>> groups = this.splitCollection(newData, 100);

        for (List<MaterialCostReport> group : groups) {
            this.yiDaSession.batchSave(group, MaterialCostReport.class);
        }
        refreshDingTalkCache();
        return newData;
    }


    /**
     * 刷新钉钉缓存
     */
    public void refreshDingTalkCache() {
        try {
            logger.info("开始刷新钉钉缓存");
            String url = "https://xfx.clojve.cn/prod-api/kitchen/division/dingtalk/cache/refreshAllCache";
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            boolean success = response.getStatusCode() == HttpStatus.OK;
            logger.info("刷新钉钉缓存{}，响应状态: {}", success ? "成功" : "失败", response.getStatusCode());
        } catch (Exception e) {
            logger.error("刷新钉钉缓存异常", e);
        }
    }


    public List<MaterialCostReport> findMaterialCostReportByDate(LocalDateTime day) {

        // 转换为时间戳（毫秒）
        long timestampMillis = day.toInstant(ZoneOffset.UTC).toEpochMilli();

        int page = 1;
        int len = 0;
        long[] dates = {timestampMillis, timestampMillis};

        List<MaterialCostReport> all = new ArrayList<>();
        do {

            List<MaterialCostReport> has = this.yiDaSession.searchFormDataConditionsRequest(MaterialCostReport.class, null, page);

            all.addAll(has);

            len = has.size();

            page++;

        } while (len == 100);

        return all;
    }

    private <T> List<List<T>> splitCollection(Collection<T> collection, int size) {
        List<List<T>> result = new ArrayList<>();
        List<T> currentList = new ArrayList<>(size);

        for (T element : collection) {
            currentList.add(element);
            if (currentList.size() == size) {
                result.add(new ArrayList<>(currentList));
                currentList.clear();
            }
        }

        // 如果最后的子集合不为空，将其加入结果列表
        if (!currentList.isEmpty()) {
            result.add(new ArrayList<>(currentList));
        }

        return result;
    }

}
