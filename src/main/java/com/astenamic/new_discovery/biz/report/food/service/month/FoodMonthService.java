package com.astenamic.new_discovery.biz.report.food.service.month;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.report.food.entity.month.FoodInfo;
import com.astenamic.new_discovery.biz.report.food.entity.month.FoodMonth;
import com.astenamic.new_discovery.biz.report.food.entity.month.FoodMonthResponse;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthRowNumber;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.datre.api.session.DatreSession;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class FoodMonthService extends DatreSession {

    private final String resource = "/dolinkdata/api/foodMonthData";
    private final YiDaSession yiDaSession;
    private final ShopRelationAppService shopRelationAppService;

    public List<FoodMonthResponse> getFoodMonthData(LocalDateTime target, String shopNo) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String yearMonth = target.format(formatter);

        JSONObject jsonObject = JSONObject.of(
                "shopNo", shopNo,
                "yearMonth", yearMonth
        );

        return this.postListRequest(resource, jsonObject, FoodMonthResponse.class);
    }

    public void saveFoodMonthData(List<MonthRowNumber> shops, LocalDateTime day) {
        List<FoodMonth> oldData = getOldData(day);

        List<ShopRelation> relations = this.shopRelationAppService.getRelationsByClosed("否");

        List<FoodMonth> saveData = new ArrayList<>();
        List<FoodMonth> updateData = new ArrayList<>();

        List<FoodMonth> newData = new ArrayList<>();
        for (MonthRowNumber shop : shops) {
            FoodMonth foodMonth = new FoodMonth();
            BeanUtils.copyProperties(shop, foodMonth);
            foodMonth.setYearMonth(shop.getDate());
            String shopNo = shop.getShopNo();
            List<FoodMonthResponse> foodMonthData = getFoodMonthData(day, shopNo);

            List<FoodInfo> foods = new ArrayList<>();
            for (FoodMonthResponse foodMonthDatum : foodMonthData) {
                FoodInfo foodInfo = new FoodInfo();
                BeanUtils.copyProperties(foodMonthDatum, foodInfo);
                if (foodInfo.getLlCostRate() != null) {
                    foodInfo.setLlCostRate(foodInfo.getLlCostRate() * 100);
                    foodInfo.setSjCostRate(foodInfo.getSjCostRate() * 100);
                }
                foods.add(foodInfo);
            }
            foodMonth.setFoods(foods);
            foodMonth.setObjectId(null);
            newData.add(foodMonth);
        }

        newData.stream().peek(newDatum -> {
                    for (ShopRelation relation : relations) {
                        if (StringUtils.equals(relation.getShopId(), newDatum.getShopId())) {
                            newDatum.setShopSysId(getListValue(relation.getShopSysId()));
                            newDatum.setAreaSysId(getListValue(relation.getAreaSysId()));
                            newDatum.setBrandSysId(getListValue(relation.getBrandSysId()));
                            break;
                        }
                    }
                    for (FoodMonth oldDatum : oldData) {
                        if (StringUtils.equals(oldDatum.getShopId(), newDatum.getShopId())) {
                            newDatum.setObjectId(oldDatum.getObjectId());
                            break;
                        }
                    }
                }).filter(n -> n.getShopSysId() != null && !n.getShopSysId().isEmpty())
                .forEach(newDatum -> {
                    if (newDatum.getObjectId() == null) {
                        saveData.add(newDatum);
                    } else {
                        updateData.add(newDatum);
                    }
                });

        if (!updateData.isEmpty()) {
            List<List<FoodMonth>> groups = ArrayUtils.splitCollection(updateData, 1);
            for (List<FoodMonth> group : groups) {
                this.yiDaSession.batchUpdateDataByObjectId(group, FoodMonth.class);
            }
        }

        if (!saveData.isEmpty()) {
            for (FoodMonth foodMonth : saveData) {
                this.yiDaSession.saveForm(foodMonth, FoodMonth.class);
            }
        }
    }

    public List<FoodMonth> getOldData(LocalDateTime day) {
        day = day.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

        long[] time = TimeUtils.getDayRange(day);

        SearchConditions conn = SearchCondition.builder().dateBetween("dateField_m5otglj3", time, "+").get();

        List<FoodMonth> oldData = new ArrayList<>();
        int size = 0;
        int page = 1;
        do {
            List<FoodMonth> od = this.yiDaSession.searchFormDataConditionsRequest(FoodMonth.class, conn, page);
            size = od.size();
            page++;
            oldData.addAll(od);
        } while (size == 100);

        return oldData;

    }


    private String getListValue(List<String> list) {
        return Optional.ofNullable(list)
                .filter(vs -> !vs.isEmpty())
                .map(vs -> vs.get(0))
                .orElse("");
    }

}
