package com.astenamic.new_discovery.biz.report.shop.service;

import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.report.shop.entity.base.RowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DaySmartWarning;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.week.WeekRowNumber;
import com.astenamic.new_discovery.biz.report.shop.repository.base.RowNumberRepository;
import com.astenamic.new_discovery.biz.report.shop.repository.base.SmartWarningRepository;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@AllArgsConstructor
public class RowNumberService {
    private final RowNumberRepository<MonthRowNumber> monthRepository;
    private final ShopRelationAppService shopRelationAppService;
    private final RowNumberRepository<QuarterRowNumber> quarterRepository;
    private final RowNumberRepository<DayRowNumber> dayRepository;
    private final RowNumberRepository<WeekRowNumber> weekRepository;

    private final SmartWarningRepository<DaySmartWarning> daySmartWarningRepository;

    public List<MonthRowNumber> month(LocalDateTime targetTime) {

        Time monthTime = Time.month(targetTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime localDateTime = targetTime.withDayOfMonth(1);

        List<MonthRowNumber> data = this.monthRepository.findByTime(
                monthTime.getCurrent(),
                monthTime.getLast(),
                monthTime.getSamePeriodOfYear(),
                monthTime.getMonth(),
                monthTime.getFirstDayOf(),
                localDateTime.format(formatter)

        );

        return this.relateDailyReport(data);
    }

    public List<QuarterRowNumber> quarter(LocalDateTime targetTime) {

        Time quarterTime = Time.quarter(targetTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime localDateTime = targetTime.withDayOfMonth(1);

        List<QuarterRowNumber> data = this.quarterRepository.findByTime(
                quarterTime.getCurrent(),
                quarterTime.getLast(),
                quarterTime.getSamePeriodOfYear(),
                quarterTime.getMonth(),
                quarterTime.getFirstDayOf(),
                localDateTime.format(formatter)

        );

        return this.relateDailyReport(data);
    }

    public List<DaySmartWarning> daySmartWarning(LocalDateTime yesterday) {
        return this.daySmartWarningRepository.findByTime(yesterday.toLocalDate());
    }

    public List<DayRowNumber> day(LocalDateTime targetTime) {

        Time dayTime = Time.day(targetTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime localDateTime = targetTime.withDayOfMonth(1);

        List<DayRowNumber> data = this.dayRepository.findByTime(
                dayTime.getCurrent(),
                dayTime.getLast(),
                dayTime.getSamePeriodOfYear(),
                dayTime.getMonth(),
                dayTime.getFirstDayOf(),
                localDateTime.format(formatter)
        );

        return this.relateDailyReport(data);
    }

    public List<WeekRowNumber> week(LocalDateTime targetTime) {

        Time weekTime = Time.week(targetTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime localDateTime = targetTime.withDayOfMonth(1);

        List<WeekRowNumber> data = this.weekRepository.findByTime(
                weekTime.getCurrent(),
                weekTime.getLast(),
                weekTime.getSamePeriodOfYear(),
                weekTime.getMonth(),
                weekTime.getFirstDayOf(),
                localDateTime.format(formatter)
        );

        return this.relateDailyReport(data);
    }

    public <T extends RowNumber> List<T> relateDailyReport(List<T> shopReports) {

        if (shopReports == null) {
            return null;
        }

        List<ShopRelation> relations = this.shopRelationAppService.getRelationsByClosed("否");

        shopReports.forEach(report -> {

            for (ShopRelation relation : relations) {
                if (Objects.equals(report.getShopId(), relation.getShopId())) {
                    report.setBrandSysId(getListValue(relation.getBrandSysId()));
                    report.setBatAreaSysId(getListValue(relation.getBatAreaSysId()));
                    report.setBatAreaPeopleSysId(getListValue(relation.getBatAreaPeopleSysId()));
                    report.setAreaSysId(getListValue(relation.getAreaSysId()));
                    report.setSupervisorSysId(getListValue(relation.getSupervisorSysId()));
                    report.setShopSysId(getListValue(relation.getShopSysId()));
                    report.setShopManagerSysId(getListValue(relation.getShopManagerSysId()));
                    report.setChiefSysId(getListValue(relation.getChiefSysId()));
                    report.setGroupId(relation.getShopGroupId());
                    report.setAccountantSysId(getListValue(relation.getAccountant()));
                    break;
                }
            }
        });

        return shopReports.stream().filter(f -> f.getShopSysId() != null).toList();
    }

    public String getListValue(List<String> list) {
        return Optional.ofNullable(list)
                .filter(vs -> !vs.isEmpty())
                .map(vs -> vs.get(0))
                .orElse("");
    }

}
