package com.astenamic.new_discovery.biz.report.divisionDetail.service;

import com.astenamic.new_discovery.biz.report.divisionDetail.entity.DivisionDetail;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class DivisionDetailService {

    private final YiDaSession yiDaSession;

    public List<DivisionDetail> getDivisionDetailByYiDa() {
        int page = 1;
        int size = 0;

        List<DivisionDetail> all = new ArrayList<>();
        do {
            List<DivisionDetail> od = this.yiDaSession.searchFormDataConditionsEmbedded(DivisionDetail.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
