package com.astenamic.new_discovery.biz.report.pricing.service;

import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.prd.entity.Prd;
import com.astenamic.new_discovery.ace.scm.shop.entity.Shop;
import com.astenamic.new_discovery.ace.scm.shop.service.ShopService;
import com.astenamic.new_discovery.ace.scm.supplier.entity.SaveSupplier;
import com.astenamic.new_discovery.ace.scm.supplier.entity.Supplier;
import com.astenamic.new_discovery.ace.scm.supplier.service.SupplierService;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity.AddSupplierItem;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity.AddSupplierPricingSheet;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity.SupplierPricingSheetResponse;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.service.SupplierPricingSheetService;
import com.astenamic.new_discovery.biz.report.pricing.entity.*;
import com.astenamic.new_discovery.biz.report.pricing.entity.data_base.YiDaBase;
import com.astenamic.new_discovery.biz.report.pricing.repository.YiDaBaseRepository;
import com.astenamic.new_discovery.biz.scheduler.Scheduler;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class PricingService extends AceSession {

    private static final Logger logger = LoggerFactory.getLogger(Scheduler.class);

    private YiDaSession yiDaSession;
    private SupplierPricingSheetService supplierPricingSheetService;
    private ShopService shopService;
    private SupplierService supplierService;
    private YiDaBaseRepository yiDaBaseRepository;

    public List<YiDaBase> saveYiDaBase(LocalDateTime targetTime, Integer start, Integer limit) {

        targetTime = targetTime.withHour(0).withMinute(0).withSecond(0).withNano(0);

        // 接口获取的数据
        List<SupplierPricingSheetResponse> newDataS = this.supplierPricingSheetService.getSupplierPricingSheetList(start, limit, targetTime);

        List<YiDaBase> list = new ArrayList<>();

        newDataS.forEach(s -> {
            if(!StringUtils.equals(s.getStatus(), "-1")){
                s.getItems().forEach(item -> {
                    item.setCtime(s.getCtime());
                    item.setAtime(s.getAtime());
                    list.add(item);
                });
            }
        });

        return yiDaBaseRepository.saveAll(list);
    }

    public void syncToYiDa(LocalDateTime targetTime, String CprocessCode, String FprocessCode) {

        LocalDateTime start = targetTime.plusDays(25)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        LocalDateTime end = targetTime.plusDays(35)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        // 这些是还有一个月过期的物料，放大访问区间
        List<YiDaBase> todayData = yiDaBaseRepository.getTodayData(start, end);

        List<Shop> shopList = this.shopService.getShopList();
        for (YiDaBase one : todayData) {
            String[] split = one.getShop_id().split(",");
            List<String> shopNames = new ArrayList<>();
            for (Shop shop : shopList) {
                // 每一个shopid
                for (String s : split) {
                    if (s.equals(shop.getId().toString())) {
                        shopNames.add(shop.getName() + "-" + shop.getSno());
                    }
                }
            }
            one.setShopNames(shopNames);
        }


        int page = 1;
        int size = 0;

        // 从宜搭获取数据
        List<Prd> prds = new ArrayList<>();
        do {
            List<Prd> od = this.yiDaSession.searchFormDataConditionsRequest(Prd.class, null, page);
            size = od.size();
            page++;
            prds.addAll(od);
        } while (size == 100);

        // 生鲜列表
        List<Fresh> freshList = new ArrayList<>();
        // 采集列表
        List<Collect> collectList = new ArrayList<>();

        // 进行货物基础表的类型对比
        for (YiDaBase one : todayData) {
            for (Prd prd : prds) {
                if (StringUtils.equals(one.getLgid().toString(), prd.getId().toString())) {
                    if (StringUtils.equals(prd.getSupplierType(), "生鲜")) {
                        Fresh fresh = new Fresh();
                        BeanUtils.copyProperties(one, fresh);
                        if (StringUtils.isNotBlank(fresh.getMomratio())) {
                            fresh.setMomratio(fresh.getMomratio().split("%")[0]);
                        }
                        freshList.add(fresh);
                    }
                    if (StringUtils.equals(prd.getSupplierType(), "集采")) {
                        Collect collect = new Collect();
                        BeanUtils.copyProperties(one, collect);
                        if (StringUtils.isNotBlank(collect.getMomratio())) {
                            collect.setMomratio(collect.getMomratio().split("%")[0]);
                        }
                        collectList.add(collect);
                    }
                }
            }
        }


        List<Supplier> supplierList = supplierService.getSupplierList();
        for (Fresh fresh : freshList) {
            for (Supplier supplier : supplierList) {
                if (fresh.getSupplier_sno().equals(supplier.getSno())) {
                    List<SaveSupplier> save = new ArrayList<>();
                    SaveSupplier saveSupplier = new SaveSupplier();
                    saveSupplier.setAppType("APP_VAWCFBK8UUNBTJINOCWQ");
                    saveSupplier.setFormUuid("FORM-88028C54935741CC89CF1A73C9B621D7LJAM");
                    saveSupplier.setFormType("receipt");
                    saveSupplier.setInstanceId(supplier.getObjectId());
                    saveSupplier.setTitle(supplier.getName());
                    saveSupplier.setSubTitle("");
                    save.add(saveSupplier);
                    fresh.setSuppliers(save);
                    fresh.setSupplier_id(supplier.getLspid().toString());
                    fresh.setBselfpur(supplier.getBselfpur());
                    fresh.setAccountdatetype(supplier.getAccountdatetype());
                    fresh.setInvoicetype(supplier.getInvoicetype());
                    fresh.setPhoneNumber(supplier.getPhoneNumber());
                    fresh.setSupplier_name(supplier.getName());
                    break;
                }
            }
        }

        for (Collect collect : collectList) {
            for (Supplier supplier : supplierList) {
                if (collect.getSupplier_sno().equals(supplier.getSno())) {
                    List<SaveSupplier> save = new ArrayList<>();
                    SaveSupplier saveSupplier = new SaveSupplier();
                    saveSupplier.setAppType("APP_VAWCFBK8UUNBTJINOCWQ");
                    saveSupplier.setFormUuid("FORM-91297B051E664218913F01680ED0917CVME9");
                    saveSupplier.setFormType("receipt");
                    saveSupplier.setInstanceId(supplier.getObjectId());
                    saveSupplier.setTitle(supplier.getName());
                    saveSupplier.setSubTitle("");
                    save.add(saveSupplier);
                    collect.setSuppliers(save);
                    collect.setSupplier_id(supplier.getLspid().toString());
                    collect.setBselfpur(supplier.getBselfpur());
                    collect.setAccountdatetype(supplier.getAccountdatetype());
                    collect.setInvoicetype(supplier.getInvoicetype());
                    collect.setPhoneNumber(supplier.getPhoneNumber());
                    collect.setSupplier_name(supplier.getName());
                    break;
                }
            }
        }

        try{
            if (!collectList.isEmpty()) {
                CollectForm collectForm = new CollectForm();
                collectForm.setItems(collectList);
                collectForm.setDate(targetTime);
                this.yiDaSession.processSave(collectForm, CollectForm.class, CprocessCode);
            }

            if (!freshList.isEmpty()) {
                FreshForm freshForm = new FreshForm();
                freshForm.setDate(targetTime);
                freshForm.setFreshs(freshList);
                this.yiDaSession.processSave(freshForm, FreshForm.class, FprocessCode);
            }
        }catch (Exception e) {
            throw new IllegalArgumentException(e);
        }

        if (collectList.isEmpty() && freshList.isEmpty()) {
            System.out.println("没有可更新的数据");
        }

        yiDaBaseRepository.deleteAll(todayData);
    }

    public String addSupplierPricingSheet(LocalDateTime targetTime) {
        StringBuilder response = new StringBuilder();

        targetTime = targetTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        long ta = targetTime.toInstant(ZoneOffset.UTC).toEpochMilli();

        long[] time = {ta, ta + 1000 * 60 * 60 * 24 - 1};

        // 从宜搭获取集采数据
        int Csize = 0;
        int Cpage = 1;

        List<GetPricingFromYida> collectForms = new ArrayList<>();
        SearchConditions cond = SearchCondition
                .builder()
                .dateBetween("createTime", time, "+")
                .get();
        do {
            List<GetPricingFromYida> p = this.yiDaSession.searchFormDataConditionsRequest(GetPricingFromYida.class, cond, Cpage);
            Csize = p.size();
            Cpage++;
            collectForms.addAll(p);
        } while (Csize == 100);

        for (GetPricingFromYida collectForm : collectForms) {
            AddSupplierPricingSheet addSupplierPricingSheet = new AddSupplierPricingSheet();
            BeanUtils.copyProperties(collectForm, addSupplierPricingSheet);
            if (collectForm.getShops().isEmpty()) {
                continue;
            }

            StringBuilder sb = new StringBuilder();
            for (String shop : collectForm.getShops()) {
                if (shop.contains("-")) {
                    String[] split = shop.split("-");
                    sb.append(split[split.length - 1]);
                    sb.append(",");
                }
            }
            sb.deleteCharAt(sb.length() - 1);

            addSupplierPricingSheet.setShop_sno(sb.toString());

            addSupplierPricingSheet.setAuto_audit(0);
            addSupplierPricingSheet.setCuser_sno(String.valueOf(72));
            addSupplierPricingSheet.setAuser_sno(String.valueOf(72));
            List<AddSupplierItem> addSupplierItems = new ArrayList<>();
            AddSupplierItem addSupplierItem = new AddSupplierItem();
            BeanUtils.copyProperties(collectForm, addSupplierItem);
            addSupplierItems.add(addSupplierItem);
            addSupplierPricingSheet.setItems(addSupplierItems);
            String message = supplierPricingSheetService.addSupplierPricingSheet(addSupplierPricingSheet);
            response.append("货品编码:" + addSupplierItem.getGood_sno() + "\n"
                    + "运行结果:" + message + "\n"
                    + "运行时间:" + LocalDateTime.now() + "\n");
        }

        logger.info(response.toString());
        return response.toString();
    }

    public List<CollectForm> getData() {
        int page = 1;
        int size = 0;

        List<CollectForm> all = new ArrayList<>();
        do {
            List<CollectForm> form = this.yiDaSession.searchFormDataConditionsEmbedded(CollectForm.class, null, page);
            page++;
            size = form.size();
            all.addAll(form);
        } while (size == 1);

        return all;
    }
}

