package com.astenamic.new_discovery.biz.report.material.entity;

import com.astenamic.new_discovery.datre.report.material.entity.MaterialReport;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.YidaObject;
import jakarta.persistence.Transient;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity("FORM-2594A46DA25740D48E9FF638DED4431DNVEY")
public class MaterialShopReport extends YidaObject {




    @FormField("textField_m4qf9iqi")
    private String shopId;

    // 门店
    @FormField("departmentSelectField_m4qd49xe")
    @Transient
    private List<String> shopSysId;

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    // 厨师长
    @FormField("employeeField_m4s33lup")
    @Transient
    private List<String> chiefSysId;

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }

    // 日期
    @FormField("dateField_m4qf9iqh")
    private LocalDateTime date;

    // 	实际成本
    @FormField("tableField_m3xv5p86")
    private List<MaterialReport> materialReports;

    public MaterialShopReport(String shopId, LocalDateTime date) {
        this.shopId = shopId;
        this.date = date;
    }
}
