package com.astenamic.new_discovery.biz.report.shop.entity.base;

import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;


@Data
@MappedSuperclass
public abstract class Member extends RangeData {

    @Id // 必须定义主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 门店ID
    @Column(name = "shop_id")
    private String shopId;

    // 券拉动金额
    @Column(name = "member_drive_funds")
    @FormField("numberField_m3o3hk3c")
    private Float memberDriveFunds;

}
