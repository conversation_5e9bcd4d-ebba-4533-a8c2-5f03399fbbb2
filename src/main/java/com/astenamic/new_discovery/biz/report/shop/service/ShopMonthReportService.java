package com.astenamic.new_discovery.biz.report.shop.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.biz.business.application.service.MaterialShopReportAppService;
import com.astenamic.new_discovery.biz.report.food.service.month.FoodMonthService;
import com.astenamic.new_discovery.biz.business.domain.entity.MaterialShopReport;
import com.astenamic.new_discovery.biz.report.shop.entity.RangeData;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DayRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.day.DaySmartWarning;
import com.astenamic.new_discovery.biz.report.shop.entity.dayGold.DayGoal;
import com.astenamic.new_discovery.biz.report.shop.entity.dayGold.DayGoalReport;
import com.astenamic.new_discovery.biz.report.shop.entity.month.MonthRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.quarter.QuarterRowNumber;
import com.astenamic.new_discovery.biz.report.shop.entity.week.WeekRowNumber;
import com.astenamic.new_discovery.biz.report.shop.service.base.RangeDataService;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@AllArgsConstructor
public class ShopMonthReportService {
    private final YiDaSession yiDaSession;
    private final RowNumberService rowNumberService;
    private final List<RangeDataService> rangeDataServices;
    private final MaterialShopReportAppService materialShopReportAppService;
    private final FoodMonthService foodMonthService;
    private final FormManager formManager;

    private static final Logger logger = LoggerFactory.getLogger(ShopMonthReportService.class);

    public Object month(LocalDateTime targetMonth, Integer flag) {
        // 日
        if (flag == 1) {
            targetMonth = targetMonth.withHour(0).withMinute(0).withSecond(0).withNano(0);

            long[] time = TimeUtils.getDayRange(targetMonth);

            SearchConditions cond = SearchCondition
                    .builder()
                    .dateBetween("dateField_m3weopav", time, "+")
                    .get();

            List<DayRowNumber> oldData = getOldData(DayRowNumber.class, cond);

            List<DayRowNumber> newData = this.rowNumberService.day(targetMonth);

            if (oldData != null && !oldData.isEmpty()) {
                for (DayRowNumber oldDatum : oldData) {
                    for (DayRowNumber newDatum : newData) {
                        if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                            newDatum.setObjectId(oldDatum.getObjectId());
                            break;
                        }
                    }
                }
            }

            List<DaySmartWarning> daySmartWarnings = this.rowNumberService.daySmartWarning(targetMonth);

            if (daySmartWarnings != null && !daySmartWarnings.isEmpty()) {
                for (DayRowNumber newDatum : newData) {
                    for (DaySmartWarning daySmartWarning : daySmartWarnings) {
                        if (StringUtils.equals(newDatum.getShopId(), daySmartWarning.getShopId())) {
                            newDatum.setSmartWarning(daySmartWarning.getSmartWarning());
                            newDatum.setPredictAmount(daySmartWarning.getPredictAmount());
                            newDatum.setPredictDeviation(daySmartWarning.getPredictDeviation());
                            newDatum.setInitPredictAmount(daySmartWarning.getInitPredictAmount());
                            break;
                        }
                    }
                }
            }

            List<DayGoalReport> dayGoal = getDayGoal(targetMonth);
            for (DayRowNumber newDatum : newData) {
                for (DayGoalReport dayGoalReport : dayGoal) {
                    if (StringUtils.equals(newDatum.getShopId(), dayGoalReport.getShopId())) {
                        for (DayGoal goal : dayGoalReport.getDayGoals()) {
                            if (newDatum.getDate().equals(goal.getDate())) {
                                newDatum.setDayBudgetRevenue(goal.getDayRevenue());
                                newDatum.setDayBudgetDineInRevenue(goal.getDayDineInRevenue());
                                newDatum.setDayBudgetTakeOutAcuRevenue(goal.getDayTakeOutRevenue());
                                break;
                            }
                        }
                        break;
                    }
                }
            }

            Map<Integer, List<DayRowNumber>> data = newData
                    .stream()
                    .collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getObjectId()) ? 1 : 2));

            List<DayRowNumber> total = new ArrayList<>();
            List<DayRowNumber> update = new ArrayList<>();

            Integer k;
            List<DayRowNumber> rows;

            for (Map.Entry<Integer, List<DayRowNumber>> en : data.entrySet()) {
                k = en.getKey();
                rows = en.getValue();

                List<List<DayRowNumber>> groups = ArrayUtils.splitCollection(rows, 1);

                for (List<DayRowNumber> group : groups) {

                    List<JSONObject> jsonObjects = this.formManager.serialize(group, DayRowNumber.class).toJsonObjectList();

                    for (RangeDataService rangeDataService : rangeDataServices) {

                        List<? extends RangeData> dayData = rangeDataService.day(group, targetMonth);

                        List<JSONObject> ss = this.formManager.serialize((List<RangeData>) dayData, (Class<RangeData>) rangeDataService.getClaz()).toJsonObjectList();

                        if (!ss.isEmpty()) {
                            JSONObject o = ss.get(0);
                            if (!o.isEmpty()) {
                                jsonObjects.get(0).putAll(o);
                            }
                        }
                    }

                    if (k == 1) {
                        this.yiDaSession.batchSaveJson(jsonObjects, DayRowNumber.class);
                        total.addAll(group);
                    } else {
                        this.yiDaSession.batchUpdateDataByObjectId(group, DayRowNumber.class);
                        total.addAll(group);
                        update.addAll(group);
                    }
                }

            }

            for (RangeDataService rangeDataService : rangeDataServices) {
                rangeDataService.dayUpdate(update, targetMonth);
            }


            this.saveMaterialDaily(targetMonth);
            return total;
        }
        // 月
        if (flag == 2) {

            targetMonth = targetMonth.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

            long[] time = TimeUtils.getMonthRange(targetMonth);

            SearchConditions cond = SearchCondition
                    .builder()
                    .dateBetween("dateField_m3weopav", time, "+")
                    .get();

            List<MonthRowNumber> oldData = getOldData(MonthRowNumber.class, cond);
            List<MonthRowNumber> newData = this.rowNumberService.month(targetMonth);

            if (oldData != null && !oldData.isEmpty()) {
                for (MonthRowNumber oldDatum : oldData) {
                    for (MonthRowNumber newDatum : newData) {
                        if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                            newDatum.setObjectId(oldDatum.getObjectId());
                            break;
                        }
                    }
                }
            }

            Map<Integer, List<MonthRowNumber>> data = newData.stream().collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getObjectId()) ? 1 : 2));

            List<MonthRowNumber> total = new ArrayList<>();

            Integer k;
            List<MonthRowNumber> rows;

            for (Map.Entry<Integer, List<MonthRowNumber>> en : data.entrySet()) {
                k = en.getKey();
                rows = en.getValue();

                List<List<MonthRowNumber>> groups = ArrayUtils.splitCollection(rows, 1);

                for (List<MonthRowNumber> group : groups) {
                    if (k == 1) {
                        this.yiDaSession.batchSave(group, MonthRowNumber.class);
                        total.addAll(group);
                    } else {
                        this.yiDaSession.batchUpdateDataByObjectId(group, MonthRowNumber.class);
                        total.addAll(group);
                    }
                }

            }

            for (RangeDataService rangeDataService : rangeDataServices) {
                rangeDataService.month(total, targetMonth);
            }

            this.foodMonthService.saveFoodMonthData(total, targetMonth);
            return total;
        }
        // 季度
        if (flag == 3) {
            String firstDayOf = Time.quarter(targetMonth).getFirstDayOf();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDateTime parse = LocalDate.parse(firstDayOf, formatter).atStartOfDay();

            parse = parse.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

            long[] time = TimeUtils.getDayRange(parse);

            SearchConditions cond = SearchCondition
                    .builder()
                    .dateBetween("dateField_m3weopav", time, "+")
                    .get();

            List<QuarterRowNumber> oldData = getOldData(QuarterRowNumber.class, cond);

            List<QuarterRowNumber> newData = this.rowNumberService.quarter(targetMonth);
            int monthValue = targetMonth.getMonthValue();
            String quarters = ((monthValue - 1) / 3 + 1) + "";
            for (QuarterRowNumber newDatum : newData) {
                newDatum.setQuarter(quarters);
            }

            if (oldData != null && !oldData.isEmpty()) {
                for (QuarterRowNumber oldDatum : oldData) {
                    for (QuarterRowNumber newDatum : newData) {
                        if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                            newDatum.setObjectId(oldDatum.getObjectId());
                            break;
                        }
                    }
                }
            }

            Map<Integer, List<QuarterRowNumber>> data = newData.stream().collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getObjectId()) ? 1 : 2));

            List<QuarterRowNumber> total = new ArrayList<>();

            Integer k;
            List<QuarterRowNumber> rows;

            for (Map.Entry<Integer, List<QuarterRowNumber>> en : data.entrySet()) {
                k = en.getKey();
                rows = en.getValue();

                List<List<QuarterRowNumber>> groups = ArrayUtils.splitCollection(rows, 1);

                for (List<QuarterRowNumber> group : groups) {
                    if (k == 1) {
                        this.yiDaSession.batchSave(group, QuarterRowNumber.class);
                        total.addAll(group);
                    } else {
                        this.yiDaSession.batchUpdateDataByObjectId(group, QuarterRowNumber.class);
                        total.addAll(group);
                    }
                }

            }

            for (RangeDataService rangeDataService : rangeDataServices) {
                rangeDataService.quarter(total, targetMonth);
            }

            return total;
        }

        // 周
        if (flag == 4) {

            logger.info("周报表数据同步开始");
            ZoneId zone = ZoneId.of("Asia/Shanghai");
            ZonedDateTime firstDayOfWeek = targetMonth
                    .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                    .atZone(zone)
                    .with(LocalTime.MIN);
            long start = firstDayOfWeek.toInstant().toEpochMilli();
            long end = firstDayOfWeek.plusDays(7).toInstant().toEpochMilli() - 1;

            long[] time = TimeUtils.getWeekRange(targetMonth);

            SearchConditions cond = SearchCondition
                    .builder()
                    .dateBetween("dateField_m3weopav", time, "+")
                    .get();

            List<WeekRowNumber> oldData = this.getOldData(WeekRowNumber.class, cond);

            List<WeekRowNumber> newData = this.rowNumberService.week(targetMonth);

            LocalDateTime startOfWeek = Instant.ofEpochMilli(start)
                    .atZone(zone)
                    .toLocalDateTime();

            LocalDateTime endOfWeek = Instant.ofEpochMilli(end)
                    .atZone(zone)
                    .toLocalDateTime();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            String weekRange = formatter.format(startOfWeek) + " ~ " + formatter.format(endOfWeek);

            for (WeekRowNumber newDatum : newData) {
                newDatum.setWeek(weekRange);
            }

            if (oldData != null && !oldData.isEmpty()) {
                for (WeekRowNumber oldDatum : oldData) {
                    for (WeekRowNumber newDatum : newData) {
                        if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                            newDatum.setObjectId(oldDatum.getObjectId());
                            break;
                        }
                    }
                }
            }

            Map<String, List<DayGoalReport>> weekGoal = getWeekGoal(targetMonth);

            newData.forEach(newDatum ->
                    Optional.ofNullable(weekGoal.get(newDatum.getShopId()))
                            .ifPresent(reports -> {
                                float[] sums = reports.stream()
                                        .filter(report -> report.getDayGoals() != null)
                                        .flatMap(report -> report.getDayGoals().stream())
                                        .reduce(
                                                new float[3],
                                                (acc, goal) -> {
                                                    acc[0] += Optional.ofNullable(goal.getDayRevenue()).orElse(0f);
                                                    acc[1] += Optional.ofNullable(goal.getDayDineInRevenue()).orElse(0f);
                                                    acc[2] += Optional.ofNullable(goal.getDayTakeOutRevenue()).orElse(0f);
                                                    return acc;
                                                },
                                                (a, b) -> new float[]{a[0] + b[0], a[1] + b[1], a[2] + b[2]}
                                        );
                                newDatum.setWeekBudgetRevenue(sums[0]);
                                newDatum.setWeekBudgetDineInRevenue(sums[1]);
                                newDatum.setWeekBudgetTakeOutRevenue(sums[2]);
                            })
            );

            Map<Integer, List<WeekRowNumber>> data = newData.stream().collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getObjectId()) ? 1 : 2));
            List<WeekRowNumber> total = new ArrayList<>();
            List<WeekRowNumber> update = new ArrayList<>();
            for (Map.Entry<Integer, List<WeekRowNumber>> en : data.entrySet()) {
                Integer k = en.getKey();
                List<WeekRowNumber> rows = en.getValue();
                List<List<WeekRowNumber>> groups = ArrayUtils.splitCollection(rows, 1);
                for (List<WeekRowNumber> group : groups) {
                    List<JSONObject> jsonObjects = this.formManager.serialize(group, WeekRowNumber.class).toJsonObjectList();
                    for (RangeDataService rangeDataService : rangeDataServices) {
                        if (rangeDataService instanceof TakeawayPlatformService) {
                            List<? extends RangeData> dayData = rangeDataService.week(group, targetMonth);
                            List<JSONObject> ss = this.formManager.serialize((List<RangeData>) dayData, (Class<RangeData>) rangeDataService.getClaz()).toJsonObjectList();
                            if (!ss.isEmpty()) {
                                JSONObject o = ss.get(0);
                                if (!o.isEmpty()) {
                                    jsonObjects.get(0).putAll(o);
                                }
                            }
                        }
                    }
                    if (k == 1) {
                        this.yiDaSession.batchSaveJson(jsonObjects, WeekRowNumber.class);
                        total.addAll(group);
                    } else {
                        this.yiDaSession.batchUpdateDataByObjectId(group, WeekRowNumber.class);
                        total.addAll(group);
                        update.addAll(group);
                    }
                }
            }

            for (RangeDataService rangeDataService : rangeDataServices) {
                if (rangeDataService instanceof TakeawayPlatformService) {
                    rangeDataService.weekUpdate(update, targetMonth);
                }
            }

            return total;


        }
        throw new IllegalArgumentException("Invalid flag: " + flag);
    }

    public <T extends RangeData> List<T> getOldData(Class<T> claz, SearchConditions cond) {
        String reportType;
        try {
            if (claz.getSimpleName().contains("Day")) {
                reportType = "日";
            } else if (claz.getSimpleName().contains("Week")) {
                reportType = "周";
            } else if (claz.getSimpleName().contains("Month")) {
                reportType = "月";
            } else if (claz.getSimpleName().contains("Quarter")) {
                reportType = "季度";
            } else {
                reportType = claz.getSimpleName();
            }
            logger.info("开始获取宜搭{}报表数据，{}", reportType, cond.getConditionsJson());
        } catch (Exception e) {
            reportType = claz.getSimpleName();
            logger.error("打印日志失败！", e);
        }
        int page = 1;
        int size = 0;
        List<T> oldData = new ArrayList<>();
        do {
            List<T> od = new ArrayList<>();
            try {
                od = this.yiDaSession.searchFormDataConditionsRequest(claz, cond, page);
            } catch (Exception e) {
                logger.error("获取宜搭{}报表数据失败，{}{}", reportType, e.getMessage(), System.lineSeparator(), e);
                throw e;
            }
            page++;
            size = od.size();
            oldData.addAll(od);
        } while (size == 100);
        logger.info("获取宜搭{}报表数据完成，{}", reportType, oldData.size());
        return oldData;
    }

    public void saveMaterialDaily(LocalDateTime day) {
        this.materialShopReportAppService.syncMaterialDaily(day);
    }


    public List<DayGoalReport> getDayGoal(LocalDateTime day) {
        day = day.withHour(0).withMinute(0).withSecond(0).withNano(0);

        SearchConditions conditions = SearchCondition.builder()
                .textEq("numberField_m5mb0oec", String.valueOf(day.getYear()), "+")
                .textEq("numberField_m5mb0oed", String.valueOf(day.getMonthValue()), "+")
                .get();

        return getGoalReports(conditions);
    }

    public Map<String, List<DayGoalReport>> getWeekGoal(LocalDateTime day) {
        ZoneId zone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime firstDayOfWeek = day
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                .atZone(zone)
                .with(LocalTime.MIN);
        // 获取周内所有日期
        List<ZonedDateTime> weekDates = IntStream.range(0, 7)
                .mapToObj(firstDayOfWeek::plusDays)
                .toList();
        // 提取不同月份
        Map<String, Integer[]> monthYearMap = new HashMap<>();
        weekDates.forEach(date -> {
            String monthKey = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            if (!monthYearMap.containsKey(monthKey)) {
                monthYearMap.put(monthKey, new Integer[]{date.getYear(), date.getMonthValue()});
            }
        });

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Set<String> weekDateStrings = weekDates.stream()
                .map(date -> date.format(dateFormatter))
                .collect(Collectors.toSet());
        logger.info("获取周目标，{}", weekDateStrings);
        logger.info("获取周目标月份，{}", JSON.toJSONString(monthYearMap));
        // 为每个月份获取目标数据
        List<DayGoalReport> allReports = new ArrayList<>();
        for (Map.Entry<String, Integer[]> entry : monthYearMap.entrySet()) {
            Integer[] yearMonth = entry.getValue();
            logger.info("开始获取宜搭数据,{}-{}", yearMonth[0], yearMonth[1]);
            SearchConditions conditions = SearchCondition.builder()
                    .textEq("numberField_m5mb0oec", String.valueOf(yearMonth[0]), "+")
                    .textEq("numberField_m5mb0oed", String.valueOf(yearMonth[1]), "+")
                    .get();
            List<DayGoalReport> reports = getGoalReports(conditions);
            logger.info("获取宜搭数据完成,{}-{},{}", yearMonth[0], yearMonth[1], reports.size());
            reports.forEach(report -> {
                if (report.getDayGoals() != null) {
                    List<DayGoal> filteredGoals = report.getDayGoals().stream()
                            .filter(goal -> goal.getDate() != null &&
                                    weekDateStrings.contains(goal.getDate().format(dateFormatter)))
                            .collect(Collectors.toList());
                    report.setDayGoals(filteredGoals);
                }

            });
            allReports.addAll(reports);
        }
        return allReports.stream()
                .filter(report -> report.getShopId() != null)
                .collect(Collectors.groupingBy(DayGoalReport::getShopId));
    }

    private List<DayGoalReport> getGoalReports(SearchConditions conditions) {
        int page = 1;
        int size = 0;
        List<DayGoalReport> reports = new ArrayList<>();

        do {
            List<DayGoalReport> batch = this.yiDaSession.searchFormDataConditionsEmbedded(DayGoalReport.class, conditions, page);
            page++;
            size = batch.size();
            reports.addAll(batch);
        } while (size == 1);

        return reports;
    }
}
