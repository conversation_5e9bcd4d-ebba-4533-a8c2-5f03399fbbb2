package com.astenamic.new_discovery.biz.report.pricing.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.YidaObject;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity(value = "FORM-D7A1D72936244A6291CCFCDDBD696F49C8KI", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class CollectForm extends YidaObject {

    // 日期
    @FormField("dateField_m59271i9")
    private LocalDateTime date;

    // 定价单项
    @FormField("tableField_m52hzd3h")
    private List<Collect> items;

}
