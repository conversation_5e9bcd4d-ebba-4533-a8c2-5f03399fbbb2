package com.astenamic.new_discovery.biz.report.food.repository;

import com.astenamic.new_discovery.biz.kitchendivision.domain.entity.FoodSalePredicate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FoodSalePredicateRepository extends JpaRepository<FoodSalePredicate, Long> {

    @Query(value = "SELECT ROW_NUMBER() OVER () AS id, " +
            "       NULL AS object_id, " +
            "       t1.shop_id, " +
            "       t1.food_name, " +
            "       t1.food_code, " +
            "       t1.food_unit, " +
            "       t1.预测销量 AS pred_sale, " +
            "       t2.实际销量 AS actual_sale, " +
            "       NULL AS manual_adj, " +
            "       :targetDay AS date " +
            "FROM ( " +
            "         SELECT a.shop_id, a.shop_name, a.food_code, a.food_name, a.food_unit, SUM(a.qty_forecast) 预测销量 " +
            "         FROM ( " +
            "                  SELECT DISTINCT shop_id, shop_name, food_code, food_name, food_unit, hour_interval, qty_forecast " +
            "                  FROM bh_sfm_report " +
            "                  WHERE day_format = :targetDay " +
            "                    AND hour_interval IN ( " +
            "                      '09:00~09:15', '09:16~09:30', '09:31~09:45', '09:46~10:00', '10:00~10:15', " +
            "                      '10:16~10:30', '10:31~10:45', '10:46~11:00', '11:00~11:15', '11:16~11:30', " +
            "                      '11:31~11:45', '11:46~12:00', '12:00~12:15', '12:16~12:30', '12:31~12:45', " +
            "                      '12:46~13:00', '13:00~13:15', '13:16~13:30', '13:31~13:45', '13:46~14:00', " +
            "                      '14:00~14:15', '14:16~14:30', '14:31~14:45', '14:46~15:00', '15:00~15:15', " +
            "                      '15:16~15:30', '15:31~15:45', '15:46~16:00', '16:00~16:15', '16:16~16:30', " +
            "                      '16:31~16:45', '16:46~17:00', '17:00~17:15', '17:16~17:30', '17:31~17:45', " +
            "                      '17:46~18:00', '18:00~18:15', '18:16~18:30', '18:31~18:45', '18:46~19:00', " +
            "                      '19:00~19:15', '19:16~19:30', '19:31~19:45', '19:46~20:00', '20:00~20:15', " +
            "                      '20:16~20:30', '20:31~20:45', '20:46~21:00' " +
            "                    ) " +
            "                    AND time_tag = '15分钟' " +
            "              ) AS a " +
            "         GROUP BY shop_id, shop_name, food_code, food_name, food_unit " +
            "         ORDER BY shop_id, food_code " +
            "     ) AS t1 " +
            "         LEFT JOIN ( " +
            "    SELECT tmp.shop_id, tmp.food_code, food_type, tmp.food_unit, SUM(tmp.sales_qty) 实际销量 " +
            "    FROM day_shop_food_sales_report_temp AS tmp " +
            "    WHERE tmp.account_date = :targetDay " +
            "    GROUP BY tmp.shop_id, tmp.food_code, tmp.food_unit, tmp.food_type " +
            ") AS t2 ON t1.food_code = t2.food_code " +
            "    AND t1.food_unit = t2.food_unit " +
            "    AND t1.shop_id = t2.shop_id " +
            "WHERE (t2.food_type NOT IN ('物料类','赠品') OR t2.food_type IS NULL) " +
            "  AND t1.food_name NOT LIKE '%米饭%'" +
            " AND t1.food_name NOT LIKE '赠送%'" +
            " AND t1.food_name NOT LIKE '%（赠送）'", nativeQuery = true)
    List<FoodSalePredicate> findByDate(@Param("targetDay")String date);
}
