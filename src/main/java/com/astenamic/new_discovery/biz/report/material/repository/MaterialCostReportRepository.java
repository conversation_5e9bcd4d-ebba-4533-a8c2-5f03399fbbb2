package com.astenamic.new_discovery.biz.report.material.repository;

import com.astenamic.new_discovery.biz.report.material.entity.MaterialCostReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialCostReportRepository extends JpaRepository<MaterialCostReport, Long> {

    @Query(value = "SELECT DISTINCT ROW_NUMBER() OVER ()   AS id,\n" +
            "                NULL                          AS object_id,\n" +
            "                shop_id                       AS shop_id,\n" +
            "                split_part(shop_name, '-', 1) AS brand_name,\n" +
            "                food_name                     AS food_name,\n" +
            "--                 food_code                     AS 菜品编码,\n" +
            "                unit_name                     AS unit_name,\n" +
            "                t3.material_type_big_ty       AS material_type_big_ty,\n" +
            "                t3.material_type_small_ty     AS material_type_small_ty,\n" +
            "                t1.material_name_hb           AS material_name_hb,\n" +
            "                t1.cb_unit_name               AS cb_unit_name_hb,\n" +
            "                yl_qty                        AS ll_qty_ucb\n" +
            "FROM day_shop_food_material_report_temp AS t1\n" +
            "         LEFT JOIN base_unit t2 ON t1.unit_id = t2.unit_id\n" +
            "         left join material_group_report_temp t3 on t1.material_id = t3.material_id\n" +
            "WHERE version_date = :dayStr\n" +
            "  AND shop_name LIKE '%毛菜测试店%'" +
            "  AND shop_name LIKE '%毛菜测试店%'", nativeQuery = true)
    List<MaterialCostReport> findCurrent(@Param("dayStr") String dayStr);
}
