package com.astenamic.new_discovery.biz.report.pricing.entity.data_base;

import com.astenamic.new_discovery.ace.scm.supplier.entity.SaveSupplier;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@Entity(name = "yida_pricing")
public class YiDaBase {

    // 数据id
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 货品定价项ID
    @Column(name = "lgpiid")
    private Integer lgpiid;

    // 定价单ID
    @Column(name = "lgpid")
    private Integer lgpid;

    // 供应商ID
    @Column(name = "supplier_id")
    private String supplier_id;

    // 货品ID
    @Column(name = "lgid")
    private Integer lgid;

    // 货品名称
    @Column(name = "good_name")
    private String good_name;

    // 货品编码
    @Column(name = "good_sno")
    private String good_sno;

    // 货品品牌
    @Column(name = "good_brand")
    private String good_brand;

    // 规格
    @Column(name = "std")
    private String std;

    // 货品别名
    @Column(name = "galias")
    private String galias;

    // 货品小类
    @Column(name = "goodtype_name")
    private String goodtype_name;

    // 货品大类
    @Column(name = "fgoodtype_name")
    private String fgoodtype_name;

    // 订货单位名称
    @Column(name = "applyguname")
    private String applyguname;

    // 价格执行门店ID列表
    @Column(name = "shop_id", length = 1000)
    private String shop_id;

    // 价格执行门店名称
    @Column(name = "shop_name", length = 2000)
    private String shop_name;

    // 价格执行门店编码
    @Column(name = "shop_sno", length = 1000)
    private String shop_sno;

    // 供应商名称
    @Column(name = "supplier_name")
    private String supplier_name;

    // 供应商编码
    @Column(name = "supplier_sno")
    private String supplier_sno;

    // 当前执行价格
    @Column(name = "nowprice")
    private Float nowprice;

    // 下次定价
    @Column(name = "uprice")
    private Float uprice;

    // 询价一
    @Column(name = "marketpriceone")
    private String marketpriceone;

    // 询价二
    @Column(name = "marketpricetwo")
    private String marketpricetwo;

    // 供应商报价
    @Column(name = "supplier_price")
    private String supplier_price;

    // 发票类型
    @Column(name = "invoicetypetext")
    private String invoicetypetext;

    // 税率
    @Column(name = "tax")
    private Float tax;

    // 扣除率
    @Column(name = "deductrate")
    private Float deductrate;

    // 价格开始生效日期
    @Column(name = "startdate")
    private LocalDateTime startdate;

    // 价格结束日期
    @Column(name = "enddate")
    private LocalDateTime enddate;

    // 去年同期平均单价
    @Column(name = "lastyprice")
    private String lastyprice;

    // 上期平均采购单价
    @Column(name = "lastprice")
    private String lastprice;

    // 上月采购数量
    @Column(name = "applyamount")
    private String applyamount;

    // 询价调价比率
    @Column(name = "marketpriceratio")
    private String marketpriceratio;

    // 供应商报价调价比率
    @Column(name = "supriceratio")
    private String supriceratio;

    // 环比调价比率
    @Column(name = "momratio")
    private String momratio;

    // 同比调价比率
    @Column(name = "yoyratio")
    private String yoyratio;

    // 下月采购金额预估
    @Column(name = "estmoney")
    private Float estmoney;

    // 环比影响金额
    @Column(name = "mommoney")
    private Float mommoney;

    // 同比影响金额
    @Column(name = "yoymoney")
    private Float yoymoney;

    // 调价原因
    @Column(name = "reason")
    private String reason;

    // 更新时间
    @Column(name = "utime")
    private String utime;

    // 制单时间
    @Column(name = "ctime")
    private LocalDateTime ctime;

    // 审核时间
    @Column(name = "atime")
    private LocalDateTime atime;

    // 关联字段(店铺名称)
    @Transient
    private List<String> shopNames;

    // 关联字段(供应商)
    @Transient
    private List<SaveSupplier> suppliers;

}
