package com.astenamic.new_discovery.biz.report.food.entity.month;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity("tableField_m5oyw36b")
public class FoodInfo {

    // 菜品id
    @FormField("textField_m5otglig")
    private String foodId;

    // 菜品编码
    @FormField("textField_m5otglii")
    private String foodCode;

    // 菜品名称
    @FormField("textField_m5otglik")
    private String foodName;

    // 菜品类型
    @FormField("textField_m5otglim")
    private String foodStyleName;

    // 菜品做法ID
    @FormField("textField_m5otglio")
    private String foodCookId;

    // 菜品做法名称
    @FormField("textField_m5otgliq")
    private String foodCookName;

    // 菜品做法ID
    @FormField("textField_m5otglis")
    private String foodUnitId;

    // 菜品做法名称
    @FormField("textField_m5otgliu")
    private String foodUnitName;

    // 理论成本合计
    @FormField("numberField_m5qk5c8p")
    private Float llCost;

    // 实际成本合计
    @FormField("numberField_m5qk5c8q")
    private Float sjCost;

    // 理论成本率
    @FormField("numberField_m5qk5c8m")
    private Float llCostRate;

    // 实际成本率
    @FormField("numberField_m5qk5c8n")
    private Float sjCostRate;

    // 菜品流水
    @FormField("numberField_m5qk5c8o")
    private Float liushui;

}
