package com.astenamic.new_discovery.biz.wlift.service;

import com.astenamic.new_discovery.biz.wlift.entity.ConsumeRecord;
import com.astenamic.new_discovery.biz.wlift.entity.valueobject.UserCoupon;
import com.astenamic.new_discovery.biz.wlift.repository.ConsumeRecordRepository;
import com.astenamic.new_discovery.biz.wlift.repository.UserCouponRepository;
import com.astenamic.new_discovery.external.modal.dto.wlift.AccountBasicsInfoDTO;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.modal.dto.wlift.ConsumeRecordDTO;
import com.astenamic.new_discovery.external.client.wlift.service.QueryInterfaceService;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ConsumeRecordService {

    private static final Logger logger = LoggerFactory.getLogger(ConsumeRecordService.class);

    private final ConsumeRecordRepository consumeRecordRepository;

    private final UserCouponRepository userCouponRepository;

    private final QueryInterfaceService queryInterfaceService;


    public List<ConsumeRecord> syncConsumeRecord(LocalDateTime dateTime) {

        List<ConsumeRecordDTO> consumeRecordDTOS = Arrays.stream(BrandEnum.values())
                .filter(brand -> queryInterfaceService.supports(brand.getCode()))
                .flatMap(brand -> {
                    List<ConsumeRecordDTO> list = queryInterfaceService.queryConsumeList(brand, dateTime, dateTime, null);
                    Map<String, List<ConsumeRecordDTO>> byCno = list.stream()
                            .collect(Collectors.groupingBy(ConsumeRecordDTO::getCno));
                    byCno.forEach((cno, dtos) -> {
                        String phone = Optional.ofNullable(
                                        queryInterfaceService.queryAccountBasicsInfo(brand, cno))
                                .map(AccountBasicsInfoDTO::getPhone)
                                .orElse(null);
                        dtos.forEach(dto -> dto.setPhoneNumber(phone));
                    });
                    return list.stream();
                })
                .toList();

        if (consumeRecordDTOS.isEmpty()) {
            logger.info("没有需要同步的消费记录");
            return new ArrayList<>();
        }

        List<ConsumeRecord> consumeRecords = new ArrayList<>();

        List<UserCoupon> userCoupons = new ArrayList<>();

        for (ConsumeRecordDTO consumeRecordDTO : consumeRecordDTOS) {

            ConsumeRecord consumeRecord = new ConsumeRecord();

            BeanUtils.copyProperties(consumeRecordDTO, consumeRecord, "userCouponList");

            List<UserCoupon> userCouponCache = new ArrayList<>();
            for (ConsumeRecordDTO.UserCouponDTO userCouponDTO : consumeRecordDTO.getUserCouponList()) {
                UserCoupon userCoupon = new UserCoupon();
                BeanUtils.copyProperties(userCouponDTO, userCoupon);
                userCoupon.setDealId(consumeRecordDTO.getDealId());
                userCoupons.add(userCoupon);
                userCouponCache.add(userCoupon);
            }
            consumeRecord.setUserCouponList(userCouponCache);

            consumeRecords.add(consumeRecord);
        }

        consumeRecordRepository.saveAll(consumeRecords);
        userCouponRepository.saveAll(userCoupons);

        return consumeRecords;
    }
}
