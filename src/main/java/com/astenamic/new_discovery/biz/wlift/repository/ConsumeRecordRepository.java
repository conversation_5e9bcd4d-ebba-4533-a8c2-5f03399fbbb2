package com.astenamic.new_discovery.biz.wlift.repository;


import com.astenamic.new_discovery.biz.wlift.entity.ConsumeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConsumeRecordRepository extends JpaRepository<ConsumeRecord, Long> {

    @Query(value = "select * from xfx_wlift_consume_record where to_char( pay_time, 'yyyy-mm-dd' ) = :payTime", nativeQuery = true)
    List<ConsumeRecord> findByPayTime(@Param("payTime") String payTime);
}
