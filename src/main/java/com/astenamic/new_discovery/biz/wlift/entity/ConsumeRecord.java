package com.astenamic.new_discovery.biz.wlift.entity;

import com.astenamic.new_discovery.biz.wlift.entity.valueobject.UserCoupon;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity(name = "xfx_wlift_consume_record")
public class ConsumeRecord {


    /**
     * 消费流水 ID
     */
    @Id
    @Column(name = "deal_id", columnDefinition = "varchar(64)")
    private String dealId;

    /**
     * 消费用户卡号
     */
    @Column(name = "cno", columnDefinition = "varchar(64)")
    private String cno;

    /**
     * 消费用户手机号
     */
    @Column(name = "phone_number", columnDefinition = "varchar(64)")
    private String phoneNumber;

    /**
     * 消费门店 ID
     */
    @Column(name = "sid", columnDefinition = "varchar(64)")
    private String sid;

    /**
     * 被撤销消费的流水 ID，如果未撤销则为 0
     */
    @Column(name = "related_id", columnDefinition = "varchar(64)")
    private String relatedId;

    /**
     * 消费总金额（分）
     */
    @Column(name = "total_fee", columnDefinition = "int8")
    private Long totalFee;

    /**
     * 实收金额（分）
     */
    @Column(name = "actual_fee", columnDefinition = "int8")
    private Long fee;

    /**
     * 使用储值支付金额（分）
     */
    @Column(name = "stored_pay", columnDefinition = "int8")
    private Long storedPay;

    /**
     * 使用实际储值支付金额（分）
     */
    @Column(name = "stored_sale_pay", columnDefinition = "int8")
    private Long storedSalePay;

    /**
     * 使用代金券抵扣金额（分）
     */
    @Column(name = "cash_coupon_pay", columnDefinition = "int8")
    private Long cashCouponPay;

    /**
     * 使用礼品券抵扣金额（分）
     */
    @Column(name = "gift_coupon_pay", columnDefinition = "int8")
    private Long giftCouponPay;

    /**
     * 使用积分数量
     */
    @Column(name = "credit_num", columnDefinition = "int8")
    private Long creditNum;

    /**
     * 使用积分抵扣金额（分）
     */
    @Column(name = "credit_pay", columnDefinition = "int8")
    private Long creditPay;

    /**
     * 奖励积分数量
     */
    @Column(name = "credit_award", columnDefinition = "int8")
    private Long creditAward;

    /**
     * 交易类型，1:充值，2:消费，3:撤销消费...
     */
    @Column(name = "type", columnDefinition = "int2")
    private Integer type;

    /**
     * 支付类型，1:现金，2:银行卡，3:店内微信...
     */
    @Column(name = "pay_type", columnDefinition = "int2")
    private Integer payType;

    /**
     * 交易时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @Column(name = "pay_time", columnDefinition = "timestamp")
    private LocalDateTime payTime;

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "varchar(255)")
    private String remark;

    /**
     * 第三方交易号
     */
    @Column(name = "biz_id", columnDefinition = "varchar(64)")
    private String bizId;

    /**
     * 消费使用券名称
     */
    @Column(name = "user_coupon_name", columnDefinition = "varchar(64)")
    private String userCouponName;

    /**
     * 会员等级名称
     */
    @Column(name = "grade_name", columnDefinition = "varchar(64)")
    private String gradeName;

    /**
     * 会员等级编码
     */
    @Column(name = "grade_id", columnDefinition = "varchar(64)")
    private String gradeId;

    /**
     * 消费一级来源
     */
    @Column(name = "source", columnDefinition = "varchar(64)")
    private String source;

    /**
     * 消费二级来源
     */
    @Column(name = "channel", columnDefinition = "varchar(64)")
    private String channel;

    /**
     * 消费使用券明细
     */
    @Transient
    private List<UserCoupon> userCouponList;
}
