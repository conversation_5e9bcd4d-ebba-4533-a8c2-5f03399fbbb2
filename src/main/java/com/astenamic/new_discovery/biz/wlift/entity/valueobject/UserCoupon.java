package com.astenamic.new_discovery.biz.wlift.entity.valueobject;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.Data;

@Data
@Entity(name = "xfx_wlift_user_coupon")
public class UserCoupon {

    /**
     * 消费流水 ID
     */
    @Column(name = "deal_id", columnDefinition = "varchar(64)")
    private String dealId;

    /**
     * 券数量
     */
    @Column(name = "num", columnDefinition = "int4")
    private Integer num;

    /**
     * 券名称
     */
    @Column(name = "name", columnDefinition = "varchar(64)")
    private String name;

    /**
     * 使用券金额（元）
     */
    @Column(name = "amount", columnDefinition = "varchar(64)")
    private String amount;

    /**
     * 券 ID
     */
    @Column(name = "coupon_id", columnDefinition = "varchar(64)")
    private String couponId;

    /**
     * 券类型，1:代金券，2:礼品券，4:券包
     */
    @Column(name = "type", columnDefinition = "int2")
    private Integer type;

    /**
     * 券流水号
     */
    @Id
    @Column(name = "c2u_id", columnDefinition = "varchar(64)")
    private String c2uId;
}
