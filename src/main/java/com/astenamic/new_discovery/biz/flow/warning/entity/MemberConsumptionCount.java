package com.astenamic.new_discovery.biz.flow.warning.entity;

import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class MemberConsumptionCount extends EarlyWarningProcess {

    @FormField("tableField_ma4oejal")
    private List<MemberConsumptionCountItem> memberConsumptionCountList;

    @Data
    @FormEntity("tableField_ma4oejal")
    public static class MemberConsumptionCountItem {

        @FormField("textField_ma4oejam")
        private String cno;

        @FormField("textField_ma4oejan")
        private String gradeName;

        @FormField("textField_maz172bk")
        private String phoneNumber;

        @FormField("numberField_maat3azq")
        private Float payCount;

        @FormField("numberField_mc48wi4e")
        private Float monthPayCount;

        @FormField("textareaField_ma4oejap")
        private String payTimeList;

        @FormField("textareaField_ma4oejar")
        private String actualMoneyList;

        @FormField("textareaField_ma4oejas")
        private String totalMoneyList;
    }
}
