package com.astenamic.new_discovery.biz.flow.warning.entity.dto;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity(name = "yida_food_back_type")
public class FoodBackReasonDTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "shop_id")
    private String shopId;

    @Column(name = "account_date")
    private LocalDateTime date;

    @Column(name = "food_name")
    private String foodName;

    @Column(name = "retreat_remark")
    private String retreatRemark;

    @Column(name = "retreat_money")
    private Float retreatMoney;

    @Column(name = "retreat_qty")
    private Float retreatQty;

    @Column(name = "retreat_rate")
    private Float retreatRate;

    @Column(name = "retreat_all_money")
    private Float retreatAllMoney;
}
