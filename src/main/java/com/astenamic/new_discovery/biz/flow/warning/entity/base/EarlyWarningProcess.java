package com.astenamic.new_discovery.biz.flow.warning.entity.base;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class EarlyWarningProcess extends YidaFlowObject {


    @FormField("textField_m8vay3r2")
    private String shopId;
    // 异常字段
    @FormField("textField_m4257mdg")
    private String abnormalField;

    // 异常值
    @FormField("numberField_m4c58b04")
    private Float abnormalValue;

    // 日期
    @FormField("dateField_m418kt5q")
    private LocalDateTime date;

    // 督导
    @FormField("employeeField_m4ao6mpe")
    private List<String> supervisorSysId;
    // 品牌id
    @FormField("departmentSelectField_m46h9dkd")
    private List<String> brandSysId;
    // 门店经理id
    @FormField("employeeField_m3xxubmp")
    private List<String> shopManagerSysId;
    // 门店
    @FormField("departmentSelectField_m46eiry0")
    private List<String> shopSysId;
    // 战区司令id
    @FormField("employeeField_m54sz9bn")
    private List<String> batAreaPeopleSysId;
    // 异常类型
    @FormField("selectField_m4v33mot")
    private String abnormalType;
    // 厨师长
    @FormField("employeeField_m4cg23ma")
    private List<String> chiefSysId;
    // 战区id
    @FormField("departmentSelectField_m86nlx8x")
    private List<String> batAreaSysId;
    // 对应会计
    @FormField("employeeField_m8mvfd8i")
    private List<String> accountantSysId;
    // 区域
    @FormField("departmentSelectField_m419c2zu")
    private List<String> areaSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setShopManagerSysId(String shopManagerSysId) {
        if (shopManagerSysId != null) {
            this.shopManagerSysId = List.of(shopManagerSysId);
        }
    }

    public void setAccountantSysId(String accountantSysId) {
        if (accountantSysId != null) {
            this.accountantSysId = List.of(accountantSysId);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }


}
