package com.astenamic.new_discovery.biz.flow.warning.repository;


import com.astenamic.new_discovery.biz.flow.warning.entity.dto.TakeoutDiscountDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TakeoutDiscountRepository extends JpaRepository<TakeoutDiscountDTO, Long> {


    @Query(value = "SELECT " +
            "  ROW_NUMBER() OVER () AS id,\n" +
            "  C.shop_id , " +
            "  A.wm_code , " +
            "  b.activity_name , " +
            "  b.shop_discount_money , " +
            "  substr(A.order_date, 1, 10) AS order_date, " +
            "  CASE " +
            "     WHEN A.wm_type = '16933600100000804' THEN '饿了么' " +
            "     WHEN A.wm_type = '16939683850008912' THEN '美团' " +
            "  END AS order_source " +
            "FROM wm_order A " +
            "LEFT JOIN wm_discount_detail b ON A.wm_code = b.wm_order_id " +
            "INNER JOIN base_shop C ON A.shop_id = C.shop_id " +
            "LEFT JOIN base_region d ON C.region_id = d.region_id " +
            "WHERE A.order_sum != 0 " +
            "  AND (b.activity_type IN ('40', '2024') OR (b.activity_type = '1' AND b.activity_name = '商家安抚红包')) " +
            "  AND b.shop_discount_money > 3 " +
            "  AND A.is_del = 0 " +
            "  AND A.order_status = 1 " +
            "  AND substr(A.order_date, 1, 10) ~ :dates " +
            "ORDER BY A.order_date DESC",
            nativeQuery = true)
    List<TakeoutDiscountDTO> findByTime(@Param("dates") String dates);
}
