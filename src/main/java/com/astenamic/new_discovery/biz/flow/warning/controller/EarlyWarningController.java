package com.astenamic.new_discovery.biz.flow.warning.controller;

import com.astenamic.new_discovery.biz.flow.warning.service.EarlyWarningService;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 预警
 */
@RestController
@RequestMapping("/biz/warning")
@AllArgsConstructor
public class EarlyWarningController {

    private final EarlyWarningService earlyWarningService;

    /**
     * 退菜类型预警
     *
     * @param param
     */
    @GetMapping("/food/type")
    public void foodType(@RequestBody Param param) {
        String dateStr = param.date;
        LocalDateTime target = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        earlyWarningService.earlyWarning(target);
    }

    @Data
    private static class Param {
        private String date;
    }
}
