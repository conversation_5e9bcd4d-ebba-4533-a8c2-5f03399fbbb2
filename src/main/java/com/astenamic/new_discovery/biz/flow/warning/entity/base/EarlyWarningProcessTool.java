package com.astenamic.new_discovery.biz.flow.warning.entity.base;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class EarlyWarningProcessTool extends YidaFlowObject {

    @FormField("textField_m8vay3r2")
    private String shopId;

}
