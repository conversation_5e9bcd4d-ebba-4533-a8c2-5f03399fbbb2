package com.astenamic.new_discovery.biz.flow.warning.service;

import com.astenamic.new_discovery.biz.flow.warning.entity.EmployeeMeal;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.EmployeeMealDTO;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.flow.warning.repository.EmployeeMealRepository;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class EmployeeMealService extends AbstractEarlyWarningService<EmployeeMeal> {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeMealService.class);

    private final EmployeeMealRepository employeeMealRepository;

    @Override
    public void employeeMealWarning(LocalDateTime dateTime) {
        Pair<List<EmployeeMeal>, List<EmployeeMeal>> pair = super.batchLaunchProcess(dateTime, WarningType.EMPLOYEE_MEAL);
        logger.info("{}预警发起完成：{}", WarningType.EMPLOYEE_MEAL.getType(), pair.getFirst().size());
    }

    @Override
    protected Pair<Class<EmployeeMeal>, List<EmployeeMeal>> getNewData(LocalDateTime dateTime, WarningType type) {
        Time time = Time.day(dateTime);
        List<EmployeeMealDTO> employeeMealDTOS = employeeMealRepository.findByTime(time.getCurrent());

        List<EmployeeMeal> result = new ArrayList<>();
        for (EmployeeMealDTO employeeMealDTO : employeeMealDTOS) {
            if (employeeMealDTO.isNeedWarn()) {
                EmployeeMeal employeeMeal = new EmployeeMeal();
                employeeMeal.setDate(employeeMealDTO.getDate());
                employeeMeal.setShopId(employeeMealDTO.getShopId());
                employeeMeal.setAbnormalType(type.getType());
                employeeMeal.setAbnormalField(type.getType());
                employeeMeal.setTotalMoney(employeeMealDTO.getTotalMoney());
                employeeMeal.setPeopleCount(employeeMealDTO.getPeopleCount());
                employeeMeal.setNormMoney(employeeMealDTO.getNormMoney());
                result.add(employeeMeal);
            }
        }

        return Pair.of(EmployeeMeal.class, relateShopRelation(result));
    }
}
