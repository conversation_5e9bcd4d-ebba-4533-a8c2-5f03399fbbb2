package com.astenamic.new_discovery.biz.flow.warning.repository;


import com.astenamic.new_discovery.biz.flow.warning.entity.dto.FoodBackReasonDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FoodBackReasonRepository extends JpaRepository<FoodBackReasonDTO, Long> {

    /**
     * 查询退菜原因预警数据
     *
     * @param dates 日期列表，格式如 "2025-03-26|2025-03-25"
     * @return 退菜原因统计结果
     */
    @Query(value = """
            WITH params AS ( SELECT :dates AS date_prefix ),
            retreat_filtered AS (
              SELECT
                shop_id,
                shop_name AS "门店名称",
                region_name AS "区域",
                brand_name,
                account_date AS "交易日期",
                retreat_range AS "退菜时间区间",
                shishou AS "实收",
                yingshou AS "应收",
                retreat_money AS "退菜金额",
                COALESCE ( ABS ( COALESCE ( retreat_money, 0 ) / NULLIF ( yingshou, 0 ) ) * 100, 0 ) AS "退菜率"
              FROM
                retreat_statistics_shop_retreat_rate_report,
                params
              WHERE
                account_date ~ params.date_prefix\s
                AND retreat_range = '5分钟以上'
              ),
              qualified_shops AS (
              SELECT
                shop_id
              FROM
                retreat_filtered
              WHERE
                ( brand_name = '新发现' AND "退菜率" > 0.6 )
                OR ( brand_name IN ( '烤匠', '蝴蝶里' ) AND "退菜率" > 0.4 )
              ),
              TEMP AS (
              SELECT
                r.shop_id,
                r.account_date,
                r.food_name,
                r.retreat_remark,
                SUM ( r.retreat_money ) AS retreat_money,
                ABS ( SUM ( r.retreat_qty ) ) AS retreat_qty
              FROM
                retreat_statistics_shop_retreat_reason_report r,
                params
              WHERE
                r.account_date ~ params.date_prefix
                AND r.retreat_range = '5分钟以上'
                AND r.shop_id IN ( SELECT shop_id FROM qualified_shops )
              GROUP BY
                r.shop_id,
                r.account_date,
                r.food_name,
                r.retreat_remark
              ) SELECT ROW_NUMBER
              ( ) OVER ( ) AS ID,
              T.*,
              rf."退菜金额" AS "retreat_all_money",
              rf."退菜率" AS "retreat_rate"
            
            FROM
              TEMP T LEFT JOIN retreat_filtered rf ON T.shop_id = rf.shop_id;
            """,
            nativeQuery = true)
    List<FoodBackReasonDTO> findByTime(@Param("dates") String dates);

}
