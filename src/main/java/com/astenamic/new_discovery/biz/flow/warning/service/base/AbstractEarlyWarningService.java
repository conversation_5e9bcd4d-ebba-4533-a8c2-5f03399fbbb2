package com.astenamic.new_discovery.biz.flow.warning.service.base;

import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcessTool;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;


public abstract class AbstractEarlyWarningService<T extends EarlyWarningProcess> implements EarlyWarningService {

    private static final Logger logger = LoggerFactory.getLogger(AbstractEarlyWarningService.class);

    @Resource
    protected YiDaSession yiDaSession;

    @Resource
    protected ShopRelationAppService shopRelationAppService;


    protected Pair<List<T>, List<T>> batchLaunchProcess(LocalDateTime dateTime, WarningType type) {

        List<EarlyWarningProcessTool> oldData = getOldData(dateTime, type);

        Pair<Class<T>, List<T>> pair = getNewData(dateTime, type);
        Class<T> clz = pair.getFirst();
        List<T> newData = pair.getSecond();

        if (oldData != null && !oldData.isEmpty()) {
            logger.info("{}获取宜搭数据完成，{}", type.getType(), oldData.size());
            for (EarlyWarningProcessTool oldDatum : oldData) {
                for (T newDatum : newData) {
                    if (oldDatum.getShopId().equals(newDatum.getShopId())) {
                        newDatum.setInstanceId(oldDatum.getInstanceId());
                        break;
                    }
                }
            }
        }
        logger.info("{}获取新数据完成，{}", type.getType(), newData.size());

        Map<Integer, List<T>> data = newData.stream()
                .collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getInstanceId()) ? 1 : 2));

        data.forEach((k, rows) -> {
            logger.info("{}数据分组完成，{}组", type.getType(), k);
            for (T group : rows) {
                if (k == 1) {
                    this.yiDaSession.processSave(group, clz, "", "75603689");
                } else {
                    // Todo: 流程表单不允许更新
                }
            }
        });

        return Pair.of(data.getOrDefault(1, new ArrayList<>()), data.getOrDefault(2, new ArrayList<>()));
    }

    protected abstract Pair<Class<T>, List<T>> getNewData(LocalDateTime dateTime, WarningType type);

    protected List<EarlyWarningProcessTool> getOldData(LocalDateTime dateTime, WarningType type) {
        dateTime = dateTime.with(LocalTime.MIN);

        long[] time = TimeUtils.getDayRange(dateTime);

        SearchConditions cond = SearchCondition
                .builder()
                .dateBetween("dateField_m418kt5q", time, "+")
                .textEq("selectField_m4v33mot", type.getType(), "+")
                .get();

        int page = 1;
        int size = 0;

        List<EarlyWarningProcessTool> oldData = new ArrayList<>();
        do {
            List<EarlyWarningProcessTool> od = new ArrayList<>();
            try {
                od = this.yiDaSession.searchFormDataConditionsRequest(EarlyWarningProcessTool.class, cond, page);
            } catch (Exception e) {
                logger.error("获取宜搭报表数据失败，{}{}", e.getMessage(), System.lineSeparator(), e);
                throw e;
            }
            page++;
            size = od.size();
            oldData.addAll(od);
        } while (size == 100);
        logger.info("获取宜搭报表数据完成，{}", oldData.size());
        return oldData.stream()
                .filter(e -> StringUtils.isNotBlank(e.getShopId()))
                .toList();
    }

    protected List<T> relateShopRelation(List<T> datas) {
        if (datas == null || datas.isEmpty()) {
            return new ArrayList<>();
        }

        List<ShopRelation> relations = this.shopRelationAppService.getRelationsByClosed("否");
        Map<String, ShopRelation> shopRelationMap = relations.stream()
                .filter(r -> r.getShopId() != null)
                .collect(Collectors.toMap(
                        ShopRelation::getShopId,
                        r -> r,
                        (existing, replacement) -> existing
                ));

        datas.forEach(data -> {
            String shopId = data.getShopId();
            if (StringUtils.isNotBlank(shopId) && shopRelationMap.containsKey(shopId)) {
                ShopRelation relation = shopRelationMap.get(shopId);
                data.setShopSysId(getListValue(relation.getShopSysId()));
                data.setBrandSysId(getListValue(relation.getBrandSysId()));
                data.setBatAreaSysId(getListValue(relation.getBatAreaSysId()));
                data.setBatAreaPeopleSysId(getListValue(relation.getBatAreaPeopleSysId()));
                data.setAreaSysId(getListValue(relation.getAreaSysId()));
                data.setSupervisorSysId(getListValue(relation.getSupervisorSysId()));
                data.setShopManagerSysId(getListValue(relation.getShopManagerSysId()));
                data.setChiefSysId(getListValue(relation.getChiefSysId()));
                data.setAccountantSysId(getListValue(relation.getAccountant()));
            }
        });

        return datas.stream()
                .filter(e -> e.getShopSysId() != null && !e.getShopSysId().isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public void foodBackWarning(LocalDateTime dateTime) {
        logger.error("foodBackWarning方法未实现");
    }

    @Override
    public void takeoutDiscountWarning(LocalDateTime dateTime) {
        logger.error("takeoutDiscountWarning方法未实现");
    }

    @Override
    public void employeeMealWarning(LocalDateTime dateTime) {
        logger.error("employeeMealWarning方法未实现");
    }

    @Override
    public void takeoutFoodOnShelvesWarning() {
        logger.error("takeoutFoodBackWarning方法未实现");
    }

    @Override
    public void memberConsumptionWarning(LocalDateTime dateTime) {
        logger.error("memberConsumptionWarning方法未实现");
    }

    public String getListValue(List<String> list) {
        return Optional.ofNullable(list)
                .filter(vs -> !vs.isEmpty())
                .map(vs -> vs.get(0))
                .orElse("");
    }

}
