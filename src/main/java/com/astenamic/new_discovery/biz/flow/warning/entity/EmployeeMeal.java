package com.astenamic.new_discovery.biz.flow.warning.entity;

import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class EmployeeMeal  extends EarlyWarningProcess {

    @FormField("numberField_m92fvo72")
    private Float totalMoney;

    @FormField("numberField_m92fvo73")
    private Float peopleCount;

    @FormField("numberField_m92fvo74")
    private Float normMoney;

}
