package com.astenamic.new_discovery.biz.flow.warning.entity;

import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class TakeoutDiscount extends EarlyWarningProcess {

    @FormField("tableField_m9252cwt")
    private List<TakeoutDiscountItem> takeoutDiscountList;

    @Data
    @FormEntity("tableField_m9252cwt")
    public static class TakeoutDiscountItem {
        @FormField("textField_m9252cwu")
        private String wmOrderCode;
        @FormField("selectField_m9252cwv")
        private String orderSource;
        @FormField("numberField_m9252cww")
        private Float discountMoney;
        @FormField("textField_m9252cwx")
        private String activityName;
    }
}
