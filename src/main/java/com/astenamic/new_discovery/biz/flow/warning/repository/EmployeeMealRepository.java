package com.astenamic.new_discovery.biz.flow.warning.repository;


import com.astenamic.new_discovery.biz.flow.warning.entity.dto.EmployeeMealDTO;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.FoodBackReasonDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeMealRepository extends JpaRepository<EmployeeMealDTO, Long> {


    /**
     * 查询员工餐预警数据
     *
     * @param dates 日期列表，格式如 "2025-04-03"
     * @return 员工餐预警数据
     */
    @Query(value = "WITH temp1 AS (\n" +
            "  SELECT C\n" +
            "    .shop_id,\n" +
            "    C.shop_name,\n" +
            "    total_price,\n" +
            "    to_char( A.account_date, 'yyyy-mm-dd' ) account_date \n" +
            "  FROM\n" +
            "    scm_stock_in\n" +
            "    A LEFT JOIN base_stock b ON A.stock_org_id = b.stock_id\n" +
            "    LEFT JOIN (\n" +
            "    SELECT A\n" +
            "      .shop_name,\n" +
            "      A.shop_id,\n" +
            "      C.org_id \n" +
            "    FROM\n" +
            "      base_shop\n" +
            "      A LEFT JOIN map_org_relation b ON A.shop_id = b.target_org_id \n" +
            "      AND b.relation_type = 'shop' \n" +
            "      AND b.is_del = 0\n" +
            "      LEFT JOIN base_org C ON C.org_id = b.source_org_id \n" +
            "    ) C ON b.org_id = C.org_id \n" +
            "  WHERE\n" +
            "    to_char( A.account_date, 'yyyy-mm-dd' ) ~ :dates  \n" +
            "    AND b.stock_name = '员工餐库房' \n" +
            "  ),\n" +
            "  temp2 AS (\n" +
            "  SELECT\n" +
            "    b.target_org_id AS shop_id,\n" +
            "    A.clockin_date,\n" +
            "    COUNT (\n" +
            "      DISTINCT (\n" +
            "      CASE\n" +
            "          \n" +
            "          WHEN b.target_org_id IN ( '9323219540978579321', '1685888264994688986' ) \n" +
            "          AND SUBSTRING ( A.start_time, 12 ) <= '13:30:00' \n" +
            "          AND o.org_name LIKE'%前厅%' THEN\n" +
            "            user_id \n" +
            "            WHEN SUBSTRING ( A.start_time, 12 ) <= '15:00:00' \n" +
            "            AND o.org_name LIKE'%前厅%' THEN\n" +
            "              user_id \n" +
            "            END \n" +
            "            ) \n" +
            "          ) AS afternoon_front_people_count,\n" +
            "          COUNT (\n" +
            "            DISTINCT (\n" +
            "            CASE\n" +
            "                \n" +
            "                WHEN b.target_org_id IN ( '9323219540978579321', '1685888264994688986' ) \n" +
            "                AND ( SUBSTRING ( A.start_time, 12 ) > '13:30:00' OR SUBSTRING ( A.end_time, 12 ) > '18:00:00' ) \n" +
            "                AND o.org_name LIKE'%前厅%' THEN\n" +
            "                  user_id \n" +
            "                  WHEN SUBSTRING ( A.end_time, 12 ) > '18:00:00' \n" +
            "                  AND o.org_name LIKE'%前厅%' THEN\n" +
            "                    user_id \n" +
            "                  END \n" +
            "                  ) \n" +
            "                ) AS evening_front_people_count,\n" +
            "                COUNT (\n" +
            "                  DISTINCT (\n" +
            "                  CASE\n" +
            "                      \n" +
            "                      WHEN b.target_org_id IN ( '9323219540978579321', '1685888264994688986' ) \n" +
            "                      AND SUBSTRING ( A.start_time, 12 ) <= '13:30:00' \n" +
            "                      AND ( o.org_name LIKE'%后厨%' OR o.org_name LIKE'%厨房%' ) THEN\n" +
            "                        user_id \n" +
            "                        WHEN SUBSTRING ( A.start_time, 12 ) <= '15:00:00' \n" +
            "                        AND ( o.org_name LIKE'%后厨%' OR o.org_name LIKE'%厨房%' ) THEN\n" +
            "                          user_id \n" +
            "                        END \n" +
            "                        ) \n" +
            "                      ) AS afternoon_kitchen_people_count,\n" +
            "                      COUNT (\n" +
            "                        DISTINCT (\n" +
            "                        CASE\n" +
            "                            \n" +
            "                            WHEN b.target_org_id IN ( '9323219540978579321', '1685888264994688986' ) \n" +
            "                            AND ( SUBSTRING ( A.start_time, 12 ) > '13:30:00' OR SUBSTRING ( A.end_time, 12 ) > '18:00:00' ) \n" +
            "                            AND ( o.org_name LIKE'%后厨%' OR o.org_name LIKE'%厨房%' ) THEN\n" +
            "                              user_id \n" +
            "                              WHEN SUBSTRING ( A.end_time, 12 ) > '18:00:00' \n" +
            "                              AND ( o.org_name LIKE'%后厨%' OR o.org_name LIKE'%厨房%' ) THEN\n" +
            "                                user_id \n" +
            "                              END \n" +
            "                              ) \n" +
            "                            ) AS evening_kitchen_people_count \n" +
            "                          FROM\n" +
            "                            hr_clockin_day\n" +
            "                            A LEFT JOIN base_org o ON o.org_id = A.org_id \n" +
            "                            AND o.status = '1' \n" +
            "                            AND o.is_del = '0'\n" +
            "                            LEFT JOIN map_org_relation b ON b.source_org_id = o.up_org_id \n" +
            "                            AND b.is_del = '0' \n" +
            "                          WHERE\n" +
            "                            substr( A.clockin_date, 1, 10 ) ~ :dates \n" +
            "                            AND A.is_del = '0' \n" +
            "                            AND b.source_data_type = 'org' \n" +
            "                            AND b.relation_type = 'shop' \n" +
            "                          GROUP BY\n" +
            "                            b.target_org_id,\n" +
            "                            A.clockin_date \n" +
            "                          ) SELECT\n" +
            "                          ROW_NUMBER() OVER () AS id,\n" +
            "                          t1.* ,\n" +
            "                          t2.people_count,\n" +
            "                          t2.people_count * 15 norm_money \n" +
            "                        FROM\n" +
            "                        ( SELECT temp1.shop_id, temp1.account_date, SUM ( total_price ) total_money FROM temp1 GROUP BY temp1.shop_id, temp1.account_date ) t1\n" +
            "  LEFT JOIN ( SELECT shop_id, ( afternoon_front_people_count + evening_front_people_count )::numeric / 2 + ( afternoon_kitchen_people_count + evening_kitchen_people_count )::numeric / 2 AS people_count FROM temp2 ) t2 ON t1.shop_id = t2.shop_id;", nativeQuery = true)
    List<EmployeeMealDTO> findByTime(@Param("dates") String dates);
}
