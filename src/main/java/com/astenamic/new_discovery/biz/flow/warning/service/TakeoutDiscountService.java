package com.astenamic.new_discovery.biz.flow.warning.service;

import com.astenamic.new_discovery.biz.flow.warning.entity.TakeoutDiscount;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.TakeoutDiscountDTO;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.flow.warning.repository.TakeoutDiscountRepository;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class TakeoutDiscountService extends AbstractEarlyWarningService<TakeoutDiscount> {

    private static final Logger logger = LoggerFactory.getLogger(TakeoutDiscountService.class);

    private final TakeoutDiscountRepository takeoutDiscountRepository;


    @Override
    public void takeoutDiscountWarning(LocalDateTime dateTime) {
        Pair<List<TakeoutDiscount>, List<TakeoutDiscount>> pair = super.batchLaunchProcess(dateTime, WarningType.TAKEOUT_DISCOUNT);
        logger.info("{}预警发起完成：{}", WarningType.TAKEOUT_DISCOUNT.getType(), pair.getFirst().size());

    }


    @Override
    public Pair<Class<TakeoutDiscount>, List<TakeoutDiscount>> getNewData(LocalDateTime dateTime, WarningType type) {
        Time time = Time.day(dateTime);
        List<TakeoutDiscountDTO> takeoutDiscountDTOS = takeoutDiscountRepository.findByTime(time.getCurrent());
        Map<String, TakeoutDiscount> processMap = new HashMap<>();
        for (TakeoutDiscountDTO discount : takeoutDiscountDTOS) {
            String shopId = discount.getShopId();
            LocalDateTime accountDate = discount.getDate();
            if (StringUtils.isBlank(shopId) || accountDate == null) {
                continue;
            }
            String key = shopId + "-" + accountDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (!processMap.containsKey(key)) {
                TakeoutDiscount takeoutDiscount = new TakeoutDiscount();
                takeoutDiscount.setDate(accountDate);
                takeoutDiscount.setShopId(shopId);
                takeoutDiscount.setAbnormalType(type.getType());
                takeoutDiscount.setAbnormalField(type.getType());
                takeoutDiscount.setTakeoutDiscountList(new ArrayList<>());
                processMap.put(key, takeoutDiscount);
            }
            TakeoutDiscount.TakeoutDiscountItem takeoutDiscountItem = new TakeoutDiscount.TakeoutDiscountItem();
            takeoutDiscountItem.setWmOrderCode(discount.getWmOrderCode());
            takeoutDiscountItem.setOrderSource(discount.getOrderSource());
            takeoutDiscountItem.setDiscountMoney(discount.getDiscountMoney());
            takeoutDiscountItem.setActivityName(discount.getActivityName());
            processMap.get(key).getTakeoutDiscountList().add(takeoutDiscountItem);
        }
        return Pair.of(TakeoutDiscount.class, relateShopRelation(new ArrayList<>(processMap.values())));
    }

}
