package com.astenamic.new_discovery.biz.flow.warning.service;

import com.astenamic.new_discovery.biz.flow.warning.entity.FoodBackReason;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.FoodBackReasonDTO;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.flow.warning.repository.FoodBackReasonRepository;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class FoodBackWarningService extends AbstractEarlyWarningService<FoodBackReason> {

    private static final Logger logger = LoggerFactory.getLogger(FoodBackWarningService.class);

    private final FoodBackReasonRepository foodBackReasonRepository;


    @Override
    public void foodBackWarning(LocalDateTime dateTime) {
        Pair<List<FoodBackReason>, List<FoodBackReason>> pair = super.batchLaunchProcess(dateTime, WarningType.FOOD_BACK_REASON);
        logger.info("{}预警发起完成：{}", WarningType.FOOD_BACK_REASON.getType(), pair.getFirst().size());
    }


    @Override
    public Pair<Class<FoodBackReason>, List<FoodBackReason>> getNewData(LocalDateTime dateTime, WarningType type) {
        Time time = Time.day(dateTime);
        List<FoodBackReasonDTO> foodBackReasonDTOS = foodBackReasonRepository.findByTime(time.getCurrent());

        Map<String, FoodBackReason> processMap = new HashMap<>();
        for (FoodBackReasonDTO reason : foodBackReasonDTOS) {
            String shopId = reason.getShopId();
            LocalDateTime accountDate = reason.getDate();
            if (StringUtils.isBlank(shopId) || accountDate == null) {
                continue;
            }
            String key = shopId + "-" + accountDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (!processMap.containsKey(key)) {
                FoodBackReason process = new FoodBackReason();
                process.setDate(accountDate);
                process.setShopId(shopId);
                process.setAbnormalType(type.getType());
                process.setAbnormalField(type.getType());
                process.setFoodBackList(new ArrayList<>());
                process.setRetreatRate(reason.getRetreatRate());
                process.setRetreatAllMoney(reason.getRetreatAllMoney());
                process.setAbnormalValue(reason.getRetreatRate());
                processMap.put(key, process);
            }
            FoodBackReason.FoodBack foodBack = new FoodBackReason.FoodBack();
            foodBack.setFoodName(reason.getFoodName());
            foodBack.setRetreatRemark(reason.getRetreatRemark());
            foodBack.setRetreatMoney(reason.getRetreatMoney());
            foodBack.setRetreatQty(reason.getRetreatQty());
            processMap.get(key).getFoodBackList().add(foodBack);
        }
        return Pair.of(FoodBackReason.class, relateShopRelation(new ArrayList<>(processMap.values())));
    }
}
