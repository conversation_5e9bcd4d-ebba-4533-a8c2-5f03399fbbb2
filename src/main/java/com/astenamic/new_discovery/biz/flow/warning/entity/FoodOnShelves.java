package com.astenamic.new_discovery.biz.flow.warning.entity;

import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-21EF7B2CF99B49689B6C5F7BD08893C83560", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class FoodOnShelves extends EarlyWarningProcess {

    @FormField("dateField_m418kt5q")
    private LocalDateTime date;

    @FormField("tableField_m8sfdl6g")
    private List<FoodItem> foodItemList;

    @Data
    @FormEntity("tableField_m8sfdl6g")
    public static class FoodItem {

        @FormField("selectField_m9taowrg")
        private String platform;

        @FormField("textField_m9taowrh")
        private String foodName;

        @FormField("dateField_ma4x34tm")
        private LocalDateTime onShelfTime;
    }
}
