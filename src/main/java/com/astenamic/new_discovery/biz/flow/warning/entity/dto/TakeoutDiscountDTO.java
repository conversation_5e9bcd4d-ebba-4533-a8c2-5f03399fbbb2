package com.astenamic.new_discovery.biz.flow.warning.entity.dto;


import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity(name = "yida_takeout_discount")
public class TakeoutDiscountDTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "shop_id")
    private String shopId;

    @Column(name = "order_date")
    private LocalDateTime date;

    @Column(name = "wm_code")
    private String wmOrderCode;

    @Column(name = "activity_name")
    private String activityName;

    @Column(name = "shop_discount_money")
    private Float discountMoney;

    @Column(name = "order_source")
    private String orderSource;
}
