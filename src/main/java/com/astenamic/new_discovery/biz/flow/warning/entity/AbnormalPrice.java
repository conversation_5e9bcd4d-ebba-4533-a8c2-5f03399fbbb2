package com.astenamic.new_discovery.biz.flow.warning.entity;


import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-93D4F63AEECA4E9DBA1B22A341854F97VGA4", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class AbnormalPrice extends YidaFlowObject {

    // 货品名称
    @FormField("textField_m89tkcnq")
    private String good_name;

    // 货品编码
    @FormField("textField_m89tkcnr")
    private String good_sno;

    // 货品品牌
    @FormField("textField_m89tkcns")
    private String good_brand;

    // 规格
    @FormField("textField_m89tkcnu")
    private String std;


    // 货品小类
    @FormField("textField_m89tkcnt")
    private String goodtype_name;

    // 货品大类
    @FormField("textField_m89tkcnv")
    private String fgoodtype_name;

    // 订货单位名称
    @FormField("textField_m89tkcnw")
    private String applyguname;

    // 当前执行价格
    @FormField("numberField_m89tkco2")
    private Float nowprice;

    // 下次定价
    @FormField("numberField_m89tkco3")
    private Float uprice;

    // 价格开始生效日期
    @FormField("dateField_m89tkco4")
    private LocalDateTime startdate;

    // 价格结束日期
    @FormField("dateField_m89tkco5")
    private LocalDateTime enddate;

    // 环比调价比率
    @FormField("numberField_m89tkco6")
    private Float momratio;

    // 同比调价比率
    @FormField("numberField_m89tkco7")
    private Float yoyratio;

    // 审核时间
    @FormField("dateField_m96vf8dv")
    private LocalDateTime atime;

}
