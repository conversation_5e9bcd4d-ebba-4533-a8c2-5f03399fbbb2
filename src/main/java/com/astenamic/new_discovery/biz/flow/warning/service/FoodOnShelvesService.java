package com.astenamic.new_discovery.biz.flow.warning.service;

import com.astenamic.new_discovery.biz.flow.warning.entity.FoodOnShelves;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.takeout.service.TakeoutProductService;
import com.astenamic.new_discovery.external.modal.dto.takeout.ProductDTO;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class FoodOnShelvesService extends AbstractEarlyWarningService<FoodOnShelves> {

    private static final Logger logger = LoggerFactory.getLogger(FoodOnShelvesService.class);

    private final TakeoutProductService takeoutProductService;

    @Override
    protected Pair<Class<FoodOnShelves>, List<FoodOnShelves>> getNewData(LocalDateTime dateTime, WarningType type) {
        return null;
    }

    @Override
    public void takeoutFoodOnShelvesWarning() {

        Map<String, List<ProductDTO>> onShelfFood = takeoutProductService.getOnShelfFood();

        List<FoodOnShelves> newData = new ArrayList<>();

        for (Map.Entry<String, List<ProductDTO>> entry : onShelfFood.entrySet()) {
            FoodOnShelves foodOnShelves = new FoodOnShelves();
            foodOnShelves.setShopId(entry.getKey());
            foodOnShelves.setFoodItemList(new ArrayList<>());
            foodOnShelves.setDate(LocalDateTime.now());

            for (ProductDTO productDTO : entry.getValue()) {
                FoodOnShelves.FoodItem foodItem = new FoodOnShelves.FoodItem();
                foodItem.setPlatform(productDTO.getPlatform());
                foodItem.setFoodName(productDTO.getProductName());
                foodItem.setOnShelfTime(productDTO.getOnShelfTime());
                foodOnShelves.getFoodItemList().add(foodItem);
            }

            newData.add(foodOnShelves);
        }

        this.relateShopRelation(newData);

        for (FoodOnShelves group : newData) {
            this.yiDaSession.processSave(group, FoodOnShelves.class, "", "75603689");
        }

    }
}
