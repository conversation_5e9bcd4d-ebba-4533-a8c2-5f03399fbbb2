package com.astenamic.new_discovery.biz.flow.warning.entity.dto;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Entity(name = "yida_flow_employee_meal")
public class EmployeeMealDTO {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "shop_id")
    private String shopId;

    @Column(name = "account_date")
    private LocalDateTime date;

    @Column(name = "total_money")
    private Float totalMoney;

    @Column(name = "people_count")
    private Float peopleCount;

    @Column(name = "norm_money")
    private Float normMoney;

    /**
     * 是否需要预警
     *
     * @return true: 需要预警
     * false: 不需要预警
     */
    public boolean isNeedWarn() {
        // 如果normMoney为null或等于0，属于程序异常数据，不需要预警
        if (normMoney == null || normMoney == 0f) {
            return false;
        }
        // 如果totalMoney为null或等于0，需要预警
        if (totalMoney == null || totalMoney == 0f) {
            return true;
        }
        // 只有当totalMoney大于normMoney时需要预警
        return totalMoney > normMoney * 1.2;
    }

}
