package com.astenamic.new_discovery.biz.flow.warning.repository;

import com.astenamic.new_discovery.biz.flow.warning.entity.dto.MemberConsumptionCountDTO;
import com.astenamic.new_discovery.biz.wlift.entity.ConsumeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MemberConsumptionCountRepository
        extends JpaRepository<ConsumeRecord, Long> {

    @Query(value = """
            SELECT
                shop_Id AS "shopId",
                DATE(A.pay_time) AS "payDate",
                cno,
                grade_name AS "gradeName",
                COUNT(*) AS "payCount", \s
                STRING_AGG(TO_CHAR(A.pay_time, 'YYYY-MM-DD HH24:MI:SS'), '，') AS "payTimeList",
                STRING_AGG((actual_fee::FLOAT / 100)::TEXT || '元', '，') AS "actualMoneyList",
                STRING_AGG((total_fee::FLOAT / 100)::TEXT || '元', '，') AS "totalMoneyList",
                STRING_AGG(DISTINCT A.phone_number, '，') AS "phoneNumber",
                (SELECT COUNT(*)
                 FROM xfx_wlift_consume_record b
                 WHERE b.cno = A.cno
                   AND to_char(b.pay_time, 'YYYY-MM') = to_char(CURRENT_DATE, 'YYYY-MM')
                   AND b.TYPE = '2' \s
                   AND b.total_fee > 3000\s
                ) AS "monthPayCount"\s
            FROM xfx_wlift_consume_record A
            LEFT JOIN xfx_shop_mapping b ON A.sid = b.crm_id
            WHERE A.TYPE = '2'
              AND A.total_fee > 3000
              AND to_char(A.pay_time, 'YYYY-MM-DD') ~ :dates
            GROUP BY
                DATE(A.pay_time),
                cno,
                shop_id,
                b.pingzhi_name,
                grade_name
            HAVING COUNT(*) >= 2;
            """,
            nativeQuery = true)
    List<MemberConsumptionCountDTO> findByTime(@Param("dates") String dates);


}
