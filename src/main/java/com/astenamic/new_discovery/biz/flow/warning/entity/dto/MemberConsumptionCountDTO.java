package com.astenamic.new_discovery.biz.flow.warning.entity.dto;


import java.time.LocalDate;

/**
 * 会员消费次数聚合查询 DTO
 */
public interface MemberConsumptionCountDTO {
    LocalDate getPayDate();

    String getCno();

    String getShopId();

    String getGradeName();

    Float getPayCount();

    Float getMonthPayCount();

    String getPayTimeList();

    String getActualMoneyList();

    String getTotalMoneyList();

    String getPhoneNumber();

}