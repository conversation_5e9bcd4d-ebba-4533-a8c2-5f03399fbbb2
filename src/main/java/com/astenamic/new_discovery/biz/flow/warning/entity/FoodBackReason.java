package com.astenamic.new_discovery.biz.flow.warning.entity;

import com.astenamic.new_discovery.biz.flow.warning.entity.base.EarlyWarningProcess;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-B737A34937DD45B8BA354D8EC435C6C17TLR", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class FoodBackReason extends EarlyWarningProcess {

    @FormField("numberField_m8zhft2j")
    private Float retreatRate;

    @FormField("numberField_m8zhft2k")
    private Float retreatAllMoney;

    @FormField("tableField_m8sfdl6g")
    private List<FoodBack> foodBackList;

    @Data
    @FormEntity("tableField_m8sfdl6g")
    public static class FoodBack{

        @FormField("textField_md71xasr")
        private String foodName;

        @FormField("textField_m8sfdl6h")
        private String retreatRemark;

        @FormField("numberField_m8sfdl6i")
        private Float retreatMoney;

        @FormField("numberField_m8sfdl6j")
        private Float retreatQty;
    }
}
