package com.astenamic.new_discovery.biz.flow.warning.service.base;

import java.time.LocalDateTime;

public interface EarlyWarningService {

    /**
     * 退菜类型预警
     */
    void foodBackWarning(LocalDateTime dateTime);

    /**
     * 外卖代金券预警
     */
    void takeoutDiscountWarning(LocalDateTime dateTime);

    /**
     * 员工餐预警
     */
    void employeeMealWarning(LocalDateTime dateTime);

    /**
     * 外卖上下架预警
     */
    void takeoutFoodOnShelvesWarning();

    /**
     * 会员消费预警
     *
     * @param dateTime
     */
    void memberConsumptionWarning(LocalDateTime dateTime);
}
