package com.astenamic.new_discovery.biz.flow.warning.service;

import com.astenamic.new_discovery.biz.flow.warning.entity.FoodBackReason;
import com.astenamic.new_discovery.biz.flow.warning.entity.MemberConsumptionCount;
import com.astenamic.new_discovery.biz.flow.warning.entity.TakeoutDiscount;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.MemberConsumptionCountDTO;
import com.astenamic.new_discovery.biz.flow.warning.entity.dto.TakeoutDiscountDTO;
import com.astenamic.new_discovery.biz.flow.warning.enums.WarningType;
import com.astenamic.new_discovery.biz.flow.warning.repository.MemberConsumptionCountRepository;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.report.shop.utils.Time;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class MemberConsumptionCountService extends AbstractEarlyWarningService<MemberConsumptionCount> {

    private final MemberConsumptionCountRepository memberConsumptionCountRepository;

    /**
     * 会员消费记录预警
     */
    @Override
    public void memberConsumptionWarning(LocalDateTime dateTime) {
        Pair<List<MemberConsumptionCount>, List<MemberConsumptionCount>> pair = super.batchLaunchProcess(dateTime, WarningType.MEMBER_CONSUME_COUNT);
        log.info("{}预警发起完成：{}", WarningType.MEMBER_CONSUME_COUNT.getType(), pair.getFirst().size());
    }

    @Override
    public Pair<Class<MemberConsumptionCount>, List<MemberConsumptionCount>> getNewData(LocalDateTime dateTime, WarningType type) {
        Time time = Time.day(dateTime);
        List<MemberConsumptionCountDTO> memberConsumptionCountDTOs = memberConsumptionCountRepository.findByTime(time.getCurrent());
        Map<String, MemberConsumptionCount> processMap = new HashMap<>();
        for (MemberConsumptionCountDTO dto : memberConsumptionCountDTOs) {
            String shopId = dto.getShopId();
            if (StringUtils.isBlank(shopId)) {
                continue;
            }

            processMap.computeIfAbsent(shopId, key -> {
                MemberConsumptionCount count = new MemberConsumptionCount();
                count.setDate(dto.getPayDate().atStartOfDay());
                count.setShopId(shopId);
                count.setAbnormalType(type.getType());
                count.setAbnormalField(type.getType());
                count.setMemberConsumptionCountList(new ArrayList<>());
                return count;
            });

            MemberConsumptionCount.MemberConsumptionCountItem item = new MemberConsumptionCount.MemberConsumptionCountItem();
            item.setCno(dto.getCno());
            item.setGradeName(dto.getGradeName());
            item.setPayCount(dto.getPayCount());
            item.setMonthPayCount(dto.getMonthPayCount());
            item.setPayTimeList(dto.getPayTimeList());
            item.setActualMoneyList(dto.getActualMoneyList());
            item.setTotalMoneyList(dto.getTotalMoneyList());
            item.setPhoneNumber(dto.getPhoneNumber());


            processMap.get(shopId).getMemberConsumptionCountList().add(item);
        }
        return Pair.of(MemberConsumptionCount.class, relateShopRelation(new ArrayList<>(processMap.values())));
    }

}
