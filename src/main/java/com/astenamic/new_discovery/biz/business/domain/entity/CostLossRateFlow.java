package com.astenamic.new_discovery.biz.business.domain.entity;

import com.astenamic.new_discovery.biz.business.domain.valueobject.LossRateItem;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 成本损耗率表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-A6303576FB8641EC9B9E45EED9A57EA55NYM",
        appType = "APP_SW1NM01KIYJPSUV9CFTN",
        sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class CostLossRateFlow extends YidaFlowObject {

    @FormField("textField_m4qf9iqi")
    private String shopId;

    // 门店
    @FormField("departmentSelectField_m4qd49xe")
    @Transient
    private List<String> shopSysId;

    // 厨师长
    @FormField("employeeField_m4s33lup")
    @Transient
    private List<String> chiefSysId;

    // 督导
    @FormField("employeeField_m56h4qq9")
    private List<String> supervisorSysId;

    // 对应会计
    @FormField("employeeField_m8mur2p8")
    private List<String> accountantSysId;

    // 周期
    @FormField("textField_mbkh4jgu")
    private String dateRange;

    // 日期
    @FormField("dateField_mbkki08g")
    private LocalDateTime date;

    // 门店经理id
    @FormField("employeeField_m56h4qqa")
    private List<String> shopManagerSysId;

    // 战区司令id
    @FormField("employeeField_m56h4qqb")
    private List<String> batAreaPeopleSysId;

    // 战区id
    @FormField("departmentSelectField_m56h4qqc")
    private List<String> batAreaSysId;

    // 品牌id
    @FormField("departmentSelectField_m56h4qqd")
    private List<String> brandSysId;

    // 区域
    @FormField("departmentSelectField_m56h4qqe")
    private List<String> areaSysId;


    @FormField("tableField_m3xv5p86")
    private List<LossRateItem> lossRateList;

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }


    public void setShopManagerSysId(String shopManagerSysId) {
        if (shopManagerSysId != null) {
            this.shopManagerSysId = List.of(shopManagerSysId);
        }
    }

    public void setAccountantSysId(String accountantSysId) {
        if (accountantSysId != null) {
            this.accountantSysId = List.of(accountantSysId);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }

}
