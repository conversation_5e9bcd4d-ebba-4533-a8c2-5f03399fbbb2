package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PatrolDetailDao extends JpaRepository<PatrolDetail, Long> {

    /**
     * 根据巡店记录ID（businessId）查询详情
     *
     * @param businessId 巡店记录ID
     */
    List<PatrolDetail> findByBusinessId(Long businessId);

    /**
     * 清空指定 businessId 的详情
     */
    @Modifying(clearAutomatically = true)
    @Query("delete from xfx_cool_patrol_detail d where d.businessId = :businessId")
    void deleteByBusinessId(@Param("businessId") Long businessId);
}
