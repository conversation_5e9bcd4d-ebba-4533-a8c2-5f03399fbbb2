package com.astenamic.new_discovery.biz.business.domain.entity;

import com.astenamic.new_discovery.biz.business.domain.valueobject.MaterialReport;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.Transient;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@FormEntity("FORM-2594A46DA25740D48E9FF638DED4431DNVEY")
public class MaterialShopReport extends YidaObject {




    @FormField("textField_m4qf9iqi")
    private String shopId;

    // 门店
    @FormField("departmentSelectField_m4qd49xe")
    @Transient
    private List<String> shopSysId;

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }

    // 厨师长
    @FormField("employeeField_m4s33lup")
    @Transient
    private List<String> chiefSysId;

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null) {
            this.chiefSysId = List.of(chiefSysId);
        }
    }

    // 日期
    @FormField("dateField_m4qf9iqh")
    private LocalDateTime date;

    // 	实际成本
    @FormField("tableField_m3xv5p86")
    private List<MaterialReport> materialReports;

    public MaterialShopReport(String shopId, LocalDateTime date) {
        this.shopId = shopId;
        this.date = date;
    }

    // 门店经理id
    @FormField("employeeField_m56h4qqa")
    private List<String> shopManagerSysId;

    public void setShopManagerSysId(String shopManagerSysId) {
        if (shopManagerSysId != null) {
            this.shopManagerSysId = List.of(shopManagerSysId);
        }
    }

    // 战区司令id
    @FormField("employeeField_m56h4qqb")
    private List<String> batAreaPeopleSysId;

    public void setBatAreaPeopleSysId(String batAreaPeopleSysId) {
        if (batAreaPeopleSysId != null) {
            this.batAreaPeopleSysId = List.of(batAreaPeopleSysId);
        }
    }

    // 战区id
    @FormField("departmentSelectField_m56h4qqc")
    private List<String> batAreaSysId;

    public void setBatAreaSysId(String batAreaSysId) {
        if (batAreaSysId != null) {
            this.batAreaSysId = List.of(batAreaSysId);
        }
    }

    // 品牌id
    @FormField("departmentSelectField_m56h4qqd")
    private List<String> brandSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    // 区域
    @FormField("departmentSelectField_m56h4qqe")
    private List<String> areaSysId;

    public void setAreaSysId(String areaSysId) {
        if (areaSysId != null) {
            this.areaSysId = List.of(areaSysId);
        }
    }

    // 督导
    @FormField("employeeField_m56h4qq9")
    private List<String> supervisorSysId;

    public void setSupervisorSysId(String supervisorSysId) {
        if (supervisorSysId != null) {
            this.supervisorSysId = List.of(supervisorSysId);
        }
    }


}
