package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import com.astenamic.new_discovery.biz.business.domain.repository.PatrolRecordRepository;
import com.astenamic.new_discovery.biz.business.application.mapper.PatrolDetailMapper;
import com.astenamic.new_discovery.biz.business.application.mapper.PatrolRecordMapper;
import com.astenamic.new_discovery.external.client.cool.vision.service.PatrolDetailListService;
import com.astenamic.new_discovery.external.client.cool.vision.service.PatrolListService;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolRecordDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 巡店记录同步应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PatrolRecordAppService {

    /* -------- 依赖注入 -------- */
    private final PatrolListService       patrolListService;
    private final PatrolDetailListService patrolDetailListService;

    private final PatrolRecordMapper      patrolRecordMapper;
    private final PatrolDetailMapper      patrolDetailMapper;

    private final PatrolRecordRepository  patrolRecordRepository;

    /**
     * 同步指定日期（targetTime 当天）的巡店记录及详情到本地库
     *
     * @param targetTime 需要同步的目标日期（任意一天的时间，即取该天 00:00-23:59）
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncPatrolRecord(LocalDateTime targetTime) {

        if (targetTime == null) {
            log.warn("【巡店同步】targetTime 为空，直接返回");
            return;
        }

        log.info("【巡店同步】开始同步日期：{}", targetTime.toLocalDate());

        /* -------- 1. 拉取主表 DTO 列表 -------- */
        List<PatrolRecordDTO> recordDTOs = patrolListService.getPatrolList(targetTime);
        if (recordDTOs.isEmpty()) {
            log.info("【巡店同步】目标日无巡店记录");
            return;
        }

        /* -------- 2. DTO → Entity，并拉取详情 -------- */
        List<PatrolRecord> entities = new ArrayList<>(recordDTOs.size());

        for (PatrolRecordDTO recDto : recordDTOs) {

            PatrolRecord entity = patrolRecordMapper.toEntity(recDto);

            /* 2.1 拉详情 DTO 列表 */
            List<PatrolDetailDTO> detailDTOs =
                    patrolDetailListService.getPatrolDetailList(recDto.getRecordId(), targetTime);

            /* 2.2 DTO → Entity */
            List<PatrolDetail> detailEntities = patrolDetailMapper.toEntityList(detailDTOs);

            /* 2.3 绑定到主表 */
            entity.setPatrolDetailList(detailEntities);

            entities.add(entity);
        }

        /* -------- 3. 批量幂等保存 -------- */
        patrolRecordRepository.saveAll(entities);

        log.info("【巡店同步】完成，同步条数：{}", entities.size());
    }
}
