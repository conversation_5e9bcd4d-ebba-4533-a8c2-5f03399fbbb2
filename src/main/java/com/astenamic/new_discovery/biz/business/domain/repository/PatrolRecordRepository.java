package com.astenamic.new_discovery.biz.business.domain.repository;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;

import java.time.LocalDateTime;
import java.util.List;

public interface PatrolRecordRepository {

    /**
     * 查找所有 PatrolRecord 实体
     *
     * @return 所有 PatrolRecord 实体的列表
     */
    List<PatrolRecord> findByTime(LocalDateTime targetTime) ;

    /**
     * 根据巡店id查找 PatrolRecord 实体
     *
     * @return 所有 PatrolRecord 实体的列表
     */
    PatrolRecord findByRecordId(Long recordId);


    /**
     * 保存单个 PatrolRecord 实体
     *
     * @param entity 要保存的实体
     * @return 保存后的实体
     */
    String save(PatrolRecord entity);

    /**
     * 批量保存 PatrolRecord 实体
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体列表
     */
    List<String> saveAll(List<PatrolRecord> entities);
}
