package com.astenamic.new_discovery.biz.business.domain.repository;


import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;

import java.util.List;

public interface ShopRelationRepository {


    /**
     * 查找所有 ShopRelation 实体
     *
     * @return 所有 ShopRelation 实体的列表
     */
    List<ShopRelation> findAll();

    /**
     * 查找所有已开启的 ShopRelation 实体
     *
     * @return 所有已开启的 ShopRelation 实体的列表
     */
    List<ShopRelation> findOpen();

    /**
     * 保存单个 ShopRelation 实体
     *
     * @param entity 要保存的实体
     * @return 保存后的实体
     */
    String save(ShopRelation entity);

    /**
     * 批量保存 ShopRelation 实体
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体列表
     */
    List<String> saveAll(List<ShopRelation> entities);

    /**
     * 批量删除 ShopRelation 实体
     *
     * @param entities 要删除的实体列表
     */
    void deleteAll(List<ShopRelation> entities);
}
