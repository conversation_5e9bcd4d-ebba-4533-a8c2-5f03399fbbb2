package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dto;

public interface MaterialReportDTO {
    /**
     * 门店 ID
     */
    String getShopId();

    /**
     * 门店名称
     */
    String getShopName();

    /**
     * 合并后物料编码
     */
    String getMaterialNoHb();

    /**
     * 合并后物料名称
     */
    String getMaterialNameHb();

    /**
     * 统一物料大类
     */
    String getMaterialTypeBigTy();

    /**
     * 统一物料小类
     */
    String getMaterialTypeSmallTy();

    /**
     * 合并后库存单位
     */
    String getKcUnitNameHb();

    /**
     * 正序差异排名（最省）
     */
    Integer getAscRank();

    /**
     * 逆序差异排名（最亏）
     */
    Integer getDescRank();

    /**
     * 实际成本
     */
    Float getSjCost();

    /**
     * 理论成本
     */
    Float getLlCost();

    /**
     * 差异成本（实际 - 理论）
     */
    Float getCyCost();

    /**
     * 损耗率（%）
     */
    Float getLossRatio();

    /**
     * 实际用量
     */
    Float getSjQtyTy();

    /**
     * 理论用量
     */
    Float getLlQtyTy();

    /**
     * 差异用量
     */
    Float getCyQtyTy();

    /**
     * 配送入库量
     */
    Float getPsrkQtyTy();

    /**
     * 采购入库量
     */
    Float getCgrkQtyTy();

    /**
     * 期初库存量
     */
    Float getKcQcQtyTy();

    /**
     * 期末库存量
     */
    Float getKcQmQtyTy();

    /**
     * 调拨出库量
     */
    Float getDbckQtyTy();

    /**
     * 报损出库量
     */
    Float getBsckQtyTy();

    /**
     * 调拨出库成本
     */
    Float getDbckCost();

    /**
     * 报损出库成本
     */
    Float getBsckCost();
}
