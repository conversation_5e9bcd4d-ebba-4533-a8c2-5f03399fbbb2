package com.astenamic.new_discovery.biz.business.domain.entity.cool;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 区域信息表
 */
@Data
@Entity(name = "xfx_cool_region")
public class Region {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 删除标识：0-正常 1-删除
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag = "0";

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @PrePersist
    protected void preInsert() {
        this.createTime = this.updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 区域ID（原 DTO.id）
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 区域名称
     */
    @Column(name = "name", length = 128)
    private String name;

    /**
     * 上级节点ID
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 第三方关联ID
     */
    @Column(name = "syn_ding_dept_id", length = 64)
    private String synDingDeptId;

    /**
     * 区域类型：root/path/store
     */
    @Column(name = "region_type", length = 16)
    private String regionType;

    /**
     * 区域路径
     */
    @Column(name = "region_path", length = 255)
    private String regionPath;

    /**
     * 门店ID
     */
    @Column(name = "store_id", length = 64)
    private String storeId;
}

