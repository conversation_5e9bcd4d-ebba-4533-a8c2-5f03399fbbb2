package com.astenamic.new_discovery.biz.business.domain.valueobject;


import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

/**
 * 门店每日物料损耗
 */
@Data
@FormEntity("tableField_m3xv5p86")
public class MaterialReport  {

    /**
     * 门店 ID
     */
    private String shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 合并后物料编码
     */
    @FormField("textField_m3xv5p8a")
    private String materialNoHb;

    /**
     * 合并后物料名称
     */
    @FormField("textField_m3xv5p8b")
    private String materialNameHb;

    /**
     * 统一物料大类
     */
    @FormField("textField_m3xv5p8d")
    private String materialTypeBigTy;

    /**
     * 统一物料小类
     */
    @FormField("textField_m3xv5p8e")
    private String materialTypeSmallTy;

    /**
     * 合并后库存单位
     */
    @FormField("textField_m3xv5p8f")
    private String kcUnitNameHb;

    /**
     * 正序差异排名（最省）
     */
    private Integer ascRank;

    /**
     * 逆序差异排名（最亏）
     */
    private Integer descRank;

    /**
     * 实际成本
     */
    @FormField("numberField_m3xv5p8g")
    private Float sjCost;

    /**
     * 理论成本
     */
    @FormField("numberField_m3xv5p8h")
    private Float llCost;

    /**
     * 差异成本（实际 - 理论）
     */
    @FormField("numberField_m499j963")
    private Float cyCost;

    /**
     * 损耗率（%）
     */
    @FormField("numberField_m3xv5p8i")
    private Float lossRatio;

    /**
     * 实际用量（统一库存单位）
     */
    @FormField("numberField_m64t5zzm")
    private Float sjQtyTy;

    /**
     * 理论用量（统一库存单位）
     */
    @FormField("numberField_m64t5zzn")
    private Float llQtyTy;

    /**
     * 差异用量（实际 - 理论）
     */
    @FormField("numberField_m64t5zzo")
    private Float cyQtyTy;

    /**
     * 配送入库量
     */
    @FormField("numberField_m64t5zzl")
    private Float psrkQtyTy;

    /**
     * 采购入库量
     */
    @FormField("numberField_m64t5zzk")
    private Float cgrkQtyTy;

    /**
     * 期初库存量
     */
   @FormField("numberField_m64t5zzi")
    private Float kcQcQtyTy;

    /**
     * 期末库存量
     */
    @FormField("numberField_m64t5zzj")
    private Float kcQmQtyTy;

    /**
     * 调拨出库量
     */
    @FormField("numberField_m741l50w")
    private Float dbckQtyTy;

    /**
     * 报损出库量
     */
    @FormField("numberField_m741l50y")
    private Float bsckQtyTy;

    /**
     * 调拨出库成本
     */
    @FormField("numberField_m741l50x")
    private Float dbckCost;

    /**
     * 报损出库成本
     */
    @FormField("numberField_m741l50z")
    private Float bsckCost;
}
