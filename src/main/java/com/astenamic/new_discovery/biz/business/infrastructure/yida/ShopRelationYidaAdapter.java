package com.astenamic.new_discovery.biz.business.infrastructure.yida;

import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.domain.repository.ShopRelationRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ShopRelationYidaAdapter extends AbstractFormService<ShopRelation> implements ShopRelationRepository {

    public ShopRelationYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(ShopRelation.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }


    @Override
    public List<ShopRelation> findAll() {
        return super.getFormsByCond(SearchCondition.builder().get());
    }

    @Override
    public List<ShopRelation> findOpen() {
        return super.getFormsByCond(
                SearchCondition.builder()
                        .textEq(ShopRelation.class, ShopRelation::getClosed, "否", "+")
                        .get()
        );
    }

    @Override
    public String save(ShopRelation entity) {
        return super.save(entity);
    }

    @Override
    public List<String> saveAll(List<ShopRelation> entities) {
        return super.batchSave(entities);
    }

    @Override
    public void deleteAll(List<ShopRelation> entities) {
        // 宜搭适配器不支持删除操作，因为宜搭是数据源，不应该删除宜搭中的数据
        // 这个方法留空，实际删除操作只在JPA适配器中执行
    }
}
