package com.astenamic.new_discovery.biz.business.application.mapper;


import com.astenamic.new_discovery.biz.business.domain.entity.cool.Region;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.RegionDTO;
import org.mapstruct.*;

import java.util.List;

/**
 * 区域信息 Mapper（DTO ⇄ Entity）
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RegionMapper {


    @Mappings({
            @Mapping(source = "id", target = "regionId"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "delFlag", constant = "0"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    Region toEntity(RegionDTO dto);

    @Mappings({
            @Mapping(source = "regionId", target = "id")
    })
    RegionDTO toDTO(Region entity);

    List<Region> toEntityList(List<RegionDTO> dtoList);

    List<RegionDTO> toDTOList(List<Region> entityList);
}

