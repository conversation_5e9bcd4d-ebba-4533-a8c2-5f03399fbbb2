package com.astenamic.new_discovery.biz.business.domain.entity;

import com.astenamic.new_discovery.biz.business.infrastructure.jpa.converter.StringListConverter;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 门店关系表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "xfx_shop_relation", uniqueConstraints = @UniqueConstraint(name = "uq_xfx_shop_relation_shop_id", columnNames = "shop_id"))
@FormEntity(value = "FORM-98A81256D81F4CA29C2BA0700428BDDAGXJV", appType = "APP_SW1NM01KIYJPSUV9CFTN", sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K")
public class ShopRelation extends YidaObject {

    /* 主键 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /* 门店ID */
    @FormField("textField_m3xt9z9d")
    @Column(name = "shop_id", length = 64, nullable = false)
    private String shopId;

    /* 酷巡店ID */
    @FormField("textField_mdfi3z7x")
    @Column(name = "kuxundian_id", length = 64)
    private String kuxundianId;

    /* 奥琦玮ID */
    @FormField("textField_mdfi3z84")
    @Column(name = "aoqiwei_id", length = 64)
    private String aoqiweiId;

    /* 抖音ID */
    @FormField("textField_mdfi3z82")
    @Column(name = "douyin_id", length = 64)
    private String douyinId;

    /* 品质ID */
    @FormField("textField_mdfi3z83")
    @Column(name = "pinzhi_id", length = 64)
    private String pinzhiId;

    /* 微生活ID */
    @FormField("textField_mdfi3z81")
    @Column(name = "wlift_id", length = 64)
    private String wliftId;

    /* 大众点评ID */
    @FormField("textField_mdfi3z80")
    @Column(name = "dazhongdianping_id", length = 64)
    private String dazhongdianpingId;

    /* 饿了么ID */
    @FormField("textField_mdfi3z7y")
    @Column(name = "eleme_id", length = 64)
    private String elemeId;

    /* 美团ID */
    @FormField("textField_mdfi3z7z")
    @Column(name = "meituan_id", length = 64)
    private String meituanId;

    /* 门店财务系统名称 */
    @FormField("textField_mcijmn9p")
    @Column(name = "shop_finance_name", length = 128)
    private String shopFinanceName;

    /* 门店ABC名称 */
    @FormField("textField_mcijmn9o")
    @Column(name = "shop_abc_name", length = 128)
    private String shopAbcName;

    /* 品牌 */
    @FormField("departmentSelectField_m41c2xhs")
    @Column(name = "brand_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> brandSysId;

    /* 品牌文本 */
    @FormField("textField_m50r6ypj")
    @Column(name = "brand_text")
    private String brandText;

    /* 战区 */
    @FormField("departmentSelectField_m419fef2")
    @Column(name = "bat_area_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> batAreaSysId;

    /* 战区文本 */
    @FormField("textField_md436049")
    @Column(name = "bat_area_text")
    private String batAreaText;

    /* 战区司令 */
    @FormField("employeeField_m3wuckey")
    @Column(name = "bat_area_people_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> batAreaPeopleSysId;

    /* 区域 */
    @FormField("departmentSelectField_m419fef3")
    @Column(name = "area_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> areaSysId;

    /* 区域文本 */
    @FormField("textField_md43604a")
    @Column(name = "area_text")
    private String areaText;

    /* 省份 */
    @FormField("selectField_mbr9phlj")
    @Column(name = "province", length = 64)
    private String province;

    /* 督导 */
    @FormField("employeeField_m3wuckf0")
    @Column(name = "supervisor_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> supervisorSysId;

    /* 门店 */
    @FormField("departmentSelectField_m419fef4")
    @Column(name = "shop_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> shopSysId;

    /* 门店文本 */
    @FormField("textField_m50qvvy2")
    @Column(name = "shop_text")
    private String shopText;

    /* 门店奥琦玮名称 */
    @FormField("textField_m70df5av")
    @Column(name = "shop_aoqiwei_name", length = 128)
    private String shopAoqiweiName;

    /* 门店群ID */
    @FormField("textField_m3zmsvkm")
    @Column(name = "shop_group_id", length = 64)
    private String shopGroupId;


    /* 门店经理 */
    @FormField("employeeField_m3wtrn8f")
    @Column(name = "shop_manager_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> shopManagerSysId;

    /* 厨师长 */
    @FormField("employeeField_m3wuckf2")
    @Column(name = "chief_sys_id", length = 64)
    @Convert(converter = StringListConverter.class)
    private List<String> chiefSysId;

    /* 是否闭店 */
    @FormField("textField_m3wtrn8d")
    @Column(name = "closed", length = 16)
    private String closed;

    /* 总账会计 */
    @FormField("employeeField_m88jm845")
    @Column(name = "accountant", columnDefinition = "text")
    @Convert(converter = StringListConverter.class)
    private List<String> accountant;

    /* 面积 */
    @FormField("numberField_mbr9dc8m")
    @Column(name = "area")
    private Float area;

    /* 面积区间 */
    @FormField("selectField_mbr9dc8o")
    @Column(name = "area_range", length = 64)
    private String areaRange;

    /* 城市 */
    @FormField("selectField_mbr9dc8p")
    @Column(name = "city", length = 64)
    private String city;

    /* 商圈等级 */
    @FormField("selectField_mbr9dc8q")
    @Column(name = "business_level", length = 64)
    private String businessLevel;

    /* 时间戳 */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void preInsert() {
        this.createdAt = this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    public void setBrandSysId(String v) {
        if (v != null) this.brandSysId = List.of(v);
    }

    public void setBatAreaSysId(String v) {
        if (v != null) this.batAreaSysId = List.of(v);
    }

    public void setBatAreaPeopleSysId(String v) {
        if (v != null) this.batAreaPeopleSysId = List.of(v);
    }

    public void setAreaSysId(String v) {
        if (v != null) this.areaSysId = List.of(v);
    }

    public void setSupervisorSysId(String v) {
        if (v != null) this.supervisorSysId = List.of(v);
    }

    public void setShopSysId(String v) {
        if (v != null) this.shopSysId = List.of(v);
    }

    public void setShopManagerSysId(String v) {
        if (v != null) this.shopManagerSysId = List.of(v);
    }

    public void setChiefSysId(String v) {
        if (v != null) this.chiefSysId = List.of(v);
    }

    public void setAccountant(String v) {
        if (v != null) this.accountant = List.of(v);
    }
}
