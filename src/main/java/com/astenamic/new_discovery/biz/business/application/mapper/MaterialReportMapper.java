package com.astenamic.new_discovery.biz.business.application.mapper;


import com.astenamic.new_discovery.biz.business.domain.valueobject.LossRateItem;
import com.astenamic.new_discovery.biz.business.domain.valueobject.MaterialReport;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dto.MaterialReportDTO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MaterialReportMapper {

    /**
     * 将 MaterialReportDTO（投影接口）映射为 MaterialReport
     * 两者属性名、类型完全一致，MapStruct 会自动匹配并拷贝。
     *
     * @param dto 数据库投影接口
     * @return 领域对象实例
     */
    @Named("toMaterialReport")
    MaterialReport toMaterialReport(MaterialReportDTO dto);

    /**
     * 将 MaterialReportDTO（投影接口）映射为 MaterialReport
     * 两者属性名、类型完全一致，MapStruct 会自动匹配并拷贝。
     *
     * @param dtos 数据库投影接口
     * @return 领域对象实例
     */
    @IterableMapping(qualifiedByName = "toMaterialReport")
    List<MaterialReport> toMaterialReports(List<MaterialReportDTO> dtos);

    /**
     * 将 MaterialReportDTO（投影接口）映射为 LossRateItem
     *
     * @param dto 数据库投影接口
     * @return 领域对象实例
     */
    @Named("toLossRateItem")
    LossRateItem toLossRateItem(MaterialReportDTO dto);

    /**
     * 将 MaterialReportDTO（投影接口）映射为 LossRateItem
     *
     * @param dtos 数据库投影接口
     * @return 领域对象实例
     */
    @IterableMapping(qualifiedByName = "toLossRateItem")
    List<LossRateItem> toLossRateItems(List<MaterialReportDTO> dtos);


}