package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PatrolRecordDao extends JpaRepository<PatrolRecord, Long> {

    List<PatrolRecord> findByBizCreateTimeBetween(LocalDateTime start, LocalDateTime end);


    Optional<PatrolRecord> findByRecordId(Long recordId);

}

