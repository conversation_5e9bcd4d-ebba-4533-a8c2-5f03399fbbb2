package com.astenamic.new_discovery.biz.business.infrastructure.jpa;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import com.astenamic.new_discovery.biz.business.domain.repository.PatrolRecordRepository;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.PatrolDetailDao;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.PatrolRecordDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class PatrolRecordJpaAdapter implements PatrolRecordRepository {

    private final PatrolRecordDao patrolRecordDao;
    private final PatrolDetailDao patrolDetailDao;

    /**
     * 按日期查询巡店记录及其详情
     */
    @Override
    public List<PatrolRecord> findByTime(LocalDateTime targetTime) {
        if (targetTime == null) {
            return List.of();
        }
        LocalDateTime start = targetTime.with(LocalTime.MIN);
        LocalDateTime end = targetTime.with(LocalTime.MAX);

        List<PatrolRecord> records = patrolRecordDao.findByBizCreateTimeBetween(start, end);
        records.forEach(r ->
                r.setPatrolDetailList(patrolDetailDao.findByBusinessId(r.getRecordId()))
        );
        return records;
    }

    /**
     * 根据 recordId 查询单条
     */
    @Override
    public PatrolRecord findByRecordId(Long recordId) {
        return patrolRecordDao.findByRecordId(recordId)
                .map(r -> {
                    r.setPatrolDetailList(patrolDetailDao.findByBusinessId(recordId));
                    return r;
                })
                .orElse(null);
    }

    /**
     * 幂等保存单条记录，详情先删后插
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(PatrolRecord entity) {

        /* -------- 参数校验 -------- */
        if (entity.getRecordId() == null) {
            throw new IllegalArgumentException("recordId 不能为空");
        }

        /* -------- 幂等主表 -------- */
        patrolRecordDao.findByRecordId(entity.getRecordId())
                .ifPresent(persisted -> entity.setId(persisted.getId()));

        /* -------- 处理详情列表 -------- */
        List<PatrolDetail> list = entity.getPatrolDetailList();
        if (list == null) list = List.of();

        Long businessId = entity.getRecordId();
        list.forEach(d -> d.setBusinessId(businessId));

        patrolDetailDao.deleteByBusinessId(businessId);
        patrolDetailDao.saveAll(list);

        /* -------- 保存主表 -------- */
        PatrolRecord saved = patrolRecordDao.save(entity);
        return saved.getId().toString();
    }

    /**
     * 批量幂等保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveAll(List<PatrolRecord> entities) {
        List<String> ids = new ArrayList<>(entities.size());
        for (PatrolRecord e : entities) {
            ids.add(save(e));
        }
        return ids;
    }
}
