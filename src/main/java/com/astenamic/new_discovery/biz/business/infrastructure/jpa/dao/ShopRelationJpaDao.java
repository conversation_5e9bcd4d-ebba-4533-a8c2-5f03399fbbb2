package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShopRelationJpaDao extends JpaRepository<ShopRelation, Long> {

    List<ShopRelation> findByClosed(String closed);

}
