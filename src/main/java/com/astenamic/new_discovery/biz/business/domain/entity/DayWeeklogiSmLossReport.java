package com.astenamic.new_discovery.biz.business.domain.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 日度周逻辑门店货品损耗表
 */
@Entity
@Table(name = "day_weeklogi_sm_loss_report")
@Data
public class DayWeeklogiSmLossReport {

    /* ===================== 组合主键 ===================== */

    @EmbeddedId
    private DayWeeklogiSmLossReportId id;

    /* ===================== 维度字段 ===================== */

    /** 周数_纯数字 */
    @Column(name = "week_num_dz")
    private Integer weekNumDz;

    /** 星期 */
    @Column(name = "week")
    private String week;

    /** 门店名称 */
    @Column(name = "shop_name")
    private String shopName;

    /** 品牌 */
    @Column(name = "brand_name")
    private String brandName;

    /** 区域 */
    @Column(name = "region_name")
    private String regionName;

    /** 物料编码 */
    @Column(name = "material_no")
    private String materialNo;

    /** 物料名称 */
    @Column(name = "material_name")
    private String materialName;

    /** 所属大类 */
    @Column(name = "material_type_big")
    private String materialTypeBig;

    /** 所属小类 */
    @Column(name = "material_type_small")
    private String materialTypeSmall;

    /** 规格 */
    @Column(name = "material_desc")
    private String materialDesc;

    /** 库存单位 */
    @Column(name = "kc_unit_name")
    private String kcUnitName;

    /** 物料编码合并 */
    @Column(name = "material_no_hb")
    private String materialNoHb;

    /** 物料名称合并 */
    @Column(name = "material_name_hb")
    private String materialNameHb;

    /** 物料类型 */
    @Column(name = "material_type")
    private String materialType;

    /** 库存单位_合并 */
    @Column(name = "kc_unit_name_hb")
    private String kcUnitNameHb;

    /** 物料大类统一 */
    @Column(name = "material_type_big_ty")
    private String materialTypeBigTy;

    /** 物料小类统一 */
    @Column(name = "material_type_small_ty")
    private String materialTypeSmallTy;

    /** 日期区间 */
    @Column(name = "date_range")
    private String dateRange;

    /* ===================== 数量（原单位） ===================== */

    /** 期初库存数量 */
    @Column(name = "kc_qc_qty")
    private Float kcQcQty;

    /** 期末库存数量 */
    @Column(name = "kc_qm_qty")
    private Float kcQmQty;

    /** 采购入库数量 */
    @Column(name = "cgrk_qty")
    private Float cgrkQty;

    /** 采购退货数量 */
    @Column(name = "cgth_qty")
    private Float cgthQty;

    /** 配送入库数量 */
    @Column(name = "psrk_qty")
    private Float psrkQty;

    /** 退配出库数量 */
    @Column(name = "tpck_qty")
    private Float tpckQty;

    /** 调拨入库数量 */
    @Column(name = "dbrk_qty")
    private Float dbrkQty;

    /** 调拨出库数量 */
    @Column(name = "dbck_qty")
    private Float dbckQty;

    /** 报溢入库数量 */
    @Column(name = "byrk_qty")
    private Float byrkQty;

    /** 报损出库数量 */
    @Column(name = "bsck_qty")
    private Float bsckQty;

    /** 实际用量 */
    @Column(name = "sj_qty")
    private Float sjQty;

    /** 理论用量 */
    @Column(name = "ll_qty")
    private Float llQty;

    /* ===================== 成本（原单位） ===================== */

    /** 期初库存成本 */
    @Column(name = "kc_qc_cost")
    private Float kcQcCost;

    /** 期末库存成本 */
    @Column(name = "kc_qm_cost")
    private Float kcQmCost;

    /** 采购入库成本 */
    @Column(name = "cgrk_cost")
    private Float cgrkCost;

    /** 采购退货成本 */
    @Column(name = "cgth_cost")
    private Float cgthCost;

    /** 配送入库成本 */
    @Column(name = "psrk_cost")
    private Float psrkCost;

    /** 退配出库成本 */
    @Column(name = "tpck_cost")
    private Float tpckCost;

    /** 调拨入库成本 */
    @Column(name = "dbrk_cost")
    private Float dbrkCost;

    /** 调拨出库成本 */
    @Column(name = "dbck_cost")
    private Float dbckCost;

    /** 报溢入库成本 */
    @Column(name = "byrk_cost")
    private Float byrkCost;

    /** 报损出库成本 */
    @Column(name = "bsck_cost")
    private Float bsckCost;

    /** 实际成本 */
    @Column(name = "sj_cost")
    private Float sjCost;

    /** 理论成本 */
    @Column(name = "ll_cost")
    private Float llCost;

    /* ===================== 数量（统一库存单位 *_ty） ===================== */

    /** 实际用量_库存单位统一 */
    @Column(name = "sj_qty_ty")
    private Float sjQtyTy;

    /** 理论用量_库存单位统一 */
    @Column(name = "ll_qty_ty")
    private Float llQtyTy;

    /** 采购入库数量_库存单位统一 */
    @Column(name = "cgrk_qty_ty")
    private Float cgrkQtyTy;

    /** 采购退货数量_库存单位统一 */
    @Column(name = "cgth_qty_ty")
    private Float cgthQtyTy;

    /** 配送入库数量_库存单位统一 */
    @Column(name = "psrk_qty_ty")
    private Float psrkQtyTy;

    /** 退配出库数量_库存单位统一 */
    @Column(name = "tpck_qty_ty")
    private Float tpckQtyTy;

    /** 调拨入库数量_库存单位统一 */
    @Column(name = "dbrk_qty_ty")
    private Float dbrkQtyTy;

    /** 调拨出库数量_库存单位统一 */
    @Column(name = "dbck_qty_ty")
    private Float dbckQtyTy;

    /** 报溢入库数量_库存单位统一 */
    @Column(name = "byrk_qty_ty")
    private Float byrkQtyTy;

    /** 报损出库数量_库存单位统一 */
    @Column(name = "bsck_qty_ty")
    private Float bsckQtyTy;

    /** 期初库存数量_库存单位统一 */
    @Column(name = "kc_qc_qty_ty")
    private Float kcQcQtyTy;

    /** 期末库存数量_库存单位统一 */
    @Column(name = "kc_qm_qty_ty")
    private Float kcQmQtyTy;

    /* ===================== 组合主键类 ===================== */

    @Data
    @Embeddable
    public static class DayWeeklogiSmLossReportId implements Serializable {

        /** 日期 */
        @Column(name = "day_format", nullable = false)
        private String dayFormat;

        /** 门店ID */
        @Column(name = "shop_id", nullable = false)
        private String shopId;

        /** 物料ID */
        @Column(name = "material_id", nullable = false)
        private String materialId;
    }

}

