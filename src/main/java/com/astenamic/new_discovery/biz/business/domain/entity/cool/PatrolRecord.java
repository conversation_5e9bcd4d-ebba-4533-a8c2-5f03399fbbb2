package com.astenamic.new_discovery.biz.business.domain.entity.cool;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡店记录表实体
 */
@Data
@Entity(name = "xfx_cool_patrol_record")
public class PatrolRecord {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 删除标识：0-正常 1-删除
     */
    @Column(name = "del_flag", length = 1, nullable = false)
    private String delFlag = "0";

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @PrePersist
    protected void preInsert() {
        this.createTime = this.updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 巡店时长（实际）
     */
    @Column(name = "actual_patrol_store_duration", length = 64)
    private String actualPatrolStoreDuration;

    /**
     * 审核意见：reject/通过 pass/拒绝
     */
    @Column(name = "audit_opinion", length = 16)
    private String auditOpinion;

    /**
     * 审核图片
     */
    @Column(name = "audit_picture", columnDefinition = "text")
    private String auditPicture;

    /**
     * 审核备注
     */
    @Column(name = "audit_remark", columnDefinition = "text")
    private String auditRemark;

    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    @Column(name = "audit_user_id", length = 64)
    private String auditUserId;

    /**
     * 审核人名称
     */
    @Column(name = "audit_user_name", length = 64)
    private String auditUserName;

    /**
     * 巡店结果（优秀、良好、合格、不合格）
     */
    @Column(name = "check_result", length = 16)
    private String checkResult;

    /**
     * 业务创建时间
     */
    @Column(name = "biz_create_time")
    private LocalDateTime bizCreateTime;

    /**
     * 创建人名称
     */
    @Column(name = "creater_user_name", length = 64)
    private String createrUserName;

    /**
     * 不合格检查项数
     */
    @Column(name = "fail_column_count")
    private Integer failColumnCount;

    /**
     * 巡店记录ID（第三方）
     */
    @Column(name = "record_id")
    private Long recordId;

    /**
     * 不适用检查项数
     */
    @Column(name = "inapplicable_column_count")
    private Integer inapplicableColumnCount;

    /**
     * 是否逾期完成（字符串）
     */
    @Column(name = "overdue", length = 16)
    private String overdue;

    /**
     * 任务说明
     */
    @Column(name = "task_desc", columnDefinition = "text")
    private String taskDesc;

    /**
     * 是否逾期完成（布尔）
     */
    @Column(name = "is_overdue")
    private Boolean isOverdue;

    /**
     * 检查表ID
     */
    @Column(name = "meta_table_id")
    private Long metaTableId;

    /**
     * 检查表名称
     */
    @Column(name = "meta_table_name", length = 128)
    private String metaTableName;

    /**
     * 合格检查项数
     */
    @Column(name = "pass_column_count")
    private Integer passColumnCount;

    /**
     * 巡店类型
     */
    @Column(name = "patrol_type", length = 32)
    private String patrolType;

    /**
     * 得分率
     */
    @Column(name = "percent", precision = 5, scale = 2)
    private BigDecimal percent;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 区域名称
     */
    @Column(name = "region_name", length = 64)
    private String regionName;

    /**
     * 奖罚金额
     */
    @Column(name = "reward_penalt_money", precision = 15, scale = 2)
    private BigDecimal rewardPenaltMoney;

    /**
     * 得分
     */
    @Column(name = "score", precision = 10, scale = 2)
    private BigDecimal score;

    /**
     * 巡店结束地址
     */
    @Column(name = "sign_end_address", length = 255)
    private String signEndAddress;

    /**
     * 签到状态：1正常，2异常
     */
    @Column(name = "sign_in_status")
    private Integer signInStatus;

    /**
     * 签退状态：1正常，2异常
     */
    @Column(name = "sign_out_status")
    private Integer signOutStatus;

    /**
     * 巡店开始地址
     */
    @Column(name = "sign_start_address", length = 255)
    private String signStartAddress;

    /**
     * 签到时间
     */
    @Column(name = "sign_start_time")
    private LocalDateTime signStartTime;

    /**
     * 签退时间
     */
    @Column(name = "sign_end_time")
    private LocalDateTime signEndTime;

    /**
     * 签到/签退方式（gps）
     */
    @Column(name = "sign_way", length = 32)
    private String signWay;

    /**
     * 状态：0-待处理 1-已完成 2-待审批
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 门店ID
     */
    @Column(name = "store_id", length = 64)
    private String storeId;

    /**
     * 门店名称
     */
    @Column(name = "store_name", length = 128)
    private String storeName;

    /**
     * 任务开始时间
     */
    @Column(name = "sub_begin_time")
    private LocalDateTime subBeginTime;

    /**
     * 任务结束时间
     */
    @Column(name = "sub_end_time")
    private LocalDateTime subEndTime;

    /**
     * 巡店总结
     */
    @Column(name = "summary", columnDefinition = "text")
    private String summary;

    /**
     * 巡店总结图片
     */
    @Column(name = "summary_picture", columnDefinition = "text")
    private String summaryPicture;

    /**
     * 巡店总结视频
     */
    @Column(name = "summary_video", columnDefinition = "text")
    private String summaryVideo;

    /**
     * 巡店人ID
     */
    @Column(name = "supervisor_id", length = 64)
    private String supervisorId;

    /**
     * 巡店人名称
     */
    @Column(name = "supervisor_name", length = 64)
    private String supervisorName;

    /**
     * 巡店签名
     */
    @Column(name = "supervisor_signature", columnDefinition = "text")
    private String supervisorSignature;

    /**
     * 任务ID
     */
    @Column(name = "task_id")
    private Long taskId;

    /**
     * 任务名称
     */
    @Column(name = "task_name", length = 128)
    private String taskName;

    /**
     * 总项数
     */
    @Column(name = "total_column_count")
    private Integer totalColumnCount;

    /**
     * 总分
     */
    @Column(name = "total_score", precision = 10, scale = 2)
    private BigDecimal totalScore;

    /**
     * 巡店时长（毫秒）
     */
    @Column(name = "tour_time", length = 32)
    private String tourTime;

    /**
     * 巡店详情列表
     */
    @Transient
    private List<PatrolDetail> patrolDetailList;
}
