package com.astenamic.new_discovery.biz.business.domain.repository;


import com.astenamic.new_discovery.biz.business.domain.entity.CostLossRateFlow;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;

import java.time.LocalDateTime;
import java.util.List;

public interface CostLossRateFlowRepository {

    /**
     * 查询周度原材料预警
     */
    List<CostLossRateFlow> findByWeek(LocalDateTime targetTime);

    /**
     * 批量发起/保存 CostLossRateFlow 实体
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体列表
     */
    List<String> saveAll(List<CostLossRateFlow> entities);

}
