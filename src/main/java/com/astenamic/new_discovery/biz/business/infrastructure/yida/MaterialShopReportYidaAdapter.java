package com.astenamic.new_discovery.biz.business.infrastructure.yida;

import com.astenamic.new_discovery.biz.business.domain.entity.MaterialShopReport;
import com.astenamic.new_discovery.biz.business.domain.repository.MaterialShopReportRepository;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class MaterialShopReportYidaAdapter extends AbstractFormService<MaterialShopReport> implements MaterialShopReportRepository {

    public MaterialShopReportYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(MaterialShopReport.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    @Override
    public List<MaterialShopReport> findByTime(LocalDateTime targetTime) {
        return Optional.ofNullable(targetTime)
                .map(t -> {
                    long[] dayRange = TimeUtils.getDayRange(t);
                    SearchConditions cond = SearchCondition.builder()
                            .dateBetween(this.clazz, MaterialShopReport::getDate, dayRange, "+")
                            .get();
                    return this.getFormsByCond(cond);
                })
                .orElseGet(Collections::emptyList);
    }

    @Override
    public List<String> saveAll(List<MaterialShopReport> entities) {
        return super.batchSave(entities);
    }
}
