package com.astenamic.new_discovery.biz.business.infrastructure.yida;

import com.astenamic.new_discovery.biz.business.domain.entity.CostLossRateFlow;
import com.astenamic.new_discovery.biz.business.domain.repository.CostLossRateFlowRepository;
import com.astenamic.new_discovery.util.TimeUtils;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.service.base.AbstractFlowService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public class CostLossRateFlowYidaAdapter extends AbstractFlowService<CostLossRateFlow> implements CostLossRateFlowRepository {


    public CostLossRateFlowYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(CostLossRateFlow.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    @Override
    public List<CostLossRateFlow> findByWeek(LocalDateTime targetTime) {
        if (targetTime == null) {
            return List.of();
        }
        long[] weekRange = TimeUtils.getWeekRange(targetTime, 1);
        SearchConditions cond = SearchCondition.builder().dateBetween(CostLossRateFlow.class, CostLossRateFlow::getDate, weekRange, "+")
                .get();
        return super.getFlowsByCond(cond);
    }


    @Override
    public List<String> saveAll(List<CostLossRateFlow> entities) {
        return super.batchLaunchProcess(entities);
    }
}
