package com.astenamic.new_discovery.biz.business.application.mapper;


import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolRecord;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolRecordDTO;
import org.mapstruct.*;

import java.util.List;


import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 巡店记录 Mapper（DTO ⇄ Entity）
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PatrolRecordMapper {


    @Mappings({
            @Mapping(source = "auditTime",     target = "auditTime"),
            @Mapping(source = "createTime",    target = "bizCreateTime"),
            @Mapping(source = "signStartTime", target = "signStartTime"),
            @Mapping(source = "signEndTime",   target = "signEndTime"),
            @Mapping(source = "subBeginTime",  target = "subBeginTime"),
            @Mapping(source = "subEndTime",    target = "subEndTime"),
            @Mapping(target = "id",         ignore = true),
            @Mapping(target = "delFlag",    constant = "0"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PatrolRecord toEntity(PatrolRecordDTO dto);


    @Mappings({
            @Mapping(source = "auditTime",     target = "auditTime"),
            @Mapping(source = "bizCreateTime", target = "createTime"),
            @Mapping(source = "signStartTime", target = "signStartTime"),
            @Mapping(source = "signEndTime",   target = "signEndTime"),
            @Mapping(source = "subBeginTime",  target = "subBeginTime"),
            @Mapping(source = "subEndTime",    target = "subEndTime")
    })
    PatrolRecordDTO toDTO(PatrolRecord entity);


    List<PatrolRecord>   toEntityList(List<PatrolRecordDTO> dtoList);

    List<PatrolRecordDTO> toDTOList(List<PatrolRecord> entityList);


    default LocalDateTime longToLdt(Long ts) {
        return ts == null ? null
                : LocalDateTime.ofInstant(Instant.ofEpochMilli(ts), ZoneId.systemDefault());
    }

    default Long ldtToLong(LocalDateTime ldt) {
        return ldt == null ? null
                : ldt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}

