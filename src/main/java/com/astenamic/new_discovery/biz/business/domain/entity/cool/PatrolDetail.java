package com.astenamic.new_discovery.biz.business.domain.entity.cool;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 巡店详情表
 */
@Data
@Entity(name = "xfx_cool_patrol_detail")
public class PatrolDetail {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 删除标识：0-正常 1-删除
     */
    @Column(name = "del_flag", length = 1)
    private String delFlag = "0";

    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @PrePersist
    protected void preInsert() {
        this.createTime = this.updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 奖罚金额（标准）
     */
    @Column(name = "award_punish", length = 64)
    private String awardPunish;

    /**
     * 奖罚金额（实际）
     */
    @Column(name = "award_money", precision = 15, scale = 2)
    private BigDecimal awardMoney;

    /**
     * 巡店记录 ID
     */
    @Column(name = "business_id")
    private Long businessId;

    /**
     * 分类名称
     */
    @Column(name = "category_name", length = 128)
    private String categoryName;

    /**
     * 奖罚金额（实际，检查结果）
     */
    @Column(name = "check_award_punish", length = 64)
    private String checkAwardPunish;

    /**
     * 检查图片
     */
    @Column(name = "check_pics", columnDefinition = "text")
    private String checkPics;

    /**
     * 检查结果：PASS/FAIL/INAPPLICABLE
     */
    @Column(name = "check_result", length = 16)
    private String checkResult;

    /**
     * 检查结果名称
     */
    @Column(name = "check_result_name", length = 64)
    private String checkResultName;

    /**
     * 得分
     */
    @Column(name = "check_score", precision = 10, scale = 2)
    private BigDecimal checkScore;

    /**
     * 检查内容
     */
    @Column(name = "check_text", columnDefinition = "text")
    private String checkText;

    /**
     * 检查视频
     */
    @Column(name = "check_video", columnDefinition = "text")
    private String checkVideo;

    /**
     * 检查项名称
     */
    @Column(name = "column_name", length = 128)
    private String columnName;

    /**
     * 业务创建时间（原接口 createTime）
     */
    @Column(name = "biz_create_time")
    private LocalDateTime bizCreateTime;

    /**
     * 描述
     */
    @Column(name = "description", columnDefinition = "text")
    private String description;

    /**
     * 检查表名称
     */
    @Column(name = "meta_table_name", length = 128)
    private String metaTableName;

    /**
     * 检查表 ID
     */
    @Column(name = "meta_table_id", length = 64)
    private String metaTableId;

    /**
     * 标准图
     */
    @Column(name = "standard_pic", columnDefinition = "text")
    private String standardPic;

    /**
     * 门店 ID
     */
    @Column(name = "store_id", length = 64)
    private String storeId;

    /**
     * 门店名称
     */
    @Column(name = "store_name", length = 128)
    private String storeName;

    /**
     * 任务开始时间
     */
    @Column(name = "sub_begin_time")
    private LocalDateTime subBeginTime;

    /**
     * 任务结束时间
     */
    @Column(name = "sub_end_time")
    private LocalDateTime subEndTime;

    /**
     * 巡店人 ID
     */
    @Column(name = "supervisor_id", length = 64)
    private String supervisorId;

    /**
     * 巡店人名称
     */
    @Column(name = "supervisor_name", length = 64)
    private String supervisorName;

    /**
     * 标准分
     */
    @Column(name = "support_score", precision = 10, scale = 2)
    private BigDecimal supportScore;

    /**
     * 检查项属性
     */
    @Column(name = "table_property")
    private Integer tableProperty;

    /**
     * 任务描述
     */
    @Column(name = "task_desc", columnDefinition = "text")
    private String taskDesc;

    /**
     * 任务名称
     */
    @Column(name = "task_name", length = 128)
    private String taskName;

    /**
     * 自定义结果
     */
    @Column(name = "value", columnDefinition = "text")
    private String value;

    /**
     * 自定义格式
     */
    @Column(name = "format", length = 32)
    private String format;

    /**
     * 所属区域 ID
     */
    @Column(name = "region_id")
    private Long regionId;
}
