package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.domain.repository.ShopRelationRepository;

import com.astenamic.new_discovery.external.client.cool.vision.service.StoreListService;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.StoreDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ShopRelationAppService {


    private final ShopRelationRepository shopRelationJpaAdapter;

    private final ShopRelationRepository shopRelationYidaAdapter;

    private final StoreListService storeListService;


    public List<ShopRelation> getRelations() {
        return shopRelationJpaAdapter.findAll();
    }

    public List<ShopRelation> getRelationsByClosed(String closed) {
        return shopRelationJpaAdapter.findOpen();
    }

    public ShopRelation getRelationByShopSysId(String shopSysId) {
        return shopRelationJpaAdapter.findAll().stream()
                .filter(r -> r.getShopSysId() != null && r.getShopSysId().contains(shopSysId))
                .findFirst()
                .orElse(null);
    }

    public void syncRelations() {
        // 获取新数据（来自宜搭）
        List<ShopRelation> yidaDatas = shopRelationYidaAdapter.findAll();

        // 获取老数据（来自数据库）
        List<ShopRelation> dbDatas = shopRelationJpaAdapter.findAll();

        //酷巡店id
        List<StoreDTO> storeList = storeListService.getStoreList(1L);

        Map<String, StoreDTO> storeMap = storeList.stream()
                .filter(s -> StringUtils.isBlank(s.getSynDingDeptId()))
                .collect(Collectors.toMap(
                        StoreDTO::getSynDingDeptId,
                        Function.identity(),
                        (a, b) -> a
                ));

        for (ShopRelation sr : yidaDatas) {
            List<String> shopSysIds = sr.getShopSysId();
            if (shopSysIds == null || shopSysIds.isEmpty()) {
                continue;
            }

            String deptId = shopSysIds.get(0);

            StoreDTO matched = storeMap.get(deptId);

            if (matched != null) {
                sr.setKuxundianId(matched.getStoreId());
            } else {
                sr.setShopSysId(null);
            }
        }


        if (dbDatas.isEmpty()) {
            // 数据库为空，直接保存所有新数据
            shopRelationYidaAdapter.saveAll(yidaDatas);
            shopRelationJpaAdapter.saveAll(yidaDatas);
        } else {
            // 用新数据完全覆盖老数据（包括删除场景）

            // 创建老数据的映射表，以shopId为key，提高查找效率
            Map<String, ShopRelation> dbDataMap = dbDatas.stream()
                    .collect(Collectors.toMap(ShopRelation::getShopId, Function.identity()));

            // 创建新数据的shopId集合，用于识别需要删除的记录
            Set<String> yidaShopIds = yidaDatas.stream()
                    .map(ShopRelation::getShopId)
                    .collect(Collectors.toSet());

            // 遍历新数据，进行覆盖操作
            for (ShopRelation yidaData : yidaDatas) {
                ShopRelation existingData = dbDataMap.get(yidaData.getShopId());
                if (existingData != null) {
                    // 找到对应的老数据，保留主键和创建时间，其他字段用新数据覆盖
                    yidaData.setId(existingData.getId());
                    yidaData.setCreatedAt(existingData.getCreatedAt());
                }
                // 如果没找到对应的老数据，说明是新增记录，保持yidaData原样即可
            }

            // 找出需要删除的记录（老数据中存在但新数据中不存在的记录）
            List<ShopRelation> recordsToDelete = dbDatas.stream()
                    .filter(dbData -> !yidaShopIds.contains(dbData.getShopId()))
                    .toList();

            // 先删除不存在于新数据中的老记录
            if (!recordsToDelete.isEmpty()) {
                shopRelationJpaAdapter.deleteAll(recordsToDelete);
            }

            // 保存所有新数据（包括更新和新增）
            shopRelationYidaAdapter.saveAll(yidaDatas);
            shopRelationJpaAdapter.saveAll(yidaDatas);
        }
    }
}
