package com.astenamic.new_discovery.biz.business.domain.valueobject;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaFile;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 成本损耗率子表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity("tableField_m3xv5p86")
public class LossRateItem extends MaterialReport{

    /**
     * 原因标签
     */
    @FormField("multiSelectField_m61s4tkd")
    private List<String> reasonTags;

    /**
     * 详细描述
     */
    @FormField("textField_m4v196ka")
    private String description;

    /**
     * 行动方案
     */
    @FormField("textareaField_m411pz83")
    private String actionPlan;

    /**
     * 相关图片
     */
    @FormField("imageField_m411pz84")
    private List<YidaFile> pictures;

    /**
     * 督导审核意见
     */
    @FormField("textField_m9ammais")
    private String supervisorReview;

    /**
     * 会计复盘分析
     */
    @FormField("textField_m9ammait")
    private String accountingAnalysis;
}

