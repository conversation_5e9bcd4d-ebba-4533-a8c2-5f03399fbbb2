package com.astenamic.new_discovery.biz.business.domain.repository;

import com.astenamic.new_discovery.biz.business.domain.entity.CostLossRateFlow;
import com.astenamic.new_discovery.biz.business.domain.entity.MaterialShopReport;

import java.time.LocalDateTime;
import java.util.List;

public interface MaterialShopReportRepository {

    /**
     * 查询日度原材料门店报表
     */
    List<MaterialShopReport> findByTime(LocalDateTime targetTime);

    /**
     * 批量发起/保存 MaterialShopReport 实体
     *
     * @param entities 要保存的实体列表
     * @return 保存后的实体列表
     */
    List<String> saveAll(List<MaterialShopReport> entities);

}
