package com.astenamic.new_discovery.biz.business.application.decorator;


import com.astenamic.new_discovery.biz.business.domain.entity.CostLossRateFlow;
import com.astenamic.new_discovery.biz.business.domain.entity.MaterialShopReport;
import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ShopRelationDecorator {

    private final ShopRelationAppService shopRelationAppService;

    public List<MaterialShopReport> materialShopReportDecorator(List<MaterialShopReport> materialShopReports) {
        Map<String, ShopRelation> shopMap = getShopMap();
        return Optional.ofNullable(materialShopReports)
                .orElseGet(Collections::emptyList)
                .stream()
                .peek(report -> Optional.ofNullable(shopMap.get(report.getShopId()))
                        .ifPresent(rel -> {
                            report.setShopSysId(getListValue(rel.getShopSysId()));
                            report.setChiefSysId(getListValue(rel.getChiefSysId()));
                            report.setSupervisorSysId(getListValue(rel.getSupervisorSysId()));
                            report.setShopManagerSysId(getListValue(rel.getShopManagerSysId()));
                            report.setBatAreaPeopleSysId(getListValue(rel.getBatAreaPeopleSysId()));
                            report.setBatAreaSysId(getListValue(rel.getBatAreaSysId()));
                            report.setBrandSysId(getListValue(rel.getBrandSysId()));
                            report.setAreaSysId(getListValue(rel.getAreaSysId()));
                        })
                )
                .filter(report -> report.getShopSysId() != null && !report.getShopSysId().isEmpty())
                .collect(Collectors.toList());
    }

    private Map<String, ShopRelation> getShopMap() {
        return Optional.ofNullable(shopRelationAppService.getRelationsByClosed("否"))
                .filter(rel -> !rel.isEmpty())
                .map(rel -> rel.stream()
                        .collect(Collectors.toMap(ShopRelation::getShopId, Function.identity())))
                .orElseGet(Collections::emptyMap);
    }

    public List<CostLossRateFlow> costLossRateFlowDecorator(List<CostLossRateFlow> costLossRateFlows) {
        Map<String, ShopRelation> shopMap = getShopMap();
        return Optional.ofNullable(costLossRateFlows)
                .orElseGet(Collections::emptyList)
                .stream()
                .peek(report -> Optional.ofNullable(shopMap.get(report.getShopId()))
                        .ifPresent(rel -> {
                            report.setShopSysId(getListValue(rel.getShopSysId()));
                            report.setChiefSysId(getListValue(rel.getChiefSysId()));
                            report.setSupervisorSysId(getListValue(rel.getSupervisorSysId()));
                            report.setAccountantSysId(getListValue(rel.getAccountant()));
                            report.setShopManagerSysId(getListValue(rel.getShopManagerSysId()));
                            report.setBatAreaPeopleSysId(getListValue(rel.getBatAreaPeopleSysId()));
                            report.setBatAreaSysId(getListValue(rel.getBatAreaSysId()));
                            report.setBrandSysId(getListValue(rel.getBrandSysId()));
                            report.setAreaSysId(getListValue(rel.getAreaSysId()));
                        })
                )
                .filter(report -> report.getShopSysId() != null && !report.getShopSysId().isEmpty())
                .toList();
    }


    private String getListValue(List<String> list) {
        return Optional.ofNullable(list)
                .filter(vs -> !vs.isEmpty())
                .map(vs -> vs.get(0))
                .orElse("");
    }

}
