package com.astenamic.new_discovery.biz.business.application.service;


import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.external.client.quickbi.TicketService;
import com.astenamic.new_discovery.external.config.AliyunConfiguration;
import com.astenamic.new_discovery.external.modal.dto.quickbi.request.TicketGlobalParam;
import com.astenamic.new_discovery.interfaces.rest.yida.business.dto.CreateTicketRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * QuickBI业务服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuickBiService {

    private final TicketService ticketService;

    private final AliyunConfiguration aliyunConfiguration;


    public CompletableFuture<Result<String>> getTicketAsync(CreateTicketRequest request) {

        List<TicketGlobalParam> params = new ArrayList<>();

        if (request.getParams() != null) {
            request.getParams().forEach(param -> {
                params.add(TicketGlobalParam.createEqualCondition(param.getParamKey(), param.getValue()));
            });
        }

        return ticketService.getTicketAsync(request.getWorkId(), request.getUserId(), params)
                .thenApply(ticket -> Result.ok(buildUrl(request.getWorkId(), ticket)))
                .exceptionally(ex -> {
                    log.error("获取QuickBI票据失败", ex);
                    return Result.fail(Result.ErrorCode.REQUEST_ERROR, "获取票据失败: " + ex.getMessage());
                });
    }


    public String buildUrl(String pageId, String accessTicket) {
        String baseUrl = aliyunConfiguration.getQuickBi().getBaseUrl();
        return baseUrl
                .replace("${pageId}", pageId)
                .replace("${accessTicket}", accessTicket);
    }
}
