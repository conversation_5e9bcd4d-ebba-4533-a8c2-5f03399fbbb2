package com.astenamic.new_discovery.biz.business.application.mapper;

import com.astenamic.new_discovery.biz.business.domain.entity.cool.PatrolDetail;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import org.mapstruct.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PatrolDetailMapper {

    @Mappings({
            @Mapping(source = "createTime", target = "bizCreateTime"),
            @Mapping(source = "subBeginTime", target = "subBeginTime"),
            @Mapping(source = "subEndTime", target = "subEndTime"),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "delFlag", constant = "0"),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true)
    })
    PatrolDetail toEntity(PatrolDetailDTO dto);


    @Mappings({
            @Mapping(source = "bizCreateTime", target = "createTime"),
            @Mapping(source = "subBeginTime", target = "subBeginTime"),
            @Mapping(source = "subEndTime", target = "subEndTime")
    })
    PatrolDetailDTO toDTO(PatrolDetail entity);

    List<PatrolDetail> toEntityList(List<PatrolDetailDTO> dtoList);

    List<PatrolDetailDTO> toDTOList(List<PatrolDetail> entityList);

    /* ===== 时间转换：毫秒时间戳 <-> LocalDateTime ===== */

    /**
     * Long 毫秒 → LocalDateTime
     */
    default LocalDateTime longToLdt(Long ms) {
        return ms == null ? null
                : LocalDateTime.ofInstant(Instant.ofEpochMilli(ms), ZoneId.systemDefault());
    }

    /**
     * LocalDateTime → Long 毫秒
     */
    default Long ldtToLong(LocalDateTime ldt) {
        return ldt == null ? null
                : ldt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
