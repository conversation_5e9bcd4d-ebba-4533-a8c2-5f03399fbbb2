package com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao;

import com.astenamic.new_discovery.biz.business.domain.valueobject.MaterialReport;
import com.astenamic.new_discovery.biz.business.domain.entity.DaySmLossReport;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dto.MaterialReportDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DaySmLossReportJpaDao extends
        JpaRepository<DaySmLossReport, DaySmLossReport.DaySmLossReportId>,
        JpaSpecificationExecutor<DaySmLossReport> {


    @Query(
            value = """
                    WITH params AS (
                        SELECT
                            :dayFormat AS date_from,
                            ARRAY['生鲜类', '冻品类']::TEXT[] AS focus_bigtypes,
                            ARRAY['10133', '4120004', '3040108','4120003', '1050039', '1030', '4070125']::TEXT[] AS kj_materials
                    ),
                    
                    base AS (
                        SELECT
                            d.shop_id,
                            d.shop_name,
                            d.material_no_hb,
                            d.material_name_hb,
                            d.material_type_big_ty,
                            d.material_type_small_ty,
                            d.kc_unit_name_hb,
                            SUM(COALESCE(d.sj_cost, 0))                                   AS sj_cost,
                            SUM(COALESCE(d.ll_cost, 0))                                   AS ll_cost,
                            SUM(COALESCE(d.sj_cost, 0)) - SUM(COALESCE(d.ll_cost, 0))     AS cy_cost,
                            SUM(COALESCE(d.sj_qty_ty, 0))                                 AS sj_qty_ty,
                            SUM(COALESCE(d.ll_qty_ty, 0))                                 AS ll_qty_ty,
                            SUM(COALESCE(d.sj_qty_ty, 0)) - SUM(COALESCE(d.ll_qty_ty, 0)) AS cy_qty_ty,
                            SUM(COALESCE(d.psrk_qty_ty, 0)) AS psrk_qty_ty,
                            SUM(COALESCE(d.cgrk_qty_ty, 0)) AS cgrk_qty_ty,
                            SUM(COALESCE(d.kc_qc_qty_ty, 0)) AS kc_qc_qty_ty,
                            SUM(COALESCE(d.kc_qm_qty_ty, 0)) AS kc_qm_qty_ty,
                            SUM(COALESCE(d.dbck_qty_ty, 0))  AS dbck_qty_ty,
                            SUM(COALESCE(d.bsck_qty_ty, 0))  AS bsck_qty_ty,
                            SUM(COALESCE(d.dbck_cost, 0))    AS dbck_cost,
                            SUM(COALESCE(d.bsck_cost, 0))    AS bsck_cost
                        FROM day_sm_loss_report d
                        WHERE d.day_format BETWEEN (SELECT date_from FROM params)
                                               AND (SELECT date_from   FROM params)
                          AND (
                                d.material_type_big_ty IN (SELECT UNNEST(focus_bigtypes) FROM params)
                             OR d.shop_name ~ '烤匠'
                          )
                        GROUP BY
                            d.shop_id,
                            d.shop_name,
                            d.material_no_hb,
                            d.material_name_hb,
                            d.material_type_big_ty,
                            d.material_type_small_ty,
                            d.kc_unit_name_hb
                    ),
                    
                    ranked AS (
                        SELECT
                            b.*,
                            ROUND(
                                CASE
                                    WHEN b.ll_cost = 0 AND b.cy_cost < 0 THEN -100
                                    WHEN b.ll_cost = 0 AND b.cy_cost > 0 THEN 100
                                    ELSE b.cy_cost / NULLIF(b.ll_cost, 0) * 100
                                END, 2) AS loss_ratio,
                            ROW_NUMBER() OVER (PARTITION BY b.shop_id ORDER BY b.cy_cost ASC)  AS asc_rank,
                            ROW_NUMBER() OVER (PARTITION BY b.shop_id ORDER BY b.cy_cost DESC) AS desc_rank
                        FROM base b
                    ),
                    
                    healthy_shop AS (
                        SELECT shop_id
                        FROM   ranked
                        WHERE  shop_name !~ '烤匠'
                        GROUP BY shop_id
                        HAVING ABS(SUM(cy_cost) / NULLIF(SUM(ll_cost), 0) * 100) < 2
                    )
                    
                    SELECT r.*
                    FROM   ranked r
                    WHERE  r.shop_name !~ '烤匠'
                      AND  r.shop_id NOT IN (SELECT shop_id FROM healthy_shop)
                      AND (r.asc_rank <= 5 OR r.desc_rank <= 5)
                    
                    UNION ALL
                    
                    SELECT r.*
                    FROM   ranked r
                    WHERE  r.shop_name ~ '烤匠'
                      AND  r.material_no_hb::TEXT IN (
                            SELECT UNNEST(kj_materials) FROM params
                      );
                    """, nativeQuery = true)
    List<MaterialReportDTO> findMaterialReportByTime(@Param("dayFormat")String dayFormat);
}
