package com.astenamic.new_discovery.biz.business.infrastructure.jpa;


import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.domain.repository.ShopRelationRepository;
import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.ShopRelationJpaDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@RequiredArgsConstructor
public class ShopRelationJpaAdapter implements ShopRelationRepository {

    private final ShopRelationJpaDao jpa;


    @Override
    public List<ShopRelation> findAll() {
        return jpa.findAll();
    }

    @Override
    public List<ShopRelation> findOpen() {
        return jpa.findByClosed("否");
    }

    @Override
    public String save(ShopRelation entity) {
        return jpa.save(entity).getId().toString();
    }

    @Override
    public List<String> saveAll(List<ShopRelation> entities) {
        return jpa.saveAll(entities).stream()
                .map(shopRelation -> shopRelation.getId().toString())
                .toList();
    }

    @Override
    public void deleteAll(List<ShopRelation> entities) {
        jpa.deleteAll(entities);
    }
}
