package com.astenamic.new_discovery.biz.relation.shop.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity("FORM-98A81256D81F4CA29C2BA0700428BDDAGXJV")
public class ShopRelation {

    @FormField("textField_m3xt9z9d")
    private String shopId;

    // 品牌id
    @FormField("departmentSelectField_m41c2xhs_id")
    private String brandSysId;

    // 战区id
    @FormField("departmentSelectField_m419fef2_id")
    private String batAreaSysId;

    // 战区司令id
    @FormField("employeeField_m3wuckey_id")
    private String batAreaPeopleSysId;

    // 区域
    @FormField("departmentSelectField_m419fef3_id")
    private String areaSysId;

    // 督导
    @FormField("employeeField_m3wuckf0_id")
    private String supervisorSysId;

    // 门店id
    @FormField("departmentSelectField_m419fef4_id")
    private String shopSysId;

    // 门店经理id
    @FormField("employeeField_m3wtrn8f_id")
    private String shopManagerSysId;

    // 厨师长id
    @FormField("employeeField_m3wuckf2_id")
    private String chiefSysId;

    // 群id
    @FormField("textField_m3zmsvkm")
    private String groupId;

    @FormField("employeeField_m88jm845_id")
    private String accountant;

}
