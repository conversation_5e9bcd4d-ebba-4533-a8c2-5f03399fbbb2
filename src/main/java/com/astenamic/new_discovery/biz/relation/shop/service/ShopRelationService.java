package com.astenamic.new_discovery.biz.relation.shop.service;

import com.astenamic.new_discovery.biz.relation.shop.entity.ShopRelation;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class ShopRelationService {
    private YiDaSession yiDaSession;
    public List<ShopRelation> getRelations() {

        List<ShopRelation> data = new ArrayList<>();

        int len = 0;

        int page = 1;

        do {

            List<ShopRelation> d = this.yiDaSession.searchFormDataConditionsRequest(ShopRelation.class, null, page);

            len = d.size();

            data.addAll(d);

            page++;

        } while (len == 100);

        return data;
    }
}
