package com.astenamic.new_discovery.biz.scheduler;


import com.astenamic.new_discovery.biz.business.application.service.CostLossRateFlowAppService;
import com.astenamic.new_discovery.biz.business.application.service.PatrolRecordAppService;
import com.astenamic.new_discovery.biz.business.application.service.ShopRelationAppService;
import com.astenamic.new_discovery.biz.flow.warning.entity.EmployeeMeal;
import com.astenamic.new_discovery.biz.flow.warning.entity.FoodOnShelves;
import com.astenamic.new_discovery.biz.flow.warning.entity.MemberConsumptionCount;
import com.astenamic.new_discovery.biz.flow.warning.service.AbnormalPriceService;
import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.kitchendivision.application.service.MaterialCostCardAppService;
import com.astenamic.new_discovery.biz.purchase.application.PricingCommandDispatcher;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.SyncPricingToDbReq;
import com.astenamic.new_discovery.biz.takeout.service.TakeoutCommentService;
import com.astenamic.new_discovery.biz.wlift.service.ConsumeRecordService;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Component
@AllArgsConstructor
public class SchedulerV2 {

    private final AbnormalPriceService abnormalPriceService;

    private final AbstractEarlyWarningService<EmployeeMeal> employeeMealService;

    private final ConsumeRecordService consumeRecordService;

    private final AbstractEarlyWarningService<FoodOnShelves> foodOnShelvesService;

    private final AbstractEarlyWarningService<MemberConsumptionCount> memberConsumptionCountService;

    private final MaterialCostCardAppService materialCostCardAppService;

    private final PricingCommandDispatcher pricingCommandDispatcher;

    private final CostLossRateFlowAppService costLossRateFlowAppService;

    private final ShopRelationAppService shopRelationAppService;

    private final TakeoutCommentService takeoutCommentService;

    private final PatrolRecordAppService recordAppService;

    @ScheduledTask(name = "成本单价异常波动", cron = "0 30 8 * * ?")
    public void dailyMorningAtEightThirty() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        abnormalPriceService.detectAbnormalPriceFluctuation(yesterday);
    }

    @ScheduledTask(name = "员工餐预警", cron = "0 0 14 * * ?")
    public void employeeMealWarning() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        employeeMealService.employeeMealWarning(yesterday);
    }


    @ScheduledTask(name = "会员消费记录同步", cron = "0 0 1 * * ?")
    public void consumeRecordSync() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        consumeRecordService.syncConsumeRecord(yesterday);
    }

    @ScheduledTask(name = "会员消费记录预警", cron = "0 30 10 * * ?")
    public void memberConsumptionCountWarning() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        memberConsumptionCountService.memberConsumptionWarning(yesterday);
    }

    @ScheduledTask(name = "外卖下架预警", cron = "0 20 10 * * ?")
    @ScheduledTask(name = "外卖下架预警", cron = "0 00 12 * * ?")
    @ScheduledTask(name = "外卖下架预警", cron = "0 30 16 * * ?")
    @ScheduledTask(name = "外卖下架预警", cron = "0 00 19 * * ?")
    public void foodOnShelvesWarning() {
        foodOnShelvesService.takeoutFoodOnShelvesWarning();
    }

    @ScheduledTask(name = "成本卡同步", cron = "0 0 12 * * ?")
    public void syncMaterialCostCard() {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        materialCostCardAppService.syncToYida(yesterday);
    }

    @ScheduledTask(name = "同步奥琦玮定价单", cron = "0 0 1 * * ?")
    public void syncPriceSheetToDb() {
        pricingCommandDispatcher.dispatch(new SyncPricingToDbReq());
    }

    @ScheduledTask(name = "周度原材料预警", cron = "0 30 10 ? * TUE")
    public void weeklyMaterialReport() {
        LocalDateTime targetTime = LocalDateTime.now().minusDays(7);
        costLossRateFlowAppService.weeklyMaterialReport(targetTime);
    }

    @ScheduledTask(name = "同步门店关系表", cron = "0 0 1 * * ?")
    public void syncRelationsToDb() {
        shopRelationAppService.syncRelations();
    }

    @ScheduledTask(name = "外卖评论同步-最近三天", cron = "0 00 7 * * ?")
    public void syncTakeoutCommentsLastThreeDays() {
        LocalDateTime endTime = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
        LocalDateTime startTime = LocalDateTime.now().minusDays(3).with(LocalTime.MIN);
        takeoutCommentService.syncComments(startTime, endTime);
    }

    @ScheduledTask(name = "巡店记录同步", cron = "0 00 22 * * ?")
    public void syncPatrolRecord() {
        LocalDate today = LocalDate.now();
        LocalDate first = today.minusDays(30);

        first.datesUntil(today.plusDays(1))
                .forEach(day -> {
                    LocalDateTime startOfDay = day.atStartOfDay();
                    recordAppService.syncPatrolRecord(startOfDay);
                });
    }

}
