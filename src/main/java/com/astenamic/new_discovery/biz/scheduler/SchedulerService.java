package com.astenamic.new_discovery.biz.scheduler;

import java.util.List;

public interface SchedulerService {

    default List<SchedulerTask> dailyMorningAtEightThirty(){
        return List.of();
    }

    default List<SchedulerTask> dailyMorningAtTenThirty(){
        return List.of();
    }

    default List<SchedulerTask> dailyMorningAtTwelve(){
        return List.of();
    }

    default List<SchedulerTask> dailyNight(){
        return List.of();
    }

    default List<SchedulerTask> monthly(){
        return List.of();
    }

    default List<SchedulerTask> weekly(){
        return List.of();
    }

    default List<SchedulerTask> dailyMorningAtTenFortyFive() {
        return List.of();
    }

    default List<SchedulerTask> dailyAfternoonAtFifteen() {
        return List.of();
    }


    class SchedulerTask {
        private final String name;
        private final Runnable task;

        public SchedulerTask(String name, Runnable task) {
            this.name = name;
            this.task = task;
        }

        public String getName() {
            return name;
        }

        public Runnable getTask() {
            return task;
        }
    }

}
