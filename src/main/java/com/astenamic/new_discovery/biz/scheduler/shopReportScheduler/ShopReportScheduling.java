package com.astenamic.new_discovery.biz.scheduler.shopReportScheduler;

import com.astenamic.new_discovery.biz.report.shop.service.ShopMonthReportService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class ShopReportScheduling implements SchedulerService {

    private final ShopMonthReportService shopMonthReportService;


    // 十点半的定时
    @Override
    public List<SchedulerTask> dailyMorningAtTenThirty() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDay = now.minusDays(1);
        LocalDateTime beforeYesterday = lastDay.minusDays(1);

        return List.of(
                new SchedulerTask("经营看板日表更新", () -> shopMonthReportService.month(lastDay, 1)),
                new SchedulerTask("经营看板月表更新", () -> shopMonthReportService.month(now, 2)),
                new SchedulerTask("经营看板季表更新", () -> shopMonthReportService.month(now, 3))
        );
    }

    @Override
    public List<SchedulerTask> dailyMorningAtTwelve() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDay = now.minusDays(1);

        return List.of(
                new SchedulerTask("经营看板日表更新", () -> shopMonthReportService.month(lastDay, 1))
        );
    }

    @Override
    public List<SchedulerTask> dailyAfternoonAtFifteen() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDay = now.minusDays(1);
        LocalDateTime beforeYesterday = lastDay.minusDays(1);
        return List.of(
                new SchedulerTask("经营看板日表更新前一天的数据", () -> shopMonthReportService.month(beforeYesterday, 1))
        );
    }


    @Override
    public List<SchedulerTask> monthly() {

        List<SchedulerTask> list = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);

        LocalDateTime lastMonthOfDay = now.minusMonths(1);

        while (lastMonthOfDay.isBefore(now.minusSeconds(1))) {
            LocalDateTime d = lastMonthOfDay;
            List<SchedulerTask> o = List.of(
                    new SchedulerTask("经营看板日表更新上月数据", () -> shopMonthReportService.month(d, 1))
            );
            list.addAll(o);
            lastMonthOfDay = lastMonthOfDay.plusDays(1);
        }

        return list;
    }

    @Override
    public List<SchedulerTask> weekly() {
        LocalDateTime lastWeek = LocalDateTime.now().with(LocalTime.MIN).minusWeeks(1);
        return List.of(
                new SchedulerTask("经营看板周表更新", () -> shopMonthReportService.month(lastWeek, 4))
        );
    }
}
