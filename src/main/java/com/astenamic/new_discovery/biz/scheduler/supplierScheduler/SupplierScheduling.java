package com.astenamic.new_discovery.biz.scheduler.supplierScheduler;

import com.astenamic.new_discovery.ace.scm.supplier.service.SupplierService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class SupplierScheduling implements SchedulerService {

    private final SupplierService supplierService;


    @Override
    public List<SchedulerTask> dailyMorningAtEightThirty() {
        return List.of(
                new SchedulerTask("供应商定时更新", () -> supplierService.syncToYiDa(0, 100))
        );
    }
}
