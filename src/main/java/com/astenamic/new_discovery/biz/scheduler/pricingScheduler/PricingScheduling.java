package com.astenamic.new_discovery.biz.scheduler.pricingScheduler;

import com.astenamic.new_discovery.biz.report.pricing.service.PricingService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@AllArgsConstructor
public class PricingScheduling implements SchedulerService {

    private final PricingService pricingService;

    @Override
    public List<SchedulerTask> dailyMorningAtEightThirty() {

        LocalDateTime now = LocalDateTime.now().minusDays(1);

        return List.of(
                new SchedulerTask("宜搭基础数据更新", () -> pricingService.saveYiDaBase(now, 0, 100))

        );
    }

    @Override
    public List<SchedulerTask> dailyMorningAtTenThirty() {
        LocalDateTime now = LocalDateTime.now();

        String CprocessCode = "TPROC--837665718RERA5IS9VAGU74VBPGW29WRHH25M4";

        String FprocessCode = "TPROC--IWC66VA1QGBR1EQHC9LE75BZ17J73BGIDQ35M41";

        return List.of(
                new SchedulerTask("供应商定价写到宜搭中分为采集和生鲜", () -> pricingService.syncToYiDa(now, CprocessCode, FprocessCode))
        );
    }


    @Override
    public List<SchedulerTask> dailyNight() {

//        LocalDateTime now = LocalDateTime.now();

        return List.of(
                // 供应商定价回写定时
//                () -> pricingService.addSupplierPricingSheet(now)
        );
    }

}
