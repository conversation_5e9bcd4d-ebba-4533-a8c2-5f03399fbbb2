package com.astenamic.new_discovery.biz.scheduler.warningScheduler;

import com.astenamic.new_discovery.biz.flow.warning.service.base.AbstractEarlyWarningService;
import com.astenamic.new_discovery.biz.flow.warning.service.base.EarlyWarningService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@AllArgsConstructor
public class WarningScheduler implements SchedulerService {

    private EarlyWarningService foodBackWarningService;

    private EarlyWarningService takeoutDiscountService;

    @Override
    public List<SchedulerTask> dailyMorningAtTenThirty() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDay = now.minusDays(1);
        return List.of(
//                new SchedulerTask("外卖代金卷预警", () -> takeoutDiscountService.takeoutDiscountWarning(lastDay))
        );
    }

    @Override
    public List<SchedulerTask> dailyMorningAtTenFortyFive() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastDay = now.minusDays(1);
        return List.of(
                new SchedulerTask("退菜类型预警", () -> foodBackWarningService.foodBackWarning(lastDay))
        );
    }
}
