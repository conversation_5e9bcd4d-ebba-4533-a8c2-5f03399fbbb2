package com.astenamic.new_discovery.biz.scheduler.foodSalePredicateScheduler;

import com.astenamic.new_discovery.biz.kitchendivision.application.service.FoodSalePredicateService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@AllArgsConstructor
public class FoodSalePredicateScheduling implements SchedulerService {

    private final FoodSalePredicateService foodSalePredicateService;

    @Override
    public List<SchedulerTask> dailyMorningAtEightThirty() {

        LocalDateTime now = LocalDateTime.now();

        LocalDateTime lastDay = now.minusDays(1);

        return List.of(
                new SchedulerTask("预测数据昨日更新", () -> foodSalePredicateService.foodSaleToYiDa(lastDay)),
                new SchedulerTask("预测数据今日更新", () -> foodSalePredicateService.foodSaleToYiDa(now))
        );

    }
}
