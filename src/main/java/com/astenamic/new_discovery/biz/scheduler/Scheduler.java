package com.astenamic.new_discovery.biz.scheduler;

import com.astenamic.new_discovery.dingtalk.bot.DingTalkRobotService;
import com.astenamic.new_discovery.schedulerV2.annotation.ScheduledTask;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.function.Function;

@Component
@AllArgsConstructor
public class Scheduler {

    private static final Logger logger = LoggerFactory.getLogger(Scheduler.class);

    private static final DateTimeFormatter YMD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final List<? extends SchedulerService> schedulerServices;

    private final DingTalkRobotService dingTalkRobotService;

    @ScheduledTask(name = "每天8:30的批量定时任务", cron = "0 30 8 * * ?")
    public void dailyMorningAtEightThirty() {
        executeScheduledTasks("每天8:30", SchedulerService::dailyMorningAtEightThirty);
    }

    @ScheduledTask(name = "每天10:30的批量定时任务", cron = "0 30 10 * * ?")
    public void dailyMorningAtTenThirty() {
        executeScheduledTasks("每天10:30", SchedulerService::dailyMorningAtTenThirty);
    }

    @ScheduledTask(name = "每天10:45的批量定时任务", cron = "0 45 10 * * ?")
    public void dailyMorningAtTenFortyFive() {
        executeScheduledTasks("每天10:45", SchedulerService::dailyMorningAtTenFortyFive);
    }

    @ScheduledTask(name = "每天12:00的批量定时任务", cron = "0 0 12 * * ?")
    public void dailyMorningAtTwelve() {
        executeScheduledTasks("每天12:00", SchedulerService::dailyMorningAtTwelve);
    }

    @ScheduledTask(name = "每天15:00的批量定时任务", cron = "0 0 15 * * ?")
    public void dailyAfternoonAtFifteen() {
        executeScheduledTasks("每天15:00", SchedulerService::dailyAfternoonAtFifteen);
    }

    @ScheduledTask(name = "每天23:00的批量定时任务", cron = "0 00 23 * * ?")
    public void dailyNight() {
        executeScheduledTasks("每天23:00", SchedulerService::dailyNight);
    }

//    @Scheduled(cron = "0 0 1 1 * ?")
//    public void monthly() {
//        executeScheduledTasks("每月1号1:00", SchedulerService::monthly);
//    }

    @ScheduledTask(name = "每周一10:30的批量定时任务", cron = "0 30 10 ? * MON")
    public void weekly() {
        executeScheduledTasks("每周一10:30", SchedulerService::weekly);
    }

    private void executeScheduledTasks(String taskGroupName, Function<SchedulerService, List<SchedulerService.SchedulerTask>> taskExtractor) {

        List<SchedulerService.SchedulerTask> tasks = schedulerServices.stream()
                .flatMap(service -> taskExtractor.apply(service).stream())
                .toList();

        logger.info("{} 开始执行，共 {} 个子任务，时间: {}", taskGroupName, tasks.size(), LocalDateTime.now().format(YMD_FORMATTER));

        tasks.forEach(task -> {
            new SchedulerWrapper(task.getTask()::run, dingTalkRobotService, task.getName()).run();
        });


    }

    @FunctionalInterface
    public interface SchedulerFunction {

        void apply();

    }

    // 修改后的SchedulerWrapper，添加了dingTalkRobotService依赖，并在连续6次失败后调用发送消息方法
    private static class SchedulerWrapper {

        private int count = 1;
        private final SchedulerFunction function;
        private final DingTalkRobotService dingTalkRobotService;
        private final String taskName;

        private SchedulerWrapper(SchedulerFunction function, DingTalkRobotService dingTalkRobotService, String taskName) {
            this.function = function;
            this.dingTalkRobotService = dingTalkRobotService;
            this.taskName = taskName;
        }

        public void run() {
            if (count <= 6) {
                try {
                    long startTime = System.currentTimeMillis();
                    logger.warn("定时任务:{} 正在执行第 {} 次, 时间:{}", taskName, count, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    this.function.apply();
                    long endTime = System.currentTimeMillis();
                    logger.info("定时任务:{} 执行成功, 第 {} 次成功, 时间:{},耗时:{}", taskName, count, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), (endTime - startTime) / 1000);
                } catch (Exception e) {
                    logger.error("定时任务:{} 执行失败, 第 {} 次失败, 时间:{}", taskName, count, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    logger.error("", e);
                    count++;
                    if (count - 1 == 6) {
                        // 连续6次失败后调用钉钉机器人发送报警消息
                        dingTalkRobotService.sendTextMessage("monitor",
                                "任务 " + taskName + " 连续运行6次均失败",
                                null,
                                true);
                    } else {
                        try {
                            Thread.sleep(1000);
                        } catch (Exception ex) {
                            logger.error("异常中断", ex);
                        }
                    }
                    this.run();
                }
            }
        }
    }

}
