package com.astenamic.new_discovery.biz.scheduler.materialCostReportScheduler;

import com.astenamic.new_discovery.biz.report.material.service.MaterialCostReportService;
import com.astenamic.new_discovery.biz.scheduler.SchedulerService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@AllArgsConstructor
public class MaterialCostReportScheduling implements SchedulerService {

    private final MaterialCostReportService materialCostReportService;


    @Override
    public List<SchedulerTask> dailyMorningAtTwelve() {

        LocalDateTime lastDay = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

        return List.of(
                new SchedulerTask("成本卡更新", () -> materialCostReportService.saveCurrentToYiDa(lastDay))
        );
    }
}
