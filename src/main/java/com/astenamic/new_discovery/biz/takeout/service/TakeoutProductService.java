package com.astenamic.new_discovery.biz.takeout.service;

import com.astenamic.new_discovery.biz.takeout.entity.ShopRelationship;
import com.astenamic.new_discovery.biz.takeout.entity.dto.FoodGuqingDTO;
import com.astenamic.new_discovery.biz.takeout.repository.FoodGuqingRepository;
import com.astenamic.new_discovery.external.modal.dto.takeout.ProductDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetProductParam;
import com.astenamic.new_discovery.external.modal.enums.TakeoutPlatform;
import com.astenamic.new_discovery.biz.takeout.repository.ShopRelationshipRepository;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.ShopGoodsService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class TakeoutProductService {

    private static final Logger logger = LoggerFactory.getLogger(TakeoutProductService.class);

    private final List<ShopGoodsService> shopGoodsServices;

    private final ShopRelationshipRepository shopRelationshipRepository;

    private final FoodGuqingRepository foodGuqingRepository;

    private static final Map<TakeoutPlatform, Map<BrandEnum, List<String>>> PLATFORM_BRAND_FOODS = Map.of(
            TakeoutPlatform.MT, Map.of(
                    BrandEnum.XFX, List.of("牛肉焖土豆套餐", "甄选1+1+1随心配单人套餐", "现炒鲜牛肉套餐", "糖醋里脊套餐", "酸菜鱼套餐", "蛋黄鸡翅小份", "现炸糖醋里脊", "双人随心配套餐", "杭椒现炒鲜牛肉小份", "解暑绿豆汤", "荔香莲子叫花鸡", "酸菜甜笋炒鲜牛肉"),
                    BrandEnum.KJ, Collections.emptyList(),
                    BrandEnum.HDL, Collections.emptyList()
            ),
            TakeoutPlatform.ELEME, Map.of(
                    BrandEnum.XFX, List.of("牛肉焖土豆套餐", "甄选1+1+1随心配单人套餐", "现炒鲜牛肉套餐", "糖醋里脊套餐", "酸菜鱼套餐", "蛋黄鸡翅小份", "现炸糖醋里脊", "双人随心配套餐", "杭椒现炒鲜牛肉小份", "解暑绿豆汤", "荔香莲子叫花鸡", "酸菜甜笋炒鲜牛肉"),
                    BrandEnum.KJ, Collections.emptyList(),
                    BrandEnum.HDL, Collections.emptyList()
            )
    );

    /**
     * 查找所有下架商品
     */
    public Map<String, List<ProductDTO>> getOnShelfFood() {

        Map<String, List<ProductDTO>> result = new HashMap<>();

        Map<BrandEnum, List<ShopRelationship>> grouped = shopRelationshipRepository.findAll().stream()
                .filter(r -> StringUtils.isNotBlank(r.getBrandName()))
                .map(r -> Map.entry(BrandEnum.fromDesc(r.getBrandName()), r))
                .filter(e -> e.getKey() != null && e.getKey() != BrandEnum.DEFAULT)
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

        for (var entry : grouped.entrySet()) {

            BrandEnum brand = entry.getKey();

            for (ShopRelationship shopRel : entry.getValue()) {

                GetProductParam param = buildParam(shopRel);

                shopGoodsServices.stream()
                        .filter(svc -> svc.supports(brand))
                        .flatMap(svc -> {
                            try {
                                return svc.getGoodsList(brand, param).stream();
                            } catch (Exception e) {
                                logger.error("获取商品列表失败，brand={},shopId={},error={}", brand, shopRel.getShopId(), e.getMessage());
                                return Stream.<ProductDTO>empty();
                            }
                        })
                        .filter(p ->
                                matchesKeyword(brand, TakeoutPlatform.fromCode(p.getPlatform()), p.getProductName())
                                        && isOffShelf(TakeoutPlatform.fromCode(p.getPlatform()), p.getOnShelf())
                        )
                        .forEach(product -> result.computeIfAbsent(
                                shopRel.getShopId(), k -> new ArrayList<>()).add(product)
                        );

                List<FoodGuqingDTO> foodGuqingDTOS = foodGuqingRepository.findByShopId(shopRel.getShopId());
                foodGuqingDTOS.stream().map(dto -> {
                    ProductDTO productDTO = new ProductDTO();
                    productDTO.setProductName(dto.getProductName());
                    productDTO.setOnShelfTime(dto.getOnShelfTime());
                    productDTO.setPlatform("ts");
                    return productDTO;
                }).forEach(productDTO -> result.computeIfAbsent(shopRel.getShopId(), k -> new ArrayList<>()).add(productDTO));

            }
        }

        return result;
    }

    /**
     * 构造参数，处理 elmId 转换异常
     *
     * @return 参数或 null
     */
    private GetProductParam buildParam(ShopRelationship rel) {
        Long elmId;
        try {
            elmId = Long.parseLong(rel.getElmId());
        } catch (NumberFormatException e) {
            logger.error("elmId 转换失败: elmId='{}', shopId='{}'", rel.getElmId(), rel.getShopId(), e);
            elmId = null;
        }

        return GetProductParam.builder()
                .shopMapping(GetProductParam.ShopMapping.builder()
                        .mtThirdsShopId(rel.getMtThirdsShopId())
                        .elmId(elmId)
                        .shopId(rel.getShopId())
                        .build())
                .pageSize(200)
                .build();
    }

    /**
     * 判断下架：美团(onShelf=1)，饿了么(onShelf=0)
     */
    private boolean isOffShelf(TakeoutPlatform platform, int onShelf) {
        return (platform == TakeoutPlatform.MT && onShelf == 1)
                || (platform == TakeoutPlatform.ELEME && onShelf == 0);
    }

    /**
     * 是否匹配关键词
     */
    private boolean matchesKeyword(BrandEnum brand, TakeoutPlatform platform, String productName) {
        List<String> keywords = PLATFORM_BRAND_FOODS.getOrDefault(platform, Collections.emptyMap())
                .getOrDefault(brand, Collections.emptyList());
        if (productName == null) return false;
        return keywords.stream().anyMatch(productName::contains);
    }
}

