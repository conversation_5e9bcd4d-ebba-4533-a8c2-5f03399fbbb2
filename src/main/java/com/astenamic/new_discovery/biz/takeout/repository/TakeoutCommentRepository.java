package com.astenamic.new_discovery.biz.takeout.repository;

import com.astenamic.new_discovery.biz.takeout.entity.TakeoutComment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TakeoutCommentRepository extends JpaRepository<TakeoutComment, String> {

    @Query(value = """
            SELECT * FROM xfx_takeout_comment
            WHERE shop_id = :shopId
              AND rated_at::date BETWEEN TO_DATE(:startDate, 'YYYY-MM-DD') AND TO_DATE(:endDate, 'YYYY-MM-DD')
            """, nativeQuery = true)
    List<TakeoutComment> findByRatedAt(
            @Param("shopId") String shopId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );

}
