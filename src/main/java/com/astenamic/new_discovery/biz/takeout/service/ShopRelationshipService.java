package com.astenamic.new_discovery.biz.takeout.service;

import com.astenamic.new_discovery.biz.takeout.entity.ShopRelationship;
import com.astenamic.new_discovery.biz.takeout.repository.ShopRelationshipRepository;
import lombok.AllArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ShopRelationshipService {

    private final ShopRelationshipRepository shopRelationshipRepository;

}
