package com.astenamic.new_discovery.biz.takeout.service;

import com.astenamic.new_discovery.biz.takeout.entity.ShopRelationship;
import com.astenamic.new_discovery.biz.takeout.entity.TakeoutComment;
import com.astenamic.new_discovery.external.modal.enums.TakeoutPlatform;
import com.astenamic.new_discovery.biz.takeout.repository.ShopRelationshipRepository;
import com.astenamic.new_discovery.biz.takeout.repository.TakeoutCommentRepository;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetCommentParam;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.CommentService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class TakeoutCommentService {

    private static final Logger logger = LoggerFactory.getLogger(TakeoutCommentService.class);

    private final List<CommentService> commentServices;

    private final ShopRelationshipRepository shopRelationshipRepository;

    private final TakeoutCommentRepository takeoutCommentRepository;

    private final DateTimeFormatter ymdDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public void syncComments(LocalDateTime startTime, LocalDateTime endTime) {
        startTime = startTime.with(LocalTime.MIN);
        endTime = endTime.with(LocalTime.MAX);

        List<ShopRelationship> allShop = shopRelationshipRepository.findAll();

        Map<String, List<ShopRelationship>> brandMap = allShop.stream().filter(shopRelationship -> StringUtils.isNotBlank(shopRelationship.getBrandName())).collect(Collectors.groupingBy(ShopRelationship::getBrandName));

        for (Map.Entry<String, List<ShopRelationship>> entry : brandMap.entrySet()) {

            BrandEnum brandEnum = BrandEnum.fromDesc(entry.getKey());
            if (brandEnum == null || brandEnum.equals(BrandEnum.DEFAULT)) {
                logger.error("品牌名称不合法，brandName={}", entry.getKey());
                continue;
            }

            List<ShopRelationship> shopRelationships = entry.getValue();

            for (ShopRelationship shopRelationship : shopRelationships) {

                try {
                    GetCommentParam param = GetCommentParam.builder()
                            .pageSize(10)
                            .startTime(new GetCommentParam.TimeParam(startTime))
                            .endTime(new GetCommentParam.TimeParam(endTime))
                            .shopMapping(GetCommentParam.ShopMapping.builder()
                                    .shopId(shopRelationship.getShopId())
                                    .mtThirdsShopId(shopRelationship.getMtThirdsShopId())
                                    .elmId(shopRelationship.getElmId())
                                    .build())
                            .build();

                    List<TakeoutComment> newComments = commentServices.stream().filter(service -> service.supports(brandEnum)).flatMap(service -> service.getCommentList(brandEnum, param).stream().map(rate -> {
                        TakeoutComment comment = new TakeoutComment();
                        BeanUtils.copyProperties(rate, comment);
                        comment.setPlatform(TakeoutPlatform.fromCode(rate.getPlatform()));
                        comment.setShopId(shopRelationship.getShopId());
                        return comment;
                    })).toList();

                    List<TakeoutComment> oldComments = takeoutCommentRepository.findByRatedAt(shopRelationship.getShopId(), ymdDateTimeFormatter.format(startTime), ymdDateTimeFormatter.format(endTime));

                    // 创建新评论的commentId集合，用于快速查找
                    Set<String> newCommentIds = newComments.stream()
                            .map(TakeoutComment::getCommentId)
                            .collect(Collectors.toSet());

                    // 找出需要删除的评论（在oldComments中存在但在newComments中不存在）
                    List<TakeoutComment> commentsToDelete = oldComments.stream()
                            .filter(oldComment -> !newCommentIds.contains(oldComment.getCommentId()))
                            .toList();

                    // 删除不存在于新评论中的旧评论
                    if (!commentsToDelete.isEmpty()) {
                        takeoutCommentRepository.deleteAll(commentsToDelete);
                        logger.info("删除了{}条不存在于新评论中的旧评论，门店ID: {}", commentsToDelete.size(), shopRelationship.getShopId());
                    }

                    // 如果没有新评论，则跳过保存步骤
                    if (newComments.isEmpty()) continue;

                    // 为新评论设置已存在的ID（用于更新）
                    for (TakeoutComment newComment : newComments) {
                        for (TakeoutComment oldComment : oldComments) {
                            if (newComment.getCommentId().equals(oldComment.getCommentId())) {
                                newComment.setId(oldComment.getId());
                                break;
                            }
                        }
                    }

                    takeoutCommentRepository.saveAll(newComments);

                } catch (Exception e) {
                    logger.error("{}，门店评论查询失败，{}", shopRelationship.getShopId(), System.lineSeparator(), e);
                }

            }
        }
    }

}
