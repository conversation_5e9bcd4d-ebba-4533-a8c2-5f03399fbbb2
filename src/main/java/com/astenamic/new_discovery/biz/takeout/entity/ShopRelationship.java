package com.astenamic.new_discovery.biz.takeout.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.Data;

@Data
@Entity(name = "xfx_shop_mapping")
public class ShopRelationship {

    @Id
    private Long id;

    @Column(name = "brand_name")
    private String brandName;

    @Column(name = "shop_id")
    private String shopId;

    @Column(name = "pingzhi_id")
    private String pingzhiId;

    @Column(name = "pingzhi_name")
    private String pingzhiName;

    @Column(name = "acewill_id")
    private String acewillId;

    @Column(name = "acewill_name")
    private String acewillName;

    @Column(name = "mt_id")
    private String mtId;

    @Column(name = "mt_name")
    private String mtName;

    @Column(name = "mt_thirds_shop_id")
    private String mtThirdsShopId;

    @Column(name = "elm_id")
    private String elmId;

    @Column(name = "elm_name")
    private String elmName;

    @Column(name = "dp_id")
    private String dpId;

    @Column(name = "dp_name")
    private String dpName;

    @Column(name = "crm_id")
    private String crmId;

    @Column(name = "crm_name")
    private String crmName;

    @Column(name = "dy_id")
    private String dyId;

    @Column(name = "dy_name")
    private String dyName;
}
