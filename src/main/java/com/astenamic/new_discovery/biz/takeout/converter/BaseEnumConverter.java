package com.astenamic.new_discovery.biz.takeout.converter;

import com.astenamic.new_discovery.external.modal.enums.BaseEnum;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public abstract class BaseEnumConverter<E extends Enum<E> & BaseEnum> implements AttributeConverter<E, String> {

    private final Class<E> enumClass;

    public BaseEnumConverter(Class<E> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public String convertToDatabaseColumn(E attribute) {
        return attribute == null ? null : attribute.getCode();
    }

    @Override
    public E convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        for (E enumConstant : enumClass.getEnumConstants()) {
            if (dbData.equals(enumConstant.getCode())) {
                return enumConstant;
            }
        }
        throw new IllegalArgumentException("未知的数据库值: " + dbData);
    }
}