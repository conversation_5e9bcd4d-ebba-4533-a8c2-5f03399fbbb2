package com.astenamic.new_discovery.biz.takeout.entity;

import com.astenamic.new_discovery.biz.takeout.converter.JsonConverter;
import com.astenamic.new_discovery.biz.takeout.converter.TakeoutPlatformConverter;
import com.astenamic.new_discovery.external.modal.enums.TakeoutPlatform;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 外卖评论
 */
@Data
@Entity(name = "xfx_takeout_comment")
public class TakeoutComment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 评论id，和平台对应
     */
    @Column(name = "comment_id", columnDefinition = "varchar(32)")
    private String commentId;

    /**
     * 门店id
     */
    @Column(name = "shop_id", columnDefinition = "varchar(32)")
    private String shopId;

    /**
     * 门店名称
     */
    @Column(name = "shop_name", columnDefinition = "varchar(64)")
    private String shopName;

    /**
     * 外卖平台
     */
    @Column(name = "platform", columnDefinition = "varchar(16)")
    @Convert(converter = TakeoutPlatformConverter.class)
    private TakeoutPlatform platform;

    /**
     * 评论内容
     */
    @Column(name = "content", columnDefinition = "text")
    private String content;

    /**
     * 评论时间
     */
    @Column(name = "rated_at")
    private LocalDateTime ratedAt;

    /**
     * 追评
     */
    @Column(name = "add_content", columnDefinition = "text")
    private String addContent;

    /**
     * 追评时间
     */
    @Column(name = "add_rated_at")
    private LocalDateTime addRatedAt;

    /**
     * 是否回复 1:已回复 0:未回复
     */
    @Column(name = "is_replied", columnDefinition = "varchar(1)")
    private String isReplied;

    /**
     * 回复内容
     */
    @Column(name = "reply_content", columnDefinition = "text")
    private String replyContent;

    /**
     * 回复时间
     */
    @Column(name = "reply_time")
    private LocalDateTime replyTime;

    // 商家评分(星级评分)
    @Column(name = "shop_score")
    private Integer shopScore;

    // 口味评分
    @Column(name = "order_comment_score")
    private Integer orderCommentScore;

    // 包装评价分数
    @Column(name = "packing_score")
    private Integer packingScore;


    @Column(name = "json_info", columnDefinition = "text")
    @Convert(converter = JsonConverter.class)
    private String jsonInfo;

    /**
     * 品牌
     */
    @Column(name = "brand", columnDefinition = "varchar(32)")
    private String brand;


}
