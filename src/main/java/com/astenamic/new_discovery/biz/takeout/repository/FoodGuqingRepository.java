package com.astenamic.new_discovery.biz.takeout.repository;

import com.astenamic.new_discovery.biz.takeout.entity.FoodGuqing;
import com.astenamic.new_discovery.biz.takeout.entity.dto.FoodGuqingDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FoodGuqingRepository extends JpaRepository<FoodGuqing, Long> {

    @Query(value ="SELECT\n" +
            "  fdc_dishesname AS productName,\n" +
            "  warning_time as onShelfTime\n" +
            "FROM\n" +
            "  \"xfx_food_guqing\"\n" +
            "  A LEFT JOIN xfx_shop_mapping b ON A.fdi_ognid = b.pingzhi_id \n" +
            "WHERE\n" +
            "  end_date = '未解除沽清' AND b.shop_id = :shopId",
            nativeQuery = true)
    List<FoodGuqingDTO> findByShopId(@Param("shopId") String shopId);
}
