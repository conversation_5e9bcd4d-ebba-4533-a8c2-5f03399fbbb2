package com.astenamic.new_discovery.biz.takeout.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 品智菜品估清预警实体
 */
@Data
@Entity(name = "xfx_food_guqing")
public class FoodGuqing {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 类型 */
    @Column(name = "type")
    private Long type;

    /** DIN 门店编号 */
    @Column(name = "fdi_dnid")
    private Integer fdiDnid;

    /** 门店ID */
    @Column(name = "fdi_ognid")
    private String fdiOgnid;

    /** 估清类型 */
    @Column(name = "fdi_guqingtype")
    private Integer fdiGuqingType;

    /** 门店编码 */
    @Column(name = "fdc_ognno")
    private String fdcOgnNo;

    /** 门店名称 */
    @Column(name = "fdc_ognname")
    private String fdcOgnName;

    /** 菜品ID */
    @Column(name = "fdi_dishesid")
    private Integer fdiDishesId;

    /** 估清开始日期 */
    @Column(name = "start_date")
    private String startDate;

    /** 估清结束日期 */
    @Column(name = "end_date")
    private String endDate;

    /** 说明 */
    @Column(name = "remark")
    private String remark;

    /** 排序 */
    @Column(name = "sort")
    private Long sort;

    /** 菜品排序 */
    @Column(name = "food_sort")
    private String foodSort;

    /** 做法排序 */
    @Column(name = "mothod_sort")
    private String mothodSort;

    /** 估清原因 */
    @Column(name = "guqing_msg")
    private String guqingMsg;

    /** 菜品名称 */
    @Column(name = "fdc_dishesname")
    private String fdcDishesName;

    /** 快照时间 */
    @Column(name = "warning_time")
    private LocalDateTime warningTime;
}
