package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.LegalHolidaySalary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 法假工资表单 JPA 数据访问接口
 */
@Repository
public interface LegalHolidaySalaryDao extends JpaRepository<LegalHolidaySalary, Long> {
    LegalHolidaySalary findByObjectId(String objectId);
}
