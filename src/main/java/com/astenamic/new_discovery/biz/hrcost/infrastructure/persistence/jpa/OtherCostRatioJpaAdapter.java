package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.OtherCostRatio;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.OtherCostRatioRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.OtherCostRatioDao;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 其它费用占比 JPA 适配器
 */
@Repository
public class OtherCostRatioJpaAdapter
        extends BaseJpaRepositoryAdapter<OtherCostRatio, OtherCostRatioDao>
        implements OtherCostRatioRepository {

    public OtherCostRatioJpaAdapter(OtherCostRatioDao dao) {
        super(dao);
    }
}
