package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DailySalaryData;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DailySalaryDataRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.DailySalaryDataDao;
import org.springframework.stereotype.Repository;

/**
 * 日薪数据 JPA 适配器
 */
@Repository
public class DailySalaryDataJpaAdapter extends BaseJpaRepositoryAdapter<DailySalaryData, DailySalaryDataDao>
        implements DailySalaryDataRepository {

    public DailySalaryDataJpaAdapter(DailySalaryDataDao dao) {
        super(dao);
    }
}
