package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ALevelStoreBonus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * A级门店奖金标准表单 JPA 数据访问接口
 */
@Repository
public interface ALevelStoreBonusDao extends JpaRepository<ALevelStoreBonus, Long> {
    ALevelStoreBonus findByObjectId(String objectId);
}
