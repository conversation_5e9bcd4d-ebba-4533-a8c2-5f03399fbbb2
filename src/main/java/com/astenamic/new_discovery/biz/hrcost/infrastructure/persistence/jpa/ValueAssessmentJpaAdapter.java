package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ValueAssessment;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ValueAssessmentRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.ValueAssessmentDao;
import org.springframework.stereotype.Repository;

@Repository
public class ValueAssessmentJpaAdapter
        extends BaseJpaRepositoryAdapter<ValueAssessment, ValueAssessmentDao>
        implements ValueAssessmentRepository {

    public ValueAssessmentJpaAdapter(ValueAssessmentDao dao) {
        super(dao);
    }
}
