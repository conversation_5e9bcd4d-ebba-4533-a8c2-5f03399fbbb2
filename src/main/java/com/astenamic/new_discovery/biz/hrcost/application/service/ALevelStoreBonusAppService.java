package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ALevelStoreBonus;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ALevelStoreBonusRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ALevelStoreBonusAppService extends BaseAppService<ALevelStoreBonus> {

    public ALevelStoreBonusAppService(@Qualifier("ALevelStoreBonusJpaAdapter") ALevelStoreBonusRepository aLevelStoreBonusJpaAdapter,
                                      @Qualifier("ALevelStoreBonusYidaAdapter") ALevelStoreBonusRepository aLevelStoreBonusYidaAdapter) {
        super(aLevelStoreBonusJpaAdapter, aLevelStoreBonusYidaAdapter);
    }

}
