package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.service.base.AbstractFormService;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;

import java.util.List;
import java.util.stream.StreamSupport;

/**
 * 宜搭通用Repository适配器基类
 *
 * @param <T> 实体类型
 */
public abstract class BaseYidaAdapter<T extends BaseEntity> extends AbstractFormService<T> {

    public BaseYidaAdapter(Class<T> entityClass, YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(entityClass, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

    public String save(T entity) {
        try {
            return super.save(entity);
        } catch (Exception e) {
            throw new RuntimeException("保存实体到宜搭失败：" + e.getMessage(), e);
        }
    }

    public List<String> saveAll(Iterable<T> entities) {
        try {
            List<T> list = StreamSupport.stream(entities.spliterator(), false).toList();
            return super.batchSave(list);
        } catch (Exception e) {
            throw new RuntimeException("批量保存实体到宜搭失败：" + e.getMessage(), e);
        }
    }

    public List<T> findAll() {
        try {
            return super.getFormsByCond(SearchCondition.builder().get());
        } catch (Exception e) {
            throw new RuntimeException("查询所有宜搭实体失败：" + e.getMessage(), e);
        }
    }

    public T findByObjectId(String objectId) {
        try {
            return super.getFormByObjectId(objectId);
        } catch (Exception e) {
            throw new RuntimeException("通过objectId查询宜搭实体失败：" + e.getMessage(), e);
        }
    }

    public String deleteByObjectId(String objectId) {
        try {
            return super.deleteFormByObjectId(objectId);
        } catch (Exception e) {
            throw new RuntimeException("通过objectId删除宜搭实体失败：" + e.getMessage(), e);
        }
    }

    public List<String> deleteAllByObjectIds(List<String> ids) {
        try {
            return super.deleteFormByObjectIds(ids);
        } catch (Exception e) {
            throw new RuntimeException("批量删除宜搭实体失败：" + e.getMessage(), e);
        }
    }
}
