package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 部门负责人工资标准表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-7042AB825BC341489901C20190C99620P55U",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_department_leader_salary_standard")
public class DepartmentLeaderSalaryStandard extends BaseEntity {

    /** 副厨 */
    @FormField("numberField_mbkl9nj2")
    @Column(name = "sous_chef", precision = 10, scale = 2)
    private BigDecimal sousChef;

    /** 主管 */
    @FormField("numberField_mbkl9nj1")
    @Column(name = "supervisor", precision = 10, scale = 2)
    private BigDecimal supervisor;

    /** 厨师长 */
    @FormField("numberField_mbkl9nj0")
    @Column(name = "chef_head", precision = 10, scale = 2)
    private BigDecimal chefHead;

    /** 前厅经理 */
    @FormField("numberField_mbkl9niz")
    @Column(name = "front_office_manager", precision = 10, scale = 2)
    private BigDecimal frontOfficeManager;

    /** 营收区间 */
    @FormField("textField_mbkl9niy")
    @Column(name = "revenue_range", length = 255)
    private String revenueRange;
}
