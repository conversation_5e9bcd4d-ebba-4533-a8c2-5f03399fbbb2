package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.AccountingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.AccountingPerformanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class AccountingPerformanceAppService extends BaseAppService<AccountingPerformance> {


    public AccountingPerformanceAppService(AccountingPerformanceRepository accountingPerformanceJpaAdapter,
                                           AccountingPerformanceRepository accountingPerformanceYidaAdapter) {
        super(accountingPerformanceJpaAdapter, accountingPerformanceYidaAdapter);
    }
}
