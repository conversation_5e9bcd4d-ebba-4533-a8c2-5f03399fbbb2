package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DailySalaryData;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DailySalaryDataRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

/**
 * 日薪数据宜搭适配器
 */
@Repository
public class DailySalaryDataYidaAdapter
        extends BaseYidaAdapter<DailySalaryData>
        implements DailySalaryDataRepository {

    public DailySalaryDataYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(DailySalaryData.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
