package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 账管绩效表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-899D7931BE5A4B8CA36E22EC5609D333K8YL",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_accounting_performance")
public class AccountingPerformance extends BaseEntity {

    /** 合计 */
    @FormField("numberField_mbqb972t")
    @Column(name = "total", precision = 10, scale = 2)
    private BigDecimal total;

    /** 人数 */
    @FormField("numberField_mbqb972s")
    @Column(name = "person_count", precision = 10, scale = 0)
    private BigDecimal personCount;

    /** 账管绩效奖金基数 */
    @FormField("numberField_mbqb972q")
    @Column(name = "performance_bonus_base", precision = 10, scale = 2)
    private BigDecimal performanceBonusBase;

    /** 账管营收区间 */
    @FormField("textField_mbss4b29")
    @Column(name = "revenue_range", length = 255)
    private String revenueRange;

    /** 品牌 */
    @FormField("selectField_mbqb972n")
    @Column(name = "brand", length = 255)
    private String brand;
}
