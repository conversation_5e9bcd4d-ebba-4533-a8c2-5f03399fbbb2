package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.HousingSubsidy;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.HousingSubsidyRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class HousingSubsidyYidaAdapter
        extends BaseYidaAdapter<HousingSubsidy>
        implements HousingSubsidyRepository {

    public HousingSubsidyYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(HousingSubsidy.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
