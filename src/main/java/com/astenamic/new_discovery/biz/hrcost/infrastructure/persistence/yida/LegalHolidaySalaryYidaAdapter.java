package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.LegalHolidaySalary;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.LegalHolidaySalaryRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class LegalHolidaySalaryYidaAdapter
        extends BaseYidaAdapter<LegalHolidaySalary>
        implements LegalHolidaySalaryRepository {

    public LegalHolidaySalaryYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(LegalHolidaySalary.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
