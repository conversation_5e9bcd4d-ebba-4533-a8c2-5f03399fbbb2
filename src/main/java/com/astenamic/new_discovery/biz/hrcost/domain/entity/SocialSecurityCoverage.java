package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 企业社保及参保率表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-C7349F7751864D9EA431107BD02AD9D7EUHO",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_social_security_coverage")
public class SocialSecurityCoverage extends BaseEntity {

    /** 参保率 */
    @FormField("numberField_mbzu8wm8")
    @Column(name = "coverage_rate", precision = 8, scale = 4)
    private BigDecimal coverageRate;

    /** 全职在职人数 */
    @FormField("numberField_mbzu8wm7")
    @Column(name = "full_time_employees", precision = 10, scale = 0)
    private BigDecimal fullTimeEmployees;

    /** 社保参保总人数 */
    @FormField("numberField_mbzu8wm6")
    @Column(name = "total_insured_employees", precision = 10, scale = 0)
    private BigDecimal totalInsuredEmployees;

    /** 企业缴纳总金额 */
    @FormField("numberField_mbq68ah1")
    @Column(name = "total_company_contribution_amount", precision = 12, scale = 2)
    private BigDecimal totalCompanyContributionAmount;

    /** 企业缴纳总比例 */
    @FormField("numberField_mbq68ah0")
    @Column(name = "total_company_contribution_ratio", precision = 8, scale = 4)
    private BigDecimal totalCompanyContributionRatio;

    /** 大病 */
    @FormField("numberField_mbq68agz")
    @Column(name = "serious_illness_amount", precision = 10, scale = 2)
    private BigDecimal seriousIllnessAmount;

    /** 工伤险金额（企业） */
    @FormField("numberField_mbq68agy")
    @Column(name = "work_injury_amount_company", precision = 10, scale = 2)
    private BigDecimal workInjuryAmountCompany;

    /** 工伤险比例（企业） */
    @FormField("numberField_mbq68agx")
    @Column(name = "work_injury_ratio_company", precision = 8, scale = 4)
    private BigDecimal workInjuryRatioCompany;

    /** 工伤险基数 */
    @FormField("numberField_mbq68agw")
    @Column(name = "work_injury_base", precision = 10, scale = 2)
    private BigDecimal workInjuryBase;

    /** 失业险金额（企业） */
    @FormField("numberField_mbq68agv")
    @Column(name = "unemployment_amount_company", precision = 10, scale = 2)
    private BigDecimal unemploymentAmountCompany;

    /** 失业险比例（企业） */
    @FormField("numberField_mbq68agu")
    @Column(name = "unemployment_ratio_company", precision = 8, scale = 4)
    private BigDecimal unemploymentRatioCompany;

    /** 失业险基数 */
    @FormField("numberField_mbq68agt")
    @Column(name = "unemployment_base", precision = 10, scale = 2)
    private BigDecimal unemploymentBase;

    /** 生育险金额（企业） */
    @FormField("numberField_mbq68agq")
    @Column(name = "maternity_amount_company", precision = 10, scale = 2)
    private BigDecimal maternityAmountCompany;

    /** 生育险比例（企业） */
    @FormField("numberField_mbq68agr")
    @Column(name = "maternity_ratio_company", precision = 8, scale = 4)
    private BigDecimal maternityRatioCompany;

    /** 生育险基数 */
    @FormField("numberField_mbq68ags")
    @Column(name = "maternity_base", precision = 10, scale = 2)
    private BigDecimal maternityBase;

    /** 医疗险金额（企业） */
    @FormField("numberField_mbq68agp")
    @Column(name = "medical_amount_company", precision = 10, scale = 2)
    private BigDecimal medicalAmountCompany;

    /** 医疗险比例（企业） */
    @FormField("numberField_mbq68ago")
    @Column(name = "medical_ratio_company", precision = 8, scale = 4)
    private BigDecimal medicalRatioCompany;

    /** 医疗险基数 */
    @FormField("numberField_mbq68agn")
    @Column(name = "medical_base", precision = 10, scale = 2)
    private BigDecimal medicalBase;

    /** 养老险金额（企业） */
    @FormField("numberField_mbq68agm")
    @Column(name = "pension_amount_company", precision = 10, scale = 2)
    private BigDecimal pensionAmountCompany;

    /** 养老险比例（企业） */
    @FormField("numberField_mbq68agl")
    @Column(name = "pension_ratio_company", precision = 8, scale = 4)
    private BigDecimal pensionRatioCompany;

    /** 养老险基数 */
    @FormField("numberField_mbq68agk")
    @Column(name = "pension_base", precision = 10, scale = 2)
    private BigDecimal pensionBase;

    /** 城市 */
    @FormField("selectField_mbq68agj")
    @Column(name = "city", length = 255)
    private String city;

    /** 省份 */
    @FormField("selectField_mbq68ah2")
    @Column(name = "province", length = 255)
    private String province;

    /** 品牌 */
    @FormField("selectField_mbq68agh")
    @Column(name = "brand", length = 255)
    private String brand;
}
