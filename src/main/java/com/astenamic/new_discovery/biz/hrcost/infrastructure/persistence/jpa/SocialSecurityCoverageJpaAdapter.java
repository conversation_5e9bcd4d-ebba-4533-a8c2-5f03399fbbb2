package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SocialSecurityCoverage;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SocialSecurityCoverageRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.SocialSecurityCoverageDao;
import org.springframework.stereotype.Repository;

@Repository
public class SocialSecurityCoverageJpaAdapter
        extends BaseJpaRepositoryAdapter<SocialSecurityCoverage, SocialSecurityCoverageDao>
        implements SocialSecurityCoverageRepository {

    public SocialSecurityCoverageJpaAdapter(SocialSecurityCoverageDao dao) {
        super(dao);
    }
}
