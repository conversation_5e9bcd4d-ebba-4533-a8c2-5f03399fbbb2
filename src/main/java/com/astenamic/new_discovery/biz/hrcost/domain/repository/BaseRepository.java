package com.astenamic.new_discovery.biz.hrcost.domain.repository;


import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;

import java.util.List;

public interface BaseRepository<T extends BaseEntity> {

    String save(T entity);

    List<String> saveAll(Iterable<T> entities);

    List<T> findAll();

    T findByObjectId(String objectId);

    String deleteByObjectId(String objectId);

    List<String> deleteAllByObjectIds(List<String> objectIds);
}
