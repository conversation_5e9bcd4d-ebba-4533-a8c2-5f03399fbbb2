package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.HousingSubsidy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 住房补贴表单 JPA 数据访问接口
 */
@Repository
public interface HousingSubsidyDao extends JpaRepository<HousingSubsidy, Long> {

    HousingSubsidy findByObjectId(String objectId);
}
