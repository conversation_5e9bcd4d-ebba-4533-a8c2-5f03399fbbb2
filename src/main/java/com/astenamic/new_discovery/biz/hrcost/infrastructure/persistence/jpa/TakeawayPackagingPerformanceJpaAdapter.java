package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.TakeawayPackagingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.TakeawayPackagingPerformanceRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.TakeawayPackagingPerformanceDao;
import org.springframework.stereotype.Repository;

@Repository
public class TakeawayPackagingPerformanceJpaAdapter
        extends BaseJpaRepositoryAdapter<TakeawayPackagingPerformance, TakeawayPackagingPerformanceDao>
        implements TakeawayPackagingPerformanceRepository {

    public TakeawayPackagingPerformanceJpaAdapter(TakeawayPackagingPerformanceDao dao) {
        super(dao);
    }
}
