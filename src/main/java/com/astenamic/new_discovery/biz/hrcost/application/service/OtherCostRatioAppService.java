package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.OtherCostRatio;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.OtherCostRatioRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 人力成本模型 - 其它费用占比应用服务
 */
@Slf4j
@Service
public class OtherCostRatioAppService extends BaseAppService<OtherCostRatio> {

    public OtherCostRatioAppService(OtherCostRatioRepository otherCostRatioJpaAdapter,
                                    OtherCostRatioRepository otherCostRatioYidaAdapter) {
        super(otherCostRatioJpaAdapter, otherCostRatioYidaAdapter);
    }
}
