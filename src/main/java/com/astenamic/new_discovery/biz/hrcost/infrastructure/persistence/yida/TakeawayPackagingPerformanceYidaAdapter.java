package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.TakeawayPackagingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.TakeawayPackagingPerformanceRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class TakeawayPackagingPerformanceYidaAdapter
        extends BaseYidaAdapter<TakeawayPackagingPerformance>
        implements TakeawayPackagingPerformanceRepository {

    public TakeawayPackagingPerformanceYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(TakeawayPackagingPerformance.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
