package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * A级门店奖金标准表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-907904863F024F6EBFC8EC4893FE29B25L9S",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_a_level_store_bonus")
public class ALevelStoreBonus extends BaseEntity {

    /** 服务员/水吧/冷菜/蒸包灶/传菜/切配/打荷/PA/后勤 */
    @FormField("numberField_mc02ca5t")
    @Column(name = "general_staff_bonus", precision = 10, scale = 2)
    private BigDecimal generalStaffBonus;

    /** 迎宾/收银/炉台 */
    @FormField("numberField_mc02ca5s")
    @Column(name = "reception_cashier_bonus", precision = 10, scale = 2)
    private BigDecimal receptionCashierBonus;

    /** 领班/档口主管/面点副主管 */
    @FormField("numberField_mc02ca5r")
    @Column(name = "supervisor_bonus", precision = 10, scale = 2)
    private BigDecimal supervisorBonus;

    /** 营收区间 */
    @FormField("textField_mc02ca5q")
    @Column(name = "revenue_range", length = 255)
    private String revenueRange;
}
