package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;


@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class BaseEntity extends YidaObject {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 逻辑删除标志，0表示未删除，1表示已删除
    @Column(name = "del_flag")
    private String delFlag = "0";

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @LastModifiedDate
    @Column(name = "sync_time")
    private LocalDateTime syncTime;
}

