package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.StoreAbcDiscountRevenue;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.StoreAbcDiscountRevenueRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.StoreAbcDiscountRevenueDao;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 门店abc折后营收表 JPA 适配器
 */
@Repository
public class StoreAbcDiscountRevenueJpaAdapter
        extends BaseJpaRepositoryAdapter<StoreAbcDiscountRevenue, StoreAbcDiscountRevenueDao>
        implements StoreAbcDiscountRevenueRepository {

    public StoreAbcDiscountRevenueJpaAdapter(StoreAbcDiscountRevenueDao dao) {
        super(dao);
    }
}
