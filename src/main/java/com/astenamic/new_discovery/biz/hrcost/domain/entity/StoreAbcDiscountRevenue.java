package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人力成本模型 - 门店abc折后营收表
 * 对应宜搭表单：FORM-B2BBCAB5C45D41C29F2982C327D64DF2BIMI
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-B2BBCAB5C45D41C29F2982C327D64DF2BIMI",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_store_abc_discount_revenue")
public class StoreAbcDiscountRevenue extends BaseEntity {

    /** 实际人力资源费用 */
    @FormField("numberField_mciibz8v")
    @Column(name = "actual_hr_cost", precision = 12, scale = 2)
    private BigDecimal actualHrCost;

    /** abc折后营收 */
    @FormField("numberField_mceh3xbt")
    @Column(name = "abc_discount_revenue", precision = 12, scale = 2)
    private BigDecimal abcDiscountRevenue;

    /** 月份 */
    @FormField("textField_mceh3xbs")
    @Column(name = "month")
    private String month;

    /** 门店名称 */
    @FormField("textField_mceh3xbr")
    @Column(name = "store_name")
    private String storeName;
}
