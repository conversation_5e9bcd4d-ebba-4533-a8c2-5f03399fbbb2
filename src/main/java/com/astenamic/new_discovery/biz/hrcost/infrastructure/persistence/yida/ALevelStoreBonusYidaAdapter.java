package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ALevelStoreBonus;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ALevelStoreBonusRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class ALevelStoreBonusYidaAdapter
        extends BaseYidaAdapter<ALevelStoreBonus>
        implements ALevelStoreBonusRepository {

    public ALevelStoreBonusYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(ALevelStoreBonus.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
