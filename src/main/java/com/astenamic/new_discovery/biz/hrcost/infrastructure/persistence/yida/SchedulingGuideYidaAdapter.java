package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SchedulingGuide;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SchedulingGuideRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class SchedulingGuideYidaAdapter extends BaseYidaAdapter<SchedulingGuide> implements SchedulingGuideRepository {

    public SchedulingGuideYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(SchedulingGuide.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }

}
