package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ValueAssessment;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ValueAssessmentRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ValueAssessmentAppService extends BaseAppService<ValueAssessment> {

    public ValueAssessmentAppService(ValueAssessmentRepository valueAssessmentJpaAdapter,
                                     ValueAssessmentRepository valueAssessmentYidaAdapter) {
        super(valueAssessmentJpaAdapter, valueAssessmentYidaAdapter);
    }

}
