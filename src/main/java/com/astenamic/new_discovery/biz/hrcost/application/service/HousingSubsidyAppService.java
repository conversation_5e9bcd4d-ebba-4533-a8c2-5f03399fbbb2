package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.HousingSubsidy;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.HousingSubsidyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HousingSubsidyAppService extends BaseAppService<HousingSubsidy> {

    public HousingSubsidyAppService(HousingSubsidyRepository housingSubsidyJpaAdapter,
                                    HousingSubsidyRepository housingSubsidyYidaAdapter) {
        super(housingSubsidyJpaAdapter, housingSubsidyYidaAdapter);
    }

}
