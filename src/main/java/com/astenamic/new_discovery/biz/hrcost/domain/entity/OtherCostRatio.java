package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人力成本模型 - 其它费用占比
 * 对应宜搭表单：FORM-4C87A236AA7E45F68E6478D64D7D1BABHE20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-4C87A236AA7E45F68E6478D64D7D1BABHE20",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_other_cost_ratio")
public class OtherCostRatio extends BaseEntity {

    /** 其它费用占比 */
    @FormField("numberField_mccpmek9")
    @Column(name = "other_cost_ratio", precision = 8, scale = 4)
    private BigDecimal otherCostRatio;

    /** 月份 */
    @FormField("textField_mccpmek8")
    @Column(name = "month")
    private String month;

    /** 门店名称 */
    @FormField("textField_mccpmek7")
    @Column(name = "shop_name")
    private String shopName;
}
