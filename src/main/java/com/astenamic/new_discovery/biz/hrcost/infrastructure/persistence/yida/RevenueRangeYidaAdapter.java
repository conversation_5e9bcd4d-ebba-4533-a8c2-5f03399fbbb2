package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.RevenueRange;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.RevenueRangeRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class RevenueRangeYidaAdapter
        extends BaseYidaAdapter<RevenueRange>
        implements RevenueRangeRepository {

    public RevenueRangeYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(RevenueRange.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
