package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.AccountingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.AccountingPerformanceRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class AccountingPerformanceYidaAdapter
        extends BaseYidaAdapter<AccountingPerformance>
        implements AccountingPerformanceRepository {

    public AccountingPerformanceYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(AccountingPerformance.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
