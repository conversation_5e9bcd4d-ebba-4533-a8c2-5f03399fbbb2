package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.EmployeeSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.EmployeeSalaryStandardRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class EmployeeSalaryStandardYidaAdapter
        extends BaseYidaAdapter<EmployeeSalaryStandard>
        implements EmployeeSalaryStandardRepository {

    public EmployeeSalaryStandardYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(EmployeeSalaryStandard.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
