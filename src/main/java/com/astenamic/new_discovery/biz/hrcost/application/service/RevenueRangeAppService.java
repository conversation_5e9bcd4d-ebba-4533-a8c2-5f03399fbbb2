package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.RevenueRange;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.RevenueRangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RevenueRangeAppService extends BaseAppService<RevenueRange> {

    public RevenueRangeAppService(RevenueRangeRepository revenueRangeJpaAdapter,
                                  RevenueRangeRepository revenueRangeYidaAdapter) {
        super(revenueRangeJpaAdapter, revenueRangeYidaAdapter);
    }

}
