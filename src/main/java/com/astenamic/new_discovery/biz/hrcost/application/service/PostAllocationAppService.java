package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.PostAllocation;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.PostAllocationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PostAllocationAppService extends BaseAppService<PostAllocation> {

    public PostAllocationAppService(PostAllocationRepository postAllocationJpaAdapter,
                                    PostAllocationRepository postAllocationYidaAdapter) {
        super(postAllocationJpaAdapter, postAllocationYidaAdapter);
    }

}
