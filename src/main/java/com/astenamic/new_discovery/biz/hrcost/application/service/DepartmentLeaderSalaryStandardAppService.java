package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DepartmentLeaderSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DepartmentLeaderSalaryStandardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DepartmentLeaderSalaryStandardAppService extends BaseAppService<DepartmentLeaderSalaryStandard> {

    public DepartmentLeaderSalaryStandardAppService(DepartmentLeaderSalaryStandardRepository departmentLeaderSalaryStandardJpaAdapter,
                                                    DepartmentLeaderSalaryStandardRepository departmentLeaderSalaryStandardYidaAdapter) {
        super(departmentLeaderSalaryStandardJpaAdapter, departmentLeaderSalaryStandardYidaAdapter);
    }

}
