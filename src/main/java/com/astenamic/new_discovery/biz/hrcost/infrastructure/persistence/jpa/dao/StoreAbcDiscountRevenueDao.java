package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.StoreAbcDiscountRevenue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 门店abc折后营收表 JPA 数据访问接口
 */
@Repository
public interface StoreAbcDiscountRevenueDao extends JpaRepository<StoreAbcDiscountRevenue, Long> {
    
    /**
     * 根据宜搭对象ID查找实体
     */
    StoreAbcDiscountRevenue findByObjectId(String objectId);
}
