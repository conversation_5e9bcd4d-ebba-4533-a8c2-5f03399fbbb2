package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DailySalaryData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 日薪数据 JPA 数据访问接口
 */
@Repository
public interface DailySalaryDataDao extends JpaRepository<DailySalaryData, Long> {
    
    /**
     * 根据宜搭对象ID查找实体
     */
    DailySalaryData findByObjectId(String objectId);
}
