package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 法假工资表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-E7F4F0B0AD0644DFB7501A67F2C23B4B81FC",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_legal_holiday_salary")
public class LegalHolidaySalary extends BaseEntity {

    /** 法假工资合计 */
    @FormField("numberField_mbkkar9m")
    @Column(name = "legal_holiday_salary_total", precision = 10, scale = 2)
    private BigDecimal legalHolidaySalaryTotal;

    /** 法假天数 */
    @FormField("numberField_mbkkar9l")
    @Column(name = "holiday_days", precision = 10, scale = 2)
    private BigDecimal holidayDays;

    /** 法假工资系数 */
    @FormField("numberField_mbkkar9k")
    @Column(name = "holiday_salary_coefficient", precision = 10, scale = 4)
    private BigDecimal holidaySalaryCoefficient;

    /** 省份 */
    @FormField("selectField_mbt6msvw")
    @Column(name = "province", length = 255)
    private String province;

    /** 月份 */
    @FormField("dateField_mbkkar9j")
    @Column(name = "month")
    private LocalDate month;
}
