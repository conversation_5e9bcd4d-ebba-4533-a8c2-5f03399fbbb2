package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.LegalHolidaySalary;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.LegalHolidaySalaryRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.LegalHolidaySalaryDao;
import org.springframework.stereotype.Repository;

@Repository
public class LegalHolidaySalaryJpaAdapter
        extends BaseJpaRepositoryAdapter<LegalHolidaySalary, LegalHolidaySalaryDao>
        implements LegalHolidaySalaryRepository {

    public LegalHolidaySalaryJpaAdapter(LegalHolidaySalaryDao dao) {
        super(dao);
    }
}
