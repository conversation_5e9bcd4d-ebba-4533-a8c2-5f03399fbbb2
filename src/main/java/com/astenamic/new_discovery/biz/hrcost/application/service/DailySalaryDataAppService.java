package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DailySalaryData;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DailySalaryDataRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 日薪数据应用服务
 * 人力成本模型 - 日薪数据管理
 */
@Service
public class DailySalaryDataAppService extends BaseAppService<DailySalaryData> {

    public DailySalaryDataAppService(
            @Qualifier("dailySalaryDataJpaAdapter") DailySalaryDataRepository jpaAdapter,
            @Qualifier("dailySalaryDataYidaAdapter") DailySalaryDataRepository yidaAdapter) {
        super(jpaAdapter, yidaAdapter);
    }
}
