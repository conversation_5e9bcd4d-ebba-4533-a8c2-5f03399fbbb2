package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.EmployeeSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.EmployeeSalaryStandardRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.EmployeeSalaryStandardDao;
import org.springframework.stereotype.Repository;

@Repository
public class EmployeeSalaryStandardJpaAdapter
        extends BaseJpaRepositoryAdapter<EmployeeSalaryStandard, EmployeeSalaryStandardDao>
        implements EmployeeSalaryStandardRepository {

    public EmployeeSalaryStandardJpaAdapter(EmployeeSalaryStandardDao dao) {
        super(dao);
    }
}
