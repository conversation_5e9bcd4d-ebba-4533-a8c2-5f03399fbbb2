package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.StoreAbcDiscountRevenue;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.StoreAbcDiscountRevenueRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 人力成本模型 - 门店abc折后营收表应用服务
 */
@Slf4j
@Service
public class StoreAbcDiscountRevenueAppService extends BaseAppService<StoreAbcDiscountRevenue> {

    public StoreAbcDiscountRevenueAppService(StoreAbcDiscountRevenueRepository storeAbcDiscountRevenueJpaAdapter,
                                             StoreAbcDiscountRevenueRepository storeAbcDiscountRevenueYidaAdapter) {
        super(storeAbcDiscountRevenueJpaAdapter, storeAbcDiscountRevenueYidaAdapter);
    }
}
