package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.StoreAbcDiscountRevenue;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.StoreAbcDiscountRevenueRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 门店abc折后营收表 Yida 适配器
 */
@Repository
public class StoreAbcDiscountRevenueYidaAdapter
        extends BaseYidaAdapter<StoreAbcDiscountRevenue>
        implements StoreAbcDiscountRevenueRepository {

    public StoreAbcDiscountRevenueYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(StoreAbcDiscountRevenue.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
