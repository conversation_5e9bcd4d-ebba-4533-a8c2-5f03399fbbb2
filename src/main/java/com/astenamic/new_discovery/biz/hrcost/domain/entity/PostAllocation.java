package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 岗位编制表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-18B6D8802ADA4D99AD2ADDCE124C8186J8MG",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_post_allocation")
public class PostAllocation extends BaseEntity {

    /** 500平以上定编人数 */
    @FormField("numberField_mbhr9nkm")
    @Column(name = "allocation_500_plus", precision = 10, scale = 0)
    private BigDecimal allocation500Plus;

    /** 400平-500平以内定编人数 */
    @FormField("numberField_mbhr9nkl")
    @Column(name = "allocation_400_500", precision = 10, scale = 0)
    private BigDecimal allocation400500;

    /** 300平-400平以内定编人数 */
    @FormField("numberField_mbhr9nkk")
    @Column(name = "allocation_300_400", precision = 10, scale = 0)
    private BigDecimal allocation300400;

    /** 250平-300平以内定编人数 */
    @FormField("numberField_mbhr9nkj")
    @Column(name = "allocation_250_300", precision = 10, scale = 0)
    private BigDecimal allocation250300;

    /** 250平以内定编人数 */
    @FormField("numberField_mbhr9nki")
    @Column(name = "allocation_250_less", precision = 10, scale = 0)
    private BigDecimal allocation250Less;

    /** 岗位类型 */
    @FormField("selectField_mbsthm5p")
    @Column(name = "post_type")
    private String postType;

    /** 岗位 */
    @FormField("textField_mbsthm5o")
    @Column(name = "post_name", length = 255)
    private String postName;

    /** 营收 */
    @FormField("numberField_mc48hyl0")
    @Column(name = "revenue", precision = 12, scale = 2)
    private BigDecimal revenue;

    /** 营收（隐藏） */
    @FormField("selectField_mbhr9nkg")
    @Column(name = "hidden_revenue")
    private String hiddenRevenue;

    /** 品牌 */
    @FormField("selectField_mbhr9nkf")
    @Column(name = "brand")
    private String brand;
}
