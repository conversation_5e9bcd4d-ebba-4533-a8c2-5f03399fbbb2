package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 外卖打包绩效表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-2A7AD4C001A34B29A0A843268058109616PI",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_takeaway_packaging_performance")
public class TakeawayPackagingPerformance extends BaseEntity {

    /** 合计 */
    @FormField("numberField_mbqb972t")
    @Column(name = "total", precision = 10, scale = 2)
    private BigDecimal total;

    /** 人数 */
    @FormField("numberField_mbqb972s")
    @Column(name = "person_count", precision = 10, scale = 0)
    private BigDecimal personCount;

    /** 外卖打包岗奖金基数 */
    @FormField("numberField_mbqb972r")
    @Column(name = "packaging_bonus_base", precision = 10, scale = 2)
    private BigDecimal packagingBonusBase;

    /** 外卖营收区间 */
    @FormField("selectField_mbqb972p")
    @Column(name = "takeaway_revenue_range", length = 255)
    private String takeawayRevenueRange;

    /** 还原营收区间 */
    @FormField("textField_mbssjcht")
    @Column(name = "restored_revenue_range", length = 255)
    private String restoredRevenueRange;

    /** 品牌 */
    @FormField("selectField_mbqb972n")
    @Column(name = "brand", length = 255)
    private String brand;
}
