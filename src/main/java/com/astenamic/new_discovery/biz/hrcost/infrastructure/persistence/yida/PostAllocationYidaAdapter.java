package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.PostAllocation;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.PostAllocationRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class PostAllocationYidaAdapter
        extends BaseYidaAdapter<PostAllocation>
        implements PostAllocationRepository {

    public PostAllocationYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(PostAllocation.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
