package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.schedulerV2.model.TaskDefinition;
import com.astenamic.new_discovery.schedulerV2.service.SchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * 人力成本模型定时任务服务
 * 负责将所有 BaseAppService 子类的 yidaBatchToDb 方法注册到 schedulerV2 定时任务模块
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HrcostSchedulerService {

    private final SchedulerService schedulerService;
    private final ApplicationContext applicationContext;

    /**
     * 在 Spring 容器初始化完成后自动注册所有 BaseAppService 子类的定时任务
     */
    @PostConstruct
    public void registerAllBaseAppServiceTasks() {
        log.info("开始注册人力成本模型 BaseAppService 子类的定时任务...");

        try {
            // 获取所有 BaseAppService 的子类 Bean
            Map<String, BaseAppService> baseAppServices = applicationContext.getBeansOfType(BaseAppService.class);

            int registeredCount = 0;

            for (Map.Entry<String, BaseAppService> entry : baseAppServices.entrySet()) {
                String beanName = entry.getKey();
                BaseAppService<?> service = entry.getValue();

                try {
                    registerSingleTask(beanName, service);
                    registeredCount++;
                    log.info("成功注册定时任务: {}", beanName);
                } catch (Exception e) {
                    log.error("注册定时任务失败: {}, 错误: {}", beanName, e.getMessage(), e);
                }
            }

            log.info("人力成本模型定时任务注册完成，共注册 {} 个任务", registeredCount);

        } catch (Exception e) {
            log.error("注册人力成本模型定时任务失败", e);
        }
    }

    /**
     * 注册单个 BaseAppService 的定时任务
     */
    private void registerSingleTask(String beanName, BaseAppService<?> service) {
        // 获取实体类名
        String entityName = getEntityClassName(service);
        
        // 构建任务定义
        TaskDefinition taskDefinition = TaskDefinition.builder()
                .taskId("hrcost-yida-sync-" + beanName)
                .name("人力成本模型-宜搭数据同步-" + entityName)
                .description("自动同步 " + entityName + " 数据从宜搭到数据库")
                .cronExpression("0 0 2 * * ?") // 每天凌晨2点执行
                .schedulerTaskType(TaskDefinition.SchedulerTaskType.IO_INTENSIVE)
                .priority(5)
                .maxRetries(3)
                .retryInterval(5000) // 5秒重试间隔
                .alertOnFailure(true)
                .executorType("queue")
                .build();

        // 创建任务执行逻辑
        Runnable task = createSyncTask( service, entityName);

        // 注册到 schedulerV2 服务
        schedulerService.registerTask(taskDefinition, task);
        
        log.debug("注册定时任务成功: taskId={}, entityName={}", taskDefinition.getTaskId(), entityName);
    }

    /**
     * 创建同步任务的执行逻辑
     */
    private Runnable createSyncTask(BaseAppService<?> service, String entityName) {
        return () -> {
            long startTime = System.currentTimeMillis();
            log.info("开始执行宜搭数据同步任务: {}", entityName);
            
            try {
                boolean success = service.yidaBatchToDb();
                long duration = System.currentTimeMillis() - startTime;
                
                if (success) {
                    log.info("宜搭数据同步任务执行成功: {}, 耗时: {}ms", entityName, duration);
                } else {
                    log.warn("宜搭数据同步任务执行失败: {}, 耗时: {}ms", entityName, duration);
                    throw new RuntimeException("同步任务返回失败状态");
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("宜搭数据同步任务执行异常: {}, 耗时: {}ms, 错误: {}", 
                         entityName, duration, e.getMessage(), e);
                throw new RuntimeException("同步任务执行异常: " + e.getMessage(), e);
            }
        };
    }

    /**
     * 获取 BaseAppService 泛型参数中的实体类名
     */
    private String getEntityClassName(BaseAppService<?> service) {
        try {
            // 获取服务类的泛型父类
            Type genericSuperclass = service.getClass().getGenericSuperclass();
            
            if (genericSuperclass instanceof ParameterizedType parameterizedType) {
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                
                if (actualTypeArguments.length > 0 && actualTypeArguments[0] instanceof Class<?> entityClass) {
                    return entityClass.getSimpleName();
                }
            }
            
            // 如果无法获取泛型参数，使用服务类名
            String serviceName = service.getClass().getSimpleName();
            return serviceName.replace("AppService", "");
            
        } catch (Exception e) {
            log.warn("获取实体类名失败，使用默认名称: {}", e.getMessage());
            return service.getClass().getSimpleName().replace("AppService", "");
        }
    }

}
