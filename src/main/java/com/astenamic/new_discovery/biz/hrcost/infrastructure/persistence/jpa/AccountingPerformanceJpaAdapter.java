package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.AccountingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.AccountingPerformanceRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.AccountingPerformanceDao;
import org.springframework.stereotype.Repository;

@Repository
public class AccountingPerformanceJpaAdapter
        extends BaseJpaRepositoryAdapter<AccountingPerformance, AccountingPerformanceDao>
        implements AccountingPerformanceRepository {

    public AccountingPerformanceJpaAdapter(AccountingPerformanceDao dao) {
        super(dao);
    }
}
