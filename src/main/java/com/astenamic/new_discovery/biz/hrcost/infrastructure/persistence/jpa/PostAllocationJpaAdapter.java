package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.PostAllocation;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.PostAllocationRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.PostAllocationDao;
import org.springframework.stereotype.Repository;

@Repository
public class PostAllocationJpaAdapter
        extends BaseJpaRepositoryAdapter<PostAllocation, PostAllocationDao>
        implements PostAllocationRepository {

    public PostAllocationJpaAdapter(PostAllocationDao dao) {
        super(dao);
    }
}
