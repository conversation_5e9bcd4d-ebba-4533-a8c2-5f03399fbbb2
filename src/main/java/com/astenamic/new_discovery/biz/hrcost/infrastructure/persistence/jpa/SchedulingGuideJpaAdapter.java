package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SchedulingGuide;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SchedulingGuideRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.SchedulingGuideDao;
import org.springframework.stereotype.Repository;

@Repository
public class SchedulingGuideJpaAdapter
        extends BaseJpaRepositoryAdapter<SchedulingGuide, SchedulingGuideDao>
        implements SchedulingGuideRepository {

    public SchedulingGuideJpaAdapter(SchedulingGuideDao dao) {
        super(dao);
    }
}
