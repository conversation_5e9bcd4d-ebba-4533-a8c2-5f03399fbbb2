package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.OtherCostRatio;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.OtherCostRatioRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 其它费用占比 Yida 适配器
 */
@Repository
public class OtherCostRatioYidaAdapter
        extends BaseYidaAdapter<OtherCostRatio>
        implements OtherCostRatioRepository {

    public OtherCostRatioYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(OtherCostRatio.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
