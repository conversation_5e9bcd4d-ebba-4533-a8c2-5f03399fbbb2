package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ValueAssessment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ValueAssessmentDao extends JpaRepository<ValueAssessment, Long> {

    ValueAssessment findByObjectId(String objectId);
}
