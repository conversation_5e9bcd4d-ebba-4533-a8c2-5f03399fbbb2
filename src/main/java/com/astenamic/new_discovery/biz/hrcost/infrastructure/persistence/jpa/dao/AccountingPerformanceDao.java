package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.AccountingPerformance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账管绩效表单 JPA 数据访问接口
 */
@Repository
public interface AccountingPerformanceDao extends JpaRepository<AccountingPerformance, Long> {

    AccountingPerformance findByObjectId(String employeeId);

}
