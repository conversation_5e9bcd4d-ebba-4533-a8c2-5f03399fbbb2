package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人均产值考核标准表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-0B29B3B1026440F9BEB013926AEE2FE7TJ59",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_value_assessment")
public class ValueAssessment extends BaseEntity {

    /** 四季风情下沙-奖金 */
    @FormField("numberField_mbzxnqez")
    @Column(name = "bonus_xia_sha", precision = 10, scale = 2)
    private BigDecimal bonusXiaSha;

    /** 四季风情柯桥-奖金 */
    @FormField("numberField_mbzxnqey")
    @Column(name = "bonus_ke_qiao", precision = 10, scale = 2)
    private BigDecimal bonusKeQiao;

    /** 蝴蝶里-奖金 */
    @FormField("numberField_mbzxnqex")
    @Column(name = "bonus_hu_die_li", precision = 10, scale = 2)
    private BigDecimal bonusHuDieLi;

    /** 烤匠-奖金 */
    @FormField("numberField_mbzxnqew")
    @Column(name = "bonus_kao_jiang", precision = 10, scale = 2)
    private BigDecimal bonusKaoJiang;

    /** 新发现CDE商圈-奖金 */
    @FormField("numberField_mbzxnqev")
    @Column(name = "bonus_cde", precision = 10, scale = 2)
    private BigDecimal bonusCDE;

    /** 新发现AB商圈-奖金 */
    @FormField("numberField_mbzxnqeu")
    @Column(name = "bonus_ab", precision = 10, scale = 2)
    private BigDecimal bonusAB;

    /** 人均产值区间 */
    @FormField("textField_mbzxnqer")
    @Column(name = "value_range", length = 255)
    private String valueRange;
}
