package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.BaseRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public abstract class BaseAppService<T extends BaseEntity> {


    private static final Logger log = LoggerFactory.getLogger(BaseAppService.class);

    protected final BaseRepository<T> jpaAdapter;

    protected final BaseRepository<T> yidaAdapter;

    public BaseAppService(BaseRepository<T> jpaAdapter, BaseRepository<T> yidaAdapter) {
        this.jpaAdapter = jpaAdapter;
        this.yidaAdapter = yidaAdapter;
    }

    /**
     * 批量同步宜搭数据到数据库
     *
     * @return 是否同步成功
     */
    public boolean yidaBatchToDb() {
        List<T> yidaList = yidaAdapter.findAll();
        if (yidaList.isEmpty()) {
            log.warn("宜搭数据为空，无法同步到数据库");
            return false;
        }
        List<T> dbList = jpaAdapter.findAll();

        // 1. 数据对齐：让 yidaList 的 id 和 dbList 对应
        yidaList.forEach(yida -> {
            dbList.stream()
                    .filter(d -> d.getObjectId().equals(yida.getObjectId()))
                    .findFirst().ifPresent(db -> yida.setId(db.getId()));
        });

        // 2. 逻辑删除：db里有但yida没有的，全部逻辑删除
        List<String> yidaObjectIds = yidaList.stream()
                .map(T::getObjectId)
                .toList();
        List<String> toDeleteObjectIds = dbList.stream()
                .map(T::getObjectId)
                .filter(dbObjectId -> !yidaObjectIds.contains(dbObjectId))
                .toList();

        if (!toDeleteObjectIds.isEmpty()) {
            jpaAdapter.deleteAllByObjectIds(toDeleteObjectIds);
        }

        // 3. 保存/更新
        List<String> savedList = jpaAdapter.saveAll(yidaList);
        if (savedList.isEmpty()) {
            log.warn("批量同步宜搭数据到数据库失败");
            return false;
        }
        return savedList.size() == yidaList.size();
    }


    /**
     * 实时同步宜搭数据到数据库
     *
     * @param objectId 宜搭数据的唯一标识
     * @return 是否同步成功
     */
    public boolean yidaToDb(String objectId) {
        T yida = yidaAdapter.findByObjectId(objectId);
        T db = jpaAdapter.findByObjectId(objectId);
        if (yida == null) {
            log.warn("未找到宜搭数据,尝试删除db的数据: {}", objectId);
            return StringUtils.isNotBlank(jpaAdapter.deleteByObjectId(objectId));
        } else {
            if (db != null) {
                yida.setId(db.getId());
            }
            return jpaAdapter.save(yida) != null;
        }

    }
}
