package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DepartmentLeaderSalaryStandard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 部门负责人工资标准表单 JPA 数据访问接口
 */
@Repository
public interface DepartmentLeaderSalaryStandardDao extends JpaRepository<DepartmentLeaderSalaryStandard, Long> {
    DepartmentLeaderSalaryStandard findByObjectId(String objectId);
}
