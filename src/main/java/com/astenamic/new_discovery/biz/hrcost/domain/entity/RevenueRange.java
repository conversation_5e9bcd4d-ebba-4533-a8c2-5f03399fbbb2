package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 普通表单营收区间表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(value = "FORM-318209827E054A2691BC87A30A58C1D6DDIM",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81")
@Entity(name = "xfx_revenue_range")
public class RevenueRange extends BaseEntity {


    // A级门店奖金-营收区间
    @FormField("selectField_mc02pmoh")
    @Column(name = "revenue_range_a_store")
    private String revenueRangeAStore;

    // 外卖-岗还原营收区间
    @FormField("selectField_mbssp1dx")
    @Column(name = "revenue_range_takeaway")
    private String revenueRangeTakeaway;

    // 账管-营收区间
    @FormField("selectField_mbssp1dy")
    @Column(name = "revenue_range_accounting")
    private String revenueRangeAccounting;

    // 营收区间
    @FormField("textField_mbsxmh0r")
    @Column(name = "revenue_range_description", length = 255)
    private String revenueRangeDescription;

    // 营收
    @FormField("numberField_mc488dar")
    @Column(name = "revenue", precision = 12, scale = 2)
    private BigDecimal revenue;

    // 营收（隐藏）
    @FormField("textField_mbspsygy")
    @Column(name = "hidden_revenue", length = 255)
    private String hiddenRevenue;
}

