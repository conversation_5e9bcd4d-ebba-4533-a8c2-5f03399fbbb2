package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SchedulingGuide;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 排班指引建议人数表单 JPA 数据访问接口
 */
@Repository
public interface SchedulingGuideDao extends JpaRepository<SchedulingGuide, Long> {

    SchedulingGuide findByObjectId(String objectId);

}
