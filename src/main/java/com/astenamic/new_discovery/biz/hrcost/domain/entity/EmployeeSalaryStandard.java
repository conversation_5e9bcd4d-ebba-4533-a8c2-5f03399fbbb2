package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 员工工资标准表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-69E1500B8D114190A74FBF4E373C4EE7GSM9",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_employee_salary_standard")
public class EmployeeSalaryStandard extends BaseEntity {

    /** 工资标准 */
    @FormField("numberField_mbkjurwn")
    @Column(name = "salary_standard", precision = 10, scale = 2)
    private BigDecimal salaryStandard;

    /** 岗位 */
    @FormField("textField_mbyro06y")
    @Column(name = "post", length = 255)
    private String post;

    /** 商圈等级 */
    @FormField("selectField_mbkjurwl")
    @Column(name = "business_district_level")
    private String businessDistrictLevel;

    /** 品牌 */
    @FormField("selectField_mbkjurwk")
    @Column(name = "brand")
    private String brand;
}
