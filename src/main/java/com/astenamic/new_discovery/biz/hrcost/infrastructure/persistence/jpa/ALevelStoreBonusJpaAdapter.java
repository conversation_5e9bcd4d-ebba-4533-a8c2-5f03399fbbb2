package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ALevelStoreBonus;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ALevelStoreBonusRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.ALevelStoreBonusDao;
import org.springframework.stereotype.Repository;

@Repository
public class ALevelStoreBonusJpaAdapter
        extends BaseJpaRepositoryAdapter<ALevelStoreBonus, ALevelStoreBonusDao>
        implements ALevelStoreBonusRepository {

    public ALevelStoreBonusJpaAdapter(ALevelStoreBonusDao dao) {
        super(dao);
    }
}
