package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DepartmentLeaderSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DepartmentLeaderSalaryStandardRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class DepartmentLeaderSalaryStandardYidaAdapter
        extends BaseYidaAdapter<DepartmentLeaderSalaryStandard>
        implements DepartmentLeaderSalaryStandardRepository {

    public DepartmentLeaderSalaryStandardYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(DepartmentLeaderSalaryStandard.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
