package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.EmployeeSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.EmployeeSalaryStandardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EmployeeSalaryStandardAppService extends BaseAppService<EmployeeSalaryStandard> {

    public EmployeeSalaryStandardAppService(EmployeeSalaryStandardRepository employeeS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dapter,
                                            EmployeeSalaryStandardRepository employeeSalaryStandardYidaAdapter) {
        super(employeeSalaryStandardJpaAdapter, employeeSalaryStandardYidaAdapter);
    }

}
