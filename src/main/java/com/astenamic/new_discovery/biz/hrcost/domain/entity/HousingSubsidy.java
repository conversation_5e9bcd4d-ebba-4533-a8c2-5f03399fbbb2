package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 住房补贴表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-FC5744E74F0C42E28B681996BF7DAD0EGIDJ",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_housing_subsidy")
public class HousingSubsidy extends BaseEntity {

    /** 经理厨师长住房补贴 */
    @FormField("numberField_mbyon0zi")
    @Column(name = "manager_chef_housing_subsidy", precision = 10, scale = 2)
    private BigDecimal managerChefHousingSubsidy;

    /** 合计 */
    @FormField("numberField_mbkldun8")
    @Column(name = "total", precision = 10, scale = 2)
    private BigDecimal total;

    /** 宿舍租赁床位成本 */
    @FormField("numberField_mbkldun7")
    @Column(name = "dormitory_bed_cost", precision = 10, scale = 2)
    private BigDecimal dormitoryBedCost;

    /** 住房补贴标准 */
    @FormField("numberField_mbkldun6")
    @Column(name = "housing_subsidy_standard", precision = 10, scale = 2)
    private BigDecimal housingSubsidyStandard;

    /** 商圈等级 */
    @FormField("selectField_mbkldun5")
    @Column(name = "business_district_level")
    private String businessDistrictLevel;

    /** 品牌 */
    @FormField("selectField_mbkldun4")
    @Column(name = "brand")
    private String brand;
}
