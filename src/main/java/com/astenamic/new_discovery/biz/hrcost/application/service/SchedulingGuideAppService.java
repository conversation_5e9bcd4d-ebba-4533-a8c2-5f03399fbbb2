package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SchedulingGuide;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SchedulingGuideRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SchedulingGuideAppService extends BaseAppService<SchedulingGuide> {

    public SchedulingGuideAppService(SchedulingGuideRepository schedulingGuideJpaAdapter,
                                     SchedulingGuideRepository schedulingGuideYidaAdapter) {
        super(schedulingGuideJpaAdapter, schedulingGuideYidaAdapter);
    }
}
