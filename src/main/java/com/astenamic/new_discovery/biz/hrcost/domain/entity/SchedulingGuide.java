package com.astenamic.new_discovery.biz.hrcost.domain.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 排班指引建议人数表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FormEntity(
        value = "FORM-AAC4D0DD4623444DA26556B8545EE230O7VE",
        appType = "APP_JV4TMC5QSTTSQN72RJ3F",
        sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
@Entity(name = "xfx_scheduling_guide")
public class SchedulingGuide extends BaseEntity {

    /** 门店ID（数据库字段，宜搭表单中不存在） */
    @Column(name = "shop_id", length = 64)
    private String shopId;

    /** 门店名称 */
    @FormField("textField_mcy6smy1")
    @Column(name = "store_name", length = 255)
    private String storeName;

    /** 日期 */
    @FormField("dateField_mcy6smy2")
    @Column(name = "date")
    private LocalDateTime date;

    /** 排班指引建议人数_午市前厅 */
    @FormField("numberField_mcy6smy3")
    @Column(name = "lunch_front_staff_count", precision = 10, scale = 2)
    private BigDecimal lunchFrontStaffCount;

    /** 排班指引建议人数_午市厨房 */
    @FormField("numberField_mcy6smy4")
    @Column(name = "lunch_kitchen_staff_count", precision = 10, scale = 2)
    private BigDecimal lunchKitchenStaffCount;

    /** 排班指引建议人数_晚市前厅 */
    @FormField("numberField_mcy6smy5")
    @Column(name = "dinner_front_staff_count", precision = 10, scale = 2)
    private BigDecimal dinnerFrontStaffCount;

    /** 排班指引建议人数_晚市厨房 */
    @FormField("numberField_mcy6smy6")
    @Column(name = "dinner_kitchen_staff_count", precision = 10, scale = 2)
    private BigDecimal dinnerKitchenStaffCount;

    /** 排班指引建议人数_夜宵前厅 */
    @FormField("numberField_md4arwym")
    @Column(name = "supper_front_staff_count", precision = 10, scale = 2)
    private BigDecimal supperFrontStaffCount;

    /** 排班指引建议人数_夜宵厨房 */
    @FormField("numberField_md4arwyn")
    @Column(name = "supper_kitchen_staff_count", precision = 10, scale = 2)
    private BigDecimal supperKitchenStaffCount;
}
