package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.ValueAssessment;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.ValueAssessmentRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class ValueAssessmentYidaAdapter
        extends BaseYidaAdapter<ValueAssessment>
        implements ValueAssessmentRepository {

    public ValueAssessmentYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(ValueAssessment.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
