package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.TakeawayPackagingPerformance;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.TakeawayPackagingPerformanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TakeawayPackagingPerformanceAppService extends BaseAppService<TakeawayPackagingPerformance> {

    public TakeawayPackagingPerformanceAppService(TakeawayPackagingPerformanceRepository takeawayPackagingPerformanceJpaAdapter,
                                                  TakeawayPackagingPerformanceRepository takeawayPackagingPerformanceYidaAdapter) {
        super(takeawayPackagingPerformanceJpaAdapter, takeawayPackagingPerformanceYidaAdapter);
    }

}
