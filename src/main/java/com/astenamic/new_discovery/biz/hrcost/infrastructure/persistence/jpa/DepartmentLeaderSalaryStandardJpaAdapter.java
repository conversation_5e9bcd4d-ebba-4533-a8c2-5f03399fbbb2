package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.DepartmentLeaderSalaryStandard;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.DepartmentLeaderSalaryStandardRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.DepartmentLeaderSalaryStandardDao;
import org.springframework.stereotype.Repository;

@Repository
public class DepartmentLeaderSalaryStandardJpaAdapter
        extends BaseJpaRepositoryAdapter<DepartmentLeaderSalaryStandard, DepartmentLeaderSalaryStandardDao>
        implements DepartmentLeaderSalaryStandardRepository {

    public DepartmentLeaderSalaryStandardJpaAdapter(DepartmentLeaderSalaryStandardDao dao) {
        super(dao);
    }
}
