package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.OtherCostRatio;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 人力成本模型 - 其它费用占比 JPA 数据访问接口
 */
@Repository
public interface OtherCostRatioDao extends JpaRepository<OtherCostRatio, Long> {
    
    /**
     * 根据宜搭对象ID查找实体
     */
    OtherCostRatio findByObjectId(String objectId);
}
