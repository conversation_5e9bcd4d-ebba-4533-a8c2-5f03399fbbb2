package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.LegalHolidaySalary;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.LegalHolidaySalaryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LegalHolidaySalaryAppService extends BaseAppService<LegalHolidaySalary> {

    public LegalHolidaySalaryAppService(LegalHolidaySalaryRepository legalHolidaySalaryJpaAdapter,
                                        LegalHolidaySalaryRepository legalHolidaySalaryYidaAdapter) {
        super(legalHolidaySalaryJpaAdapter, legalHolidaySalaryYidaAdapter);
    }

}
