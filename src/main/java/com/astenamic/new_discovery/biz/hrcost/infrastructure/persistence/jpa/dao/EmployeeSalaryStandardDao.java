package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.EmployeeSalaryStandard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 员工工资标准表单 JPA 数据访问接口
 */
@Repository
public interface EmployeeSalaryStandardDao extends JpaRepository<EmployeeSalaryStandard, Long> {
    EmployeeSalaryStandard findByObjectId(String objectId);
}
