package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.yida;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SocialSecurityCoverage;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SocialSecurityCoverageRepository;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.stereotype.Repository;

@Repository
public class SocialSecurityCoverageYidaAdapter
        extends BaseYidaAdapter<SocialSecurityCoverage>
        implements SocialSecurityCoverageRepository {

    public SocialSecurityCoverageYidaAdapter(YiDaSession yiDaSession, YiDaSessionV2 yiDaSessionV2, YidaConfigProperties yidaConfigProperties) {
        super(SocialSecurityCoverage.class, yiDaSession, yiDaSessionV2, yidaConfigProperties);
    }
}
