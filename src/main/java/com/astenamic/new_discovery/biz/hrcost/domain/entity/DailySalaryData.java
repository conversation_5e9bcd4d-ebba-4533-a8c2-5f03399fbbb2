package com.astenamic.new_discovery.biz.hrcost.domain.entity;



import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 日薪数据实体
 * 人力成本模型 - 日薪数据管理
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "xfx_daily_salary_data")
@FormEntity(
    value = "FORM-CFB9683494F3410AB2E9B4198A29F9B8OBG9",
    appType = "APP_JV4TMC5QSTTSQN72RJ3F",
    sysToken = "65D66X71IJ0WY1F66C7BC7XHV6XX2LQCXPHBMU81"
)
public class DailySalaryData extends BaseEntity {

    /**
     * 日薪合计
     */
    @FormField("numberField_mcbkfy2e")
    @Column(name = "daily_salary_total", precision = 10, scale = 2)
    private BigDecimal dailySalaryTotal;

    /**
     * 日期区间
     */
    @FormField("textField_mcbkfy2d")
    @Column(name = "date_range", length = 100)
    private String dateRange;

    /**
     * 门店名称
     */
    @FormField("textField_mcbkfy2c")
    @Column(name = "store_name", length = 200)
    private String storeName;
}
