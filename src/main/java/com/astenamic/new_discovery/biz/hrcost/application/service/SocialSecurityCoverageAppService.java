package com.astenamic.new_discovery.biz.hrcost.application.service;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SocialSecurityCoverage;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.SocialSecurityCoverageRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SocialSecurityCoverageAppService extends BaseAppService<SocialSecurityCoverage> {

    public SocialSecurityCoverageAppService(SocialSecurityCoverageRepository socialSecurityCoverageJpaAdapter,
                                            SocialSecurityCoverageRepository socialSecurityCoverageYidaAdapter) {
        super(socialSecurityCoverageJpaAdapter, socialSecurityCoverageYidaAdapter);
    }

}
