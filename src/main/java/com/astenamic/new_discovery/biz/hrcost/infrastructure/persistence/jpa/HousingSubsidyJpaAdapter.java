package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.HousingSubsidy;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.HousingSubsidyRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.HousingSubsidyDao;
import org.springframework.stereotype.Repository;

@Repository
public class HousingSubsidyJpaAdapter
        extends BaseJpaRepositoryAdapter<HousingSubsidy, HousingSubsidyDao>
        implements HousingSubsidyRepository {

    public HousingSubsidyJpaAdapter(HousingSubsidyDao dao) {
        super(dao);
    }
}
