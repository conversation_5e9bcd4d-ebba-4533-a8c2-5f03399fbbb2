package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.BaseEntity;
import org.springframework.data.annotation.Transient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public abstract class BaseJpaRepositoryAdapter<T extends BaseEntity, D extends JpaRepository<T, ?>> {

    protected final D dao;

    protected BaseJpaRepositoryAdapter(D dao) {
        this.dao = dao;
    }

    @Transactional
    public String save(T entity) {
        try {
            T save = dao.save(entity);
            return save.getId().toString();
        } catch (Exception e) {
            throw new RuntimeException("保存实体失败：" + e.getMessage(), e);
        }
    }

    @Transactional
    public List<String> saveAll(Iterable<T> entities) {
        try {
            List<T> saved = dao.saveAll(entities);
            return saved.stream()
                    .map(e -> e.getId().toString())
                    .toList();
        } catch (Exception e) {
            throw new RuntimeException("批量保存实体失败：" + e.getMessage(), e);
        }
    }

    @Transactional
    public List<T> findAll() {
        try {
            return dao.findAll();
        } catch (Exception e) {
            throw new RuntimeException("查询所有实体失败：" + e.getMessage(), e);
        }
    }

    @Transactional
    public T findByObjectId(String objectId) {
        try {
            return (T) dao.getClass().getMethod("findByObjectId", String.class).invoke(dao, objectId);
        } catch (Exception e) {
            throw new RuntimeException("通过objectId查询实体失败：" + e.getMessage(), e);
        }
    }


    @Transactional
    public String deleteByObjectId(String objectId) {
        T entity = findByObjectId(objectId);
        if (entity == null) {
            return "";
        }
        entity.setDelFlag("1");
        T save = dao.save(entity);
        return save.getId() == null ? "" : save.getId().toString();
    }

    @Transactional
    public List<String> deleteAllByObjectIds(List<String> objectIds) {
        return objectIds.stream()
                .map(this::deleteByObjectId)
                .filter(id -> id != null && !id.isEmpty())
                .toList();
    }


}
