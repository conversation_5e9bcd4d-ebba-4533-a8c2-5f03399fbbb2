package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.TakeawayPackagingPerformance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 外卖打包绩效表单 JPA 数据访问接口
 */
@Repository
public interface TakeawayPackagingPerformanceDao extends JpaRepository<TakeawayPackagingPerformance, Long> {
    TakeawayPackagingPerformance findByObjectId(String objectId);
}
