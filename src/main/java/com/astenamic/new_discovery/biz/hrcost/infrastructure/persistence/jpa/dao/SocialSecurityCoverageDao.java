package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.SocialSecurityCoverage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 企业社保及参保率表单 JPA 数据访问接口
 */
@Repository
public interface SocialSecurityCoverageDao extends JpaRepository<SocialSecurityCoverage, Long> {
    SocialSecurityCoverage findByObjectId(String objectId);
}
