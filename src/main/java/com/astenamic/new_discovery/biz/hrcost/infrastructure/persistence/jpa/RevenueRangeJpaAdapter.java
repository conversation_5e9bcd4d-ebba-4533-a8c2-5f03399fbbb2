package com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa;

import com.astenamic.new_discovery.biz.hrcost.domain.entity.RevenueRange;
import com.astenamic.new_discovery.biz.hrcost.domain.repository.RevenueRangeRepository;
import com.astenamic.new_discovery.biz.hrcost.infrastructure.persistence.jpa.dao.RevenueRangeDao;
import org.springframework.stereotype.Repository;

@Repository
public class RevenueRangeJpaAdapter
        extends BaseJpaRepositoryAdapter<RevenueRange, RevenueRangeDao>
        implements RevenueRangeRepository {

    public RevenueRangeJpaAdapter(RevenueRangeDao dao) {
        super(dao);
    }
}
