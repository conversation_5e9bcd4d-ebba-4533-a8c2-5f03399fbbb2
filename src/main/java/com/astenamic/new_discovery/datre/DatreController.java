package com.astenamic.new_discovery.datre;


import com.astenamic.new_discovery.datre.report.food.entity.FoodReport;
import com.astenamic.new_discovery.datre.report.food.service.FoodReportService;
import com.astenamic.new_discovery.datre.report.shop.entity.ShopReport;
import com.astenamic.new_discovery.datre.report.shop.service.ShopReportService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/datre")
@AllArgsConstructor
public class DatreController {
    private ShopReportService shopReportService;
    private FoodReportService foodReportService;

    @GetMapping("/shop")
    public List<ShopReport> shop() {
        LocalDateTime startDate = LocalDateTime.of(2024, 11, 20, 23, 59, 59);
        LocalDateTime endDate = LocalDateTime.of(2024, 11, 21, 0, 0, 0);
        return this.shopReportService.getShopDailyReport("1010",startDate, endDate);
    }

    @GetMapping("/food")
    public List<FoodReport> food() {
        LocalDateTime startDate = LocalDateTime.of(2024, 11, 10, 0, 0, 0);
        return this.foodReportService.getFoodDailyReport(startDate);
    }

}
