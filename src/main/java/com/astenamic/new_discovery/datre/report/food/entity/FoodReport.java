package com.astenamic.new_discovery.datre.report.food.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FoodReport {

    private String brandName;
    private String foodCode;
    private String foodCookId;
    private String foodCookName;
    private String foodId;
    private String foodName;
    private String foodStyleName;
    private String foodUnitId;
    private Integer liushui;
    private Float llCost;
    private Float llCostRate;
    private String regionName;
    private String shopId;
    private String shopName;
    private String shopNo;
    private Float sjCost;
    private Float sjCostRate;
    private String yearMonth;
}
