package com.astenamic.new_discovery.datre.report.food.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.datre.api.session.DatreSession;
import com.astenamic.new_discovery.datre.report.food.entity.FoodReport;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@AllArgsConstructor
public class FoodReportService extends DatreSession {
    private final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM");;
    private final String resource = "/dolinkdata/api/foodMonthData";

    public List<FoodReport> getFoodDailyReport(LocalDateTime yearMonth) {
        JSONObject params = JSONObject.of("yearMonth", dtf.format(yearMonth));
        return this.postListRequest(resource, params, FoodReport.class);
    }
}
