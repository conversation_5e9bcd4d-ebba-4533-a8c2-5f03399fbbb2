package com.astenamic.new_discovery.datre.report.material.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.biz.report.material.entity.MaterialShopReport;
import com.astenamic.new_discovery.biz.report.shop.entity.ShopDailyReport;
import com.astenamic.new_discovery.datre.api.session.DatreSession;
import com.astenamic.new_discovery.datre.report.material.entity.MaterialReport;
import com.astenamic.new_discovery.yida.http.SearchCondition;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class MaterialReportService extends DatreSession {
    private final YiDaSession yiDaSession;
    private final DateTimeFormatter dtf;
    private final String resource = "/dolinkdata/api/materialDayData";

    public MaterialReportService(YiDaSession yiDaSession,
                                 @Qualifier("DateTimeYMDHMSFormatter") DateTimeFormatter dtf) {
        this.yiDaSession = yiDaSession;
        this.dtf = dtf;
    }

    public List<MaterialShopReport> findYiDaDailyData(LocalDateTime day){

        day = this.normalizeTime(day);

        // 转换为时间戳（毫秒）
        long timestampMillis = day.toInstant(ZoneOffset.UTC).toEpochMilli();

        int page = 1;
        int len = 0;
        long[] dates = {timestampMillis, timestampMillis};

        // 根据日期筛选
        SearchConditions conditions = SearchCondition.builder().dateBetween("dateField_m4qf9iqh", dates, "+").get();

        List<MaterialShopReport> all = new ArrayList<>();
        do {

            List<MaterialShopReport> has = this.yiDaSession.searchFormDataConditionsRequest(MaterialShopReport.class, conditions, page);

            all.addAll(has);

            len = has.size();

            page++;

        } while (len == 100);

        return all;
    }

    public List<MaterialReport> getMaterialDailyReport(LocalDateTime day, String shopNo) {

        LocalDateTime finalDay = this.normalizeTime(day);

        JSONObject params = JSONObject.of(
                "shopNo", shopNo,
                "startDate", dtf.format(day.minusDays(1)),
                "endDate", dtf.format(day));
        return this.postListRequest(resource, params, MaterialReport.class);
    }

    private LocalDateTime normalizeTime(LocalDateTime day) {
        return day.withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
    }
}
