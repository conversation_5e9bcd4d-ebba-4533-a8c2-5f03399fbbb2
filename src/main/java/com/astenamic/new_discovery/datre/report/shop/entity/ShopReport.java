package com.astenamic.new_discovery.datre.report.shop.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class ShopReport {
    // 门店中台ID
    private String shopId;
    // 门店名称
    private String shopName;
    // 门店编号
    private String shopNo;
    // 报表日期
    private LocalDateTime baseDate;
    // 堂食流水(含自提)
    private Float posField1012;
    // 总营收
    private Float posField16;
    // 营收目标
    private Float posField16Budget;
    // 总营收日环比
    private Float posField16Dod;
    // 目标达成率
    private Float posField16Rate;
    // 总营收同比
    private Float posField16Yoy;
    // 堂食营收
    private Float posField17;
    // 外卖流水
    private Float posField11;
    // 外卖营收
    private Float posField18;
    // 外卖收入占比
    private Float posField1816;
    // 单均同比
    private Float posField184Yoy;
    // 外卖目标
    private Float posField18Budget;
    // 外卖目标达成率（带出外卖相关字段）
    private Float posField18Rate;
    // 桌均数同比
    private Float posField18Yoy;
    // 折扣率
    private Float posField21Rate;
    // 开桌数
    private Float posField30;
    // 退菜率
    private Float posField300;
    // 开桌数同比
    private Float posField30Yoy;
    // 翻台率
    private Float posField30rate;
    // 堂食实收（含自提）
    private Float posField44;
    // 桌均数同比
    private Float posField4430Yoy;
    // 堂食实收（含自提）预算
    private Float posField44Budget;
    // 堂食目标达成率（带出堂食相关字段）
    private Float posField44Rate;
    // 堂食收入同比
    private Float posField44Yoy;
    // 来客数
    private Float posField7;
    // 来客数同比
    private Float posField7Yoy;
    // 总流水
    private Float posField9;
    // 出餐上报率
    private Float wmField1;
    // 曝光人数日环比
    private Float wmField2Dod;
    // 曝光人数同比
    private Float wmField2Yoy;
    // 进店转化率
    private Float wmField3;
    // 下单转化率
    private Float wmField4;
    // 新客占比
    private Float wmField5;
    // 店铺星级评分
    private Float wmField6;
    // 出餐超时率（不需要带出基础数据）
    private Float wmField7;
    // 外卖营业时长
    private Float wmField8;

}
