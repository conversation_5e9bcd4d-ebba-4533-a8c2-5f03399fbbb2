package com.astenamic.new_discovery.datre.report.material.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@FormEntity("tableField_m3xv5p86")
public class MaterialReport {

    // 日期
    private LocalDateTime baseDate;
    // 物料ID
    @FormField("textField_m3xv5p89")
    private String materialId;
    // 物料名称
    @FormField("textField_m3xv5p8b")
    private String materialName;
    // 	物料No
    @FormField("textField_m3xv5p8a")
    private String materialNo;
    // 物料规格
    @FormField("textField_m3xv5p8c")
    private String materialDesc;
    // 	物料大类
    @FormField("textField_m3xv5p8d")
    private String materialTypeBig;
    // 物料小类
    @FormField("textField_m3xv5p8e")
    private String materialTypeSmall;
    // 	门店ID
    private String shopId;
    // 	门店名称
    private String shopName;
    // 	门店编码
    private String shopNo;
    // 	实际成本
    @FormField("numberField_m3xv5p8g")
    private Float sjCost;

    // 成本损耗率
    @FormField("numberField_m3xv5p8i")
    private Float costRate(){
        // 差异成本
        Float dc = this.diffCost();

        // 当理论成本=0 且 差异成本<0 则损耗率-100%
        if(this.llCost == 0){

            if(dc < 0){
                return -1f;
            }

            if(dc > 0){
                return 1f;

            }
        }

        return (dc/this.llCost) * 100;
    };
    // 理论成本
    @FormField("numberField_m3xv5p8h")
    private Float llCost;
    // 	单位
    @FormField("textField_m3xv5p8f")
    private String unit;
    // 差异成本
    @FormField("numberField_m499j963")
    public Float diffCost(){
        return this.sjCost - this.llCost;
    }
    // 期初库存数量
    @FormField("numberField_m64t5zzi")
    private String kcQcQty;
    // 期末库存数量
    @FormField("numberField_m64t5zzj")
    private String kcQmQty;
    // 实际用量
    @FormField("numberField_m64t5zzm")
    private Float sjQty;
    // 理论用量
    @FormField("numberField_m64t5zzn")
    private Float llQty;
    // 采购入库数量
    @FormField("numberField_m64t5zzk")
    private Float cgrkQty;
    // 配送入库数量
    @FormField("numberField_m64t5zzl")
    private String psrkQty;
    // 差异用量
    @FormField("numberField_m64t5zzo")
    public Float cy_qty(){
        if(llQty == null){
            return 0f;
        }
        return this.sjQty - this.llQty;

    }
    // 调拨出库数量
    @FormField("numberField_m741l50w")
    private Float dbckQty;
    // 调拨出库成本
    @FormField("numberField_m741l50x")
    private Float dbckCost;
    // 报损出库数量
    @FormField("numberField_m741l50y")
    private Float bsckQty;
    // 报损出库成本
    @FormField("numberField_m741l50z")
    private Float bsckCost;
}
