package com.astenamic.new_discovery.datre.report.shop.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.datre.api.session.DatreSession;
import com.astenamic.new_discovery.datre.report.shop.entity.ShopReport;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ShopReportService extends DatreSession {
    private DateTimeFormatter dtf;
    private final String resource = "/dolinkdata/api/reportData";

    public ShopReportService(@Qualifier("DateTimeYMDHMSFormatter") DateTimeFormatter dtf) {
        this.dtf = dtf;
    }

    public List<ShopReport> getShopDailyReport(String shop, LocalDateTime startDate, LocalDateTime endDate){

        JSONObject params = JSONObject.of(
                    "shopNo",shop,
                "startDate", dtf.format(startDate),
                "endDate", dtf.format(endDate));

        return this.postListRequest(resource, params, ShopReport.class);

    }
}
