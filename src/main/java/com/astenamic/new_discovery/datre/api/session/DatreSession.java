package com.astenamic.new_discovery.datre.api.session;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.datre.api.config.DatreConfiguration;
import com.astenamic.new_discovery.datre.api.http.response.DatreArrayDataBody;
import com.astenamic.new_discovery.util.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

public class DatreSession {

    @Autowired
    private DatreConfiguration config;
    @Autowired
    private RestTemplate restTemplate;

    protected <T> List<T> postListRequest(String resource, JSONObject params, Class<T> tClass) {

        params = this.encryptParams(params);

        String url = this.getUrl(resource);

        DatreArrayDataBody response = this.restTemplate.postForObject(url, params, DatreArrayDataBody.class);

        if (response == null) {
            throw new IllegalStateException("请求失败，响应体为空");
        }

        String status = response.getStatus();

        if (StringUtils.isBlank(status) || !status.equals("success")) {
            throw new IllegalStateException("请求失败，错误信息：" + response.getMessage());
        }

        JSONArray dataArr = response.getData();

        if (dataArr == null) {
            return null;
        }

        return dataArr.toJavaList(tClass);
    }


    private JSONObject encryptParams(JSONObject params) {

        JSONObject commonParams = new JSONObject();

        commonParams.put("secretKey", this.config.getAppKey());
//        commonParams.put("shopNo", "9323219540978470977");

        commonParams.putAll(params);

        long timestamp = TimeUtils.toEpochMilli(LocalDateTime.now());

        commonParams.put("timestamp", timestamp);

        StringBuilder stringBuilder = new StringBuilder();
        commonParams.forEach((k, v) -> {
            stringBuilder.append(v.toString());
        });

        String encryptStr = stringBuilder.toString();

//        System.out.println(encryptStr);

        String md5 = md5(encryptStr);

        commonParams.put("md5", md5);

        return commonParams;
    }

    public static String md5(String input) {

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    protected String getUrl(String uri) {
        return this.config.getBaseUrl() + uri;
    }

}
