package com.astenamic.new_discovery;

import com.astenamic.new_discovery.dingtalk.config.DingTalkConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@EnableJpaAuditing
public class NewDiscoveryApplication {

    public static void main(String[] args) {
        SpringApplication.run(NewDiscoveryApplication.class, args);
        System.out.println("                            __      __\n" +
                "                           ( _\\    /_ )\n" +
                "                            \\ _\\  /_ / \n" +
                "                             \\ _\\/_ /_ _\n" +
                "                             |_____/_/ /|\n" +
                "                             (  (_)__)J-)\n" +
                "                             (  /`.,   /\n" +
                "                              \\/  ;   /\n"
        );
    }

}
