package com.astenamic.new_discovery.util;

import java.util.concurrent.*;

/**
 * 线程池工具类
 * 根据2核CPU环境优化配置，新增单线程顺序执行线程池
 */
public class ThreadPoolUtils {

    /** CPU 核心数 */
    private static final int CPU_COUNT = 2;

    /** 核心线程数（CPU密集型任务） */
    private static final int CORE_POOL_SIZE_CPU = CPU_COUNT;
    /** 最大线程数（CPU密集型任务） */
    private static final int MAX_POOL_SIZE_CPU = CPU_COUNT + 1;

    /** 核心线程数（IO密集型任务） */
    private static final int CORE_POOL_SIZE_IO = CPU_COUNT * 2;
    /** 最大线程数（IO密集型任务） */
    private static final int MAX_POOL_SIZE_IO = CPU_COUNT * 4;

    /** 单线程执行任务 */
    private static final int SINGLE_CORE_POOL = 1;
    private static final int SINGLE_MAX_POOL  = 1;

    /** 线程空闲时间（秒） */
    private static final int KEEP_ALIVE_TIME = 60;
    /** 任务队列容量 */
    private static final int QUEUE_CAPACITY = 200;

    /** CPU密集型线程池 */
    private static ThreadPoolExecutor cpuThreadPool;
    /** IO密集型线程池 */
    private static ThreadPoolExecutor ioThreadPool;
    /** 单线程顺序执行线程池 */
    private static ThreadPoolExecutor singleThreadPool;

    /** 获取CPU密集型任务线程池 */
    public static ThreadPoolExecutor getCpuThreadPool() {
        if (cpuThreadPool == null) {
            synchronized (ThreadPoolUtils.class) {
                if (cpuThreadPool == null) {
                    cpuThreadPool = new ThreadPoolExecutor(
                            CORE_POOL_SIZE_CPU, MAX_POOL_SIZE_CPU,
                            KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                            r -> {
                                Thread t = new Thread(r);
                                t.setName("cpu-task-" + t.getId());
                                return t;
                            },
                            new ThreadPoolExecutor.CallerRunsPolicy());
                }
            }
        }
        return cpuThreadPool;
    }

    /** 获取IO密集型任务线程池 */
    public static ThreadPoolExecutor getIoThreadPool() {
        if (ioThreadPool == null) {
            synchronized (ThreadPoolUtils.class) {
                if (ioThreadPool == null) {
                    ioThreadPool = new ThreadPoolExecutor(
                            CORE_POOL_SIZE_IO, MAX_POOL_SIZE_IO,
                            KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                            r -> {
                                Thread t = new Thread(r);
                                t.setName("io-task-" + t.getId());
                                return t;
                            },
                            new ThreadPoolExecutor.CallerRunsPolicy());
                }
            }
        }
        return ioThreadPool;
    }

    /** 获取单线程顺序执行线程池 */
    public static ThreadPoolExecutor getSingleThreadPool() {
        if (singleThreadPool == null) {
            synchronized (ThreadPoolUtils.class) {
                if (singleThreadPool == null) {
                    singleThreadPool = new ThreadPoolExecutor(
                            SINGLE_CORE_POOL, SINGLE_MAX_POOL,
                            KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(),
                            r -> {
                                Thread t = new Thread(r);
                                t.setName("single-task-" + t.getId());
                                return t;
                            },
                            new ThreadPoolExecutor.CallerRunsPolicy());
                }
            }
        }
        return singleThreadPool;
    }

    /**
     * 执行CPU密集型任务
     */
    public static void executeCpuTask(Runnable task) {
        getCpuThreadPool().execute(task);
    }

    /**
     * 执行IO密集型任务
     */
    public static void executeIoTask(Runnable task) {
        getIoThreadPool().execute(task);
    }

    /**
     * 执行单线程顺序任务
     */
    public static void executeSingleTask(Runnable task) {
        getSingleThreadPool().execute(task);
    }

    /**
     * 提交IO任务并获取结果
     */
    public static <T> Future<T> submitIo(Callable<T> task) {
        return getIoThreadPool().submit(task);
    }

    /**
     * 提交单线程任务并获取结果
     */
    public static <T> Future<T> submitSingle(Callable<T> task) {
        return getSingleThreadPool().submit(task);
    }

    /**
     * 关闭所有线程池
     */
    public static void shutdown() {
        if (cpuThreadPool != null) cpuThreadPool.shutdown();
        if (ioThreadPool  != null) ioThreadPool.shutdown();
        if (singleThreadPool != null) singleThreadPool.shutdown();
    }

    /**
     * 获取线程池状态信息
     */
    public static String getPoolStatus() {
        StringBuilder sb = new StringBuilder();
        if (cpuThreadPool != null) {
            sb.append("CPU线程池: 活跃线程=").append(cpuThreadPool.getActiveCount())
                    .append(", 已完成任务=").append(cpuThreadPool.getCompletedTaskCount())
                    .append(", 队列大小=").append(cpuThreadPool.getQueue().size()).append("\n");
        }
        if (ioThreadPool != null) {
            sb.append("IO线程池: 活跃线程=").append(ioThreadPool.getActiveCount())
                    .append(", 已完成任务=").append(ioThreadPool.getCompletedTaskCount())
                    .append(", 队列大小=").append(ioThreadPool.getQueue().size()).append("\n");
        }
        if (singleThreadPool != null) {
            sb.append("单线程池: 活跃线程=").append(singleThreadPool.getActiveCount())
                    .append(", 已完成任务=").append(singleThreadPool.getCompletedTaskCount())
                    .append(", 队列大小=").append(singleThreadPool.getQueue().size());
        }
        return sb.toString();
    }
}
