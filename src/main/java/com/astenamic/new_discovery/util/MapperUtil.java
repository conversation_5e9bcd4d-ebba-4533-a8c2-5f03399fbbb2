package com.astenamic.new_discovery.util;

import org.mapstruct.Named;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * MapStruct 通用类型转换工具类
 */
public class MapperUtil {

    @Named("stringToFloat")
    public static Float stringToFloat(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        String cleaned = value.replace("%", "").trim();
        try {
            return Float.parseFloat(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Named("stringToDouble")
    public static Double stringToDouble(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        String cleaned = value.replace("%", "").trim();
        try {
            return Double.parseDouble(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Named("integerToDouble")
    public static Double integerToDouble(Integer value) {
        return value != null ? value.doubleValue() : null;
    }

    @Named("doubleToInteger")
    public static Integer doubleToInteger(Double value) {
        return value != null ? value.intValue() : null;
    }

    @Named("doubleToBigDecimal")
    public static BigDecimal doubleToBigDecimal(Double value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    @Named("integerToBigDecimal")
    public static BigDecimal integerToBigDecimal(Integer value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    @Named("floatToBigDecimal")
    public static BigDecimal floatToBigDecimal(Float value) {
        return value != null ? BigDecimal.valueOf(value) : null;
    }

    @Named("stringToBigDecimal")
    public static BigDecimal stringToBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        String cleaned = value.replace("%", "").trim();
        try {
            return new BigDecimal(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Named("stringToInteger")
    public static Integer stringToInteger(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        String cleaned = value.trim();
        try {
            return Integer.parseInt(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Named("bigDecimalToFloat")
    public static Float bigDecimalToFloat(BigDecimal value) {
        return value == null ? null : value.floatValue();
    }

    @Named("bigDecimalToDouble")
    public static Double bigDecimalToDouble(BigDecimal value) {
        return value == null ? null : value.doubleValue();
    }

    @Named("floatToString")
    public static String floatToString(Float value) {
        return value == null ? null : value.toString();
    }

    @Named("bigDecimalToString")
    public static String bigDecimalToString(BigDecimal value) {
        return value == null ? null : value.toPlainString();
    }

    @Named("localDateToLocalDateTime")
    public static LocalDateTime localDateToLocalDateTime(LocalDate date) {
        return date == null ? null : date.atStartOfDay();
    }

    @Named("localDateTimeToLocalDate")
    public static LocalDate localDateTimeToLocalDate(LocalDateTime dateTime) {
        return dateTime == null ? null : dateTime.toLocalDate();
    }

    @Named("stringToLocalDateTime")
    public static LocalDateTime stringToLocalDateTime(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        try {
            return LocalDateTime.parse(value, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } catch (Exception e) {
            return null;
        }
    }

    @Named("stringToLocalDate")
    public static LocalDate stringToLocalDate(String value) {
        if (value == null || value.trim().isEmpty()) return null;
        try {
            return LocalDate.parse(value, DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (Exception e) {
            return null;
        }
    }

}