package com.astenamic.new_discovery.external.config;


import com.astenamic.new_discovery.external.client.takeout.factory.MtSystemParamFactory;
import com.astenamic.new_discovery.external.modal.dto.takeout.MtSystemParam;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.stream.Collectors;

@Data
@Configuration
@ConfigurationProperties(prefix = "mt")
public class MtConfiguration {


    private Map<String, MtConfig> configs;

    @Data
    public static class MtConfig {

        private String appId;

        private String appSecret;
    }

    public MtConfig getMtConfig(String brandName) {
        if (configs == null || configs.isEmpty() || !configs.containsKey(brandName)) {
            throw new IllegalArgumentException("未找到美团配置: " + brandName);
        }
        return configs.get(brandName);
    }

    @Bean
    public MtSystemParamFactory mtSystemParamFactory() {
        Map<String, MtSystemParam> systemParamMap = configs.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    MtConfig config = entry.getValue();
                    return new MtSystemParam(config.getAppId(), config.getAppSecret());
                }));
        return new MtSystemParamFactory(systemParamMap);
    }


}
