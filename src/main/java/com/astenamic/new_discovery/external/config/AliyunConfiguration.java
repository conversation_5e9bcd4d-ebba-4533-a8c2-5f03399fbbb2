package com.astenamic.new_discovery.external.config;


import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.quickbi_public20220101.AsyncClient;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun")
public class AliyunConfiguration {

    private QuickBi quickBi;


    @Bean
    public StaticCredentialProvider createStaticCredentialProvider() {
        return StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(this.quickBi.getAccessKeyId())
                .accessKeySecret(this.quickBi.getAccessKeySecret())
                .build());
    }

    @Bean
    public AsyncClient createAsyncClient(StaticCredentialProvider provider) {
        return AsyncClient.builder()
                .region(this.quickBi.region)
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                .setEndpointOverride(this.quickBi.endpoint)
                                .setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();
    }

    @Data
    public static class AliyunConfig {

        private String accessKeyId;

        private String accessKeySecret;

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class QuickBi extends AliyunConfig {

        private String baseUrl;

        private String region;

        private String endpoint;

    }


}
