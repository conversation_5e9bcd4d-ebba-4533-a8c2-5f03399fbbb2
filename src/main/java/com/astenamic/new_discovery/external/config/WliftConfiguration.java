package com.astenamic.new_discovery.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "wlift")
public class WliftConfiguration {

    private String v = "2.0";

    private String baseUrl;

    private Map<String, WliftConfig> configs;

    private String fmt = "JSON";

    @Data
    public static class WliftConfig {
        private String appId;
        private String appKey;
    }
}
