package com.astenamic.new_discovery.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "cool")
public class CoolConfiguration {

    private String enterpriseId;

    private Vision vision;

    @Data
    public static class Vision {

        private String baseUrl;

        private String privateKey;

    }
}
