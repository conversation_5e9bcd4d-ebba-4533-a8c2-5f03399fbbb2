package com.astenamic.new_discovery.external.config;


import com.astenamic.new_discovery.external.client.takeout.factory.ElemeClientFactory;
import eleme.openapi.sdk.config.Config;
import eleme.openapi.sdk.oauth.OAuthClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "elm")
public class ElemeConfiguration {

    private Map<String, ElmConfig> configs;

    @Data
    public static class ElmConfig {
        private boolean sandbox;
        private String appKey;
        private String appSecret;
    }

    /**
     * 根据配置名称获取饿了么配置
     *
     * @param configName 配置名称
     * @return 饿了么配置
     */
    public Config getConfig(String configName) {
        if (configs == null || configs.isEmpty() || !configs.containsKey(configName)) {
            throw new IllegalArgumentException("未找到饿了么配置: " + configName);
        }
        ElmConfig elmConfig = configs.get(configName);
        return new Config(elmConfig.isSandbox(), elmConfig.getAppKey(), elmConfig.getAppSecret());
    }

    /**
     * 获取所有配置的Config对象
     *
     * @return 所有配置的Config对象Map
     */
    public Map<String, Config> getAllConfigs() {
        if (configs == null || configs.isEmpty()) {
            throw new IllegalArgumentException("未找到饿了么配置");
        }

        Map<String, Config> result = new HashMap<>();
        configs.forEach((key, value) ->
            result.put(key, new Config(value.isSandbox(), value.getAppKey(), value.getAppSecret()))
        );
        return result;
    }

    /**
     * 创建OAuthClient工厂，根据品牌名称获取对应的OAuthClient
     */
    @Bean
    public ElemeClientFactory elmClientFactory() {
        Map<String, OAuthClient> clientMap = new HashMap<>();
        getAllConfigs().forEach((key, config) ->
            clientMap.put(key, new OAuthClient(config))
        );
        return new ElemeClientFactory(clientMap);
    }

}
