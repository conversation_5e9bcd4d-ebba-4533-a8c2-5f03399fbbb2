package com.astenamic.new_discovery.external.modal.dto.cool.vision.enums;

/**
 * 门店状态枚举
 */
public enum StoreStatusEnum {

    /**
     * 营业
     */
    OPEN("open", "营业"),

    /**
     * 闭店
     */
    CLOSED("closed", "闭店"),

    /**
     * 未开业
     */
    NOT_OPEN("not_open", "未开业");

    private final String code;
    private final String description;

    StoreStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static StoreStatusEnum fromCode(String code) {
        for (StoreStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
