package com.astenamic.new_discovery.external.modal.dto.cool.vision;

import lombok.Data;

/**
 * 区域信息 DTO
 */
@Data
public class RegionDTO {

    /**
     * 区域id
     */
    private Long id;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 上级节点id
     */
    private Long parentId;

    /**
     * 第三方关联id
     */
    private String synDingDeptId;

    /**
     * 区域类型
     * root 根目录
     * path 区域
     * store 门店
     */
    private String regionType;

    /**
     * 区域路径
     */
    private String regionPath;

    /**
     * 门店id
     */
    private String storeId;
}
