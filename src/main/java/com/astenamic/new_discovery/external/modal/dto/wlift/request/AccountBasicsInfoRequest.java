package com.astenamic.new_discovery.external.modal.dto.wlift.request;


import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.wlift.AccountBasicsInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.wlift.response.AccountBasicsInfoResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AccountBasicsInfoRequest extends WliftBaseRequest {

    private String cno;

    @JsonIgnore
    @Override
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{AccountBasicsInfoResponse.class, AccountBasicsInfoDTO.class};
    }
}
