package com.astenamic.new_discovery.external.modal.dto.takeout;

import com.alibaba.fastjson2.JSON;
import eleme.openapi.sdk.api.entity.ugc.OOrderRateInfo;
import eleme.openapi.sdk.api.entity.ugc.ORateInfo;
import eleme.openapi.sdk.api.entity.ugc.OpenapiOrderRate;
import eleme.openapi.sdk.api.entity.ugc.OpenapiRateReply;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Data
public class CommentDTO {
    /**
     * 评论id，和平台对应
     */
    private String commentId;

    /**
     * 门店id
     */
    private String shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 外卖平台
     */
    private String platform;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论时间
     */
    private LocalDateTime ratedAt;

    /**
     * 追评
     */
    private String addContent;

    /**
     * 追评时间
     */
    private LocalDateTime addRatedAt;

    /**
     * 是否回复 1:已回复 0:未回复
     */
    private String isReplied;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复时间
     */
    private String replyTime;

    // 商家评分(星级评分)
    private Integer shopScore;

    // 口味评分
    private Integer orderCommentScore;

    // 包装评价分数
    private Integer packingScore;

    /**
     * 其他信息
     */
    private String jsonInfo;

    private String brand;


    public static CommentDTO createByMeituan(String brand, MtCommentDTO commentDTO) {
        if (commentDTO == null) {
            throw new IllegalArgumentException("commentDTO is null");
        }
        CommentDTO comment = new CommentDTO();
        comment.setBrand(brand);
        comment.setCommentId(commentDTO.getCommentId());
        comment.setPlatform("mt");

        if (commentDTO.getCommentTime() != null) {
            comment.setRatedAt(commentDTO.getCommentTime().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        comment.setContent(commentDTO.getCommentContent());

        comment.setShopScore(commentDTO.getFoodCommentScore());
        comment.setOrderCommentScore(commentDTO.getOrderCommentScore());
        comment.setPackingScore(commentDTO.getPackingScore());

        comment.setAddContent(commentDTO.getAddComment());

        if (commentDTO.getAddCommentTime() != null) {
            comment.setAddRatedAt(commentDTO.getAddCommentTime().atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        comment.setIsReplied(commentDTO.getReplyStatus());

        if (StringUtils.isNotEmpty(commentDTO.getEReplyContent()) && commentDTO.getEReplyTime() != null) {
            comment.setReplyContent(commentDTO.getEReplyContent());
            comment.setReplyTime(commentDTO.getEReplyTime().atZone(ZoneId.systemDefault()).toLocalDateTime().toString());
        }

        comment.setJsonInfo(JSON.toJSONString(commentDTO));
        return comment;
    }

    /**
     * 从ORateInfo构造CommentDTO
     *
     * @param oRateInfo ORateInfo对象
     * @return CommentDTO列表，因为一个ORateInfo可能包含多条评价
     */
    public static List<CommentDTO> createByElm(String brand, ORateInfo oRateInfo) {
        if (oRateInfo == null || oRateInfo.getOrderRateInfo() == null) {
            throw new IllegalArgumentException("oRateInfo or its orderRateInfo is null");
        }
        OOrderRateInfo orderRateInfo = oRateInfo.getOrderRateInfo().get(0);
        CommentDTO comment = new CommentDTO();
        comment.setBrand(brand);
        // 基本信息
        comment.setCommentId(orderRateInfo.getRateId());
        comment.setShopId(oRateInfo.getShopId());
        comment.setPlatform("eleme");

        // 评论内容和时间
        comment.setContent(orderRateInfo.getRatingContent());
        if (orderRateInfo.getRatingAt() != null) {
            comment.setRatedAt(convertDateToLocalDateTime(orderRateInfo.getRatingAt()));
        }

        // 评分信息
        comment.setShopScore(orderRateInfo.getServiceRating()); // 商家评分
        comment.setOrderCommentScore(orderRateInfo.getQualityRating()); // 口味评分
        comment.setPackingScore(orderRateInfo.getPackageRating()); // 包装评分

        comment.setIsReplied(orderRateInfo.getReplied() ? "1" : "0");
        // 回复信息
        if (orderRateInfo.getReplied() != null && orderRateInfo.getReplied()) {
            comment.setReplyContent(orderRateInfo.getReplyContent());
            if (orderRateInfo.getReplyAt() != null) {
                comment.setReplyTime(convertDateToLocalDateTime(orderRateInfo.getReplyAt()).toString());
            }
        }

        // 存储原始JSON信息
        comment.setJsonInfo(JSON.toJSONString(oRateInfo));
        return List.of(comment);
    }

    /**
     * 将Date转换为LocalDateTime
     */
    private static LocalDateTime convertDateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


}
