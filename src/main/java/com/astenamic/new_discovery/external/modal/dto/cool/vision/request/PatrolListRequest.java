package com.astenamic.new_discovery.external.modal.dto.cool.vision.request;


import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolRecordDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PatrolListRequest extends BaseRequest {

    /**
     * 企业id（必填）
     */
    private String enterpriseId;

    /**
     * 第几页（必填）
     */
    private Integer pageNum;

    /**
     * 分页大小，最大100（必填）
     */
    private Integer pageSize;

    /**
     * 区域id（可选）
     */
    private Long regionId;

    /**
     * 创建时间开始，13位时间戳，必填，范围限制为一天
     */
    private Long beginTime;

    /**
     * 创建时间结束，13位时间戳，必填
     */
    private Long endTime;

    /**
     * 巡店类型,PATROL_STORE_ONLINE：线上巡店，PATROL_STORE_OFFLINE：线下巡店，
     * PATROL_STORE_PICTURE_ONLINE：定时巡检，PATROL_STORE_AI：AI巡检，
     * STORE_SELF_CHECK：交叉巡店，PATROL_STORE_PLAN：计划巡店，PATROL_STORE_FORM：表单巡店
     */
    private String patrolType;

    /**
     * 状态,0:待处理 1：已完成 2:待审批 3：未开始
     */
    private Integer status;


    @Override
    @JsonIgnore
    public String getUri() {
        return "/open/patrol/list";
    }

    @Override
    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{CoolVisionPageDTO.class, PatrolRecordDTO.class};
    }
}
