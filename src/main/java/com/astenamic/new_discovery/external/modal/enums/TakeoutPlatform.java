package com.astenamic.new_discovery.external.modal.enums;


import lombok.Getter;

@Getter
public enum TakeoutPlatform implements BaseEnum {

    DEFAULT("默认", "default"),
    MT("美团", "mt"),
    ELEME("饿了么", "eleme"),
    JD("京东", "jdwm"),
    ;

    private final String name;

    private final String code;

    TakeoutPlatform(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public static TakeoutPlatform fromCode(String code) {
        for (TakeoutPlatform platform : TakeoutPlatform.values()) {
            if (platform.getCode().equals(code)) {
                return platform;
            }
        }
        return DEFAULT;
    }

    public static TakeoutPlatform fromName(String name) {
        for (TakeoutPlatform platform : TakeoutPlatform.values()) {
            if (platform.getName().equals(name)) {
                return platform;
            }
        }
        return DEFAULT;
    }
}
