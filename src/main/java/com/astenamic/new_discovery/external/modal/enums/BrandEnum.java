package com.astenamic.new_discovery.external.modal.enums;

import lombok.Getter;

@Getter
public enum BrandEnum implements BaseEnum {

    DEFAULT("默认", "default"),
    XFX("新发现", "xfx"),
    HDL("蝴蝶里", "hdl"),
    KJ("烤匠", "kj"),
    SJFQ("四季风情", "sjfq"),
    ;

    private final String name;

    private final String code;


    BrandEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public static BrandEnum fromValue(String value) {
        for (BrandEnum brand : BrandEnum.values()) {
            if (brand.getCode().equals(value)) {
                return brand;
            }
        }
        return BrandEnum.DEFAULT;
    }

    public static BrandEnum fromDesc(String desc) {
        for (BrandEnum brand : BrandEnum.values()) {
            if (brand.getName().equals(desc)) {
                return brand;
            }
        }
        return BrandEnum.DEFAULT;
    }

}
