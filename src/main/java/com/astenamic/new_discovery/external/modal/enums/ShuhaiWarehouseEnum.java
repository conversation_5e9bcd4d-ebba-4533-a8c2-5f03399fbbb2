package com.astenamic.new_discovery.external.modal.enums;


import lombok.Getter;

@Getter
public enum ShuhaiWarehouseEnum {

    HANGZHOU("杭州仓", "0W300003862", "HZQR"),
    NINGBO("宁波仓", "0W300003862", "NBFX"),
    NANJING("南京仓", "0W300003862", "NJQR"),
    WUXI("无锡仓", "0W300003862", "WXFX"),
    SHANGHAI("上海仓", "0W300003862", "SHFX");

    /**
     * 仓库名称
     */
    private final String name;
    /**
     * ent 编码
     */
    private final String entCode;
    /**
     * 客户仓库编码
     */
    private final String customerWarehouseCode;


    ShuhaiWarehouseEnum(String name, String entCode, String customerWarehouseCode) {
        this.name = name;
        this.entCode = entCode;
        this.customerWarehouseCode = customerWarehouseCode;
    }

    /**
     * 根据 entCode 查找枚举
     */
    public static ShuhaiWarehouseEnum fromEntCode(String entCode) {
        for (ShuhaiWarehouseEnum w : values()) {
            if (w.entCode.equals(entCode)) {
                return w;
            }
        }
        throw new IllegalArgumentException("未知 entCode: " + entCode);
    }

    /**
     * 根据 客户仓库编码 查找枚举
     */
    public static ShuhaiWarehouseEnum fromCustomerWarehouseCode(String code) {
        for (ShuhaiWarehouseEnum w : values()) {
            if (w.customerWarehouseCode.equals(code)) {
                return w;
            }
        }
        throw new IllegalArgumentException("未知 customerWarehouseCode: " + code);
    }
}
