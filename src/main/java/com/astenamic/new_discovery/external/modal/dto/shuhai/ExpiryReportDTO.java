package com.astenamic.new_discovery.external.modal.dto.shuhai;


import lombok.Data;

/**
 * 【获取效期报表】单条记录
 */
@Data
public class ExpiryReportDTO {

    /**
     * 温层，如“冷冻库”
     */
    private String temp;

    /**
     * 货位（文档示例为 null）
     */
    private String locationd;

    /**
     * 托运方（文档示例为 null）
     */
    private String shipperName;

    /**
     * 物料编码
     */
    private String materialNumber;

    /**
     * 物料名称
     */
    private String skuName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 库存数量
     */
    private Float stockQty;

    /**
     * 保质期
     */
    private String shelf;

    /**
     * 生产日期 yyyy-MM-dd
     */
    private String productDate;

    /**
     * 入库日期 yyyy-MM-dd
     */
    private String storageDate;

    /**
     * 过期日期 yyyy-MM-dd
     */
    private String expireDate;

    /**
     * 临期天数
     */
    private Integer adventDate;

    /**
     * 库存状态
     */
    private String stockStatus;

    /**
     * 备注（文档示例为 null）
     */
    private String cause;

    /**
     * 制表日期 yyyy-MM-dd
     */
    private String sendTime;

    /**
     * 客户物料编码
     */
    private String customerMaterialNumber;

    /**
     * 客户仓库编码
     */
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    private String customerWarehouse;
}
