package com.astenamic.new_discovery.external.modal.dto.takeout;


import eleme.openapi.sdk.api.entity.product.OItem;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductDTO {

    private String productId;

    private String productName;

    private int onShelf;

    private String platform;

    private LocalDateTime onShelfTime;

    public static ProductDTO from(MtProductDTO mtProductDTO) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(mtProductDTO.getApp_food_code());
        productDTO.setProductName(mtProductDTO.getName());
        productDTO.setOnShelf(mtProductDTO.getIs_sold_out());
        productDTO.setPlatform("mt");
        productDTO.setOnShelfTime(LocalDateTime.now());
        return productDTO;
    }

    public static ProductDTO from(OItem elmProductDTO) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(String.valueOf(elmProductDTO.getId()));
        productDTO.setProductName(elmProductDTO.getName());
        productDTO.setOnShelf(elmProductDTO.getOnShelf());
        productDTO.setPlatform("eleme");
        productDTO.setOnShelfTime(LocalDateTime.now());
        return productDTO;
    }

}
