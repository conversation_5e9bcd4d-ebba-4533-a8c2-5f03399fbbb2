package com.astenamic.new_discovery.external.modal.dto.cool.vision.response;

import lombok.Data;

/**
 * Cool Vision API 响应封装
 * 平台Response采用两层封装的方式，最外面一层是代表网关层，bizData里封装的数据代表具体的业务层
 *
 * @param <T> bizData.data 字段的类型
 */
@Data
public class CoolVisionResponse<T> {

    /**
     * 请求码(200网关层请求正常，500 网关层请求异常)
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 请求是否成功（true成功，false网关层失败）
     */
    private Boolean isSuccess;

    /**
     * 全局请求id
     */
    private String requestId;

    /**
     * 业务数据
     */
    private CoolVisionBizData<T> bizData;

    /**
     * 判断请求是否成功（网关层和业务层都成功）
     *
     * @return true 如果网关层和业务层都成功
     */
    public boolean isSuccess() {
        return Integer.valueOf(200).equals(code) &&
                bizData != null &&
                bizData.getCode() != null &&
                Integer.valueOf(200).equals(bizData.getCode());
    }
}
