package com.astenamic.new_discovery.external.modal.dto.cool.vision;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 巡店记录 DTO （已按接口文档校正字段类型）
 */
@Data
public class PatrolRecordDTO {

    /**
     * 巡店时长（实际）
     */
    private String actualPatrolStoreDuration;

    /**
     * 审核意见：reject/通过 pass/拒绝
     */
    private String auditOpinion;

    /**
     * 审核图片
     */
    private String auditPicture;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间（毫秒时间戳）
     */
    private Long auditTime;                // <- String → Long

    /**
     * 审核人id
     */
    private String auditUserId;

    /**
     * 审核人名称
     */
    private String auditUserName;

    /**
     * 巡店结果（优秀、良好、合格、不合格）
     */
    private String checkResult;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createTime;               // <- String → Long

    /**
     * 创建人名称
     */
    private String createrUserName;

    /**
     * 不合格检查项数
     */
    private Integer failColumnCount;

    /**
     * 巡店记录id
     */
    private Long recordId;

    /**
     * 不适用检查项数
     */
    private Integer inapplicableColumnCount;

    /**
     * 是否逾期完成
     */
    private String overdue;

    /**
     * 任务说明
     */
    private String taskDesc;               // <- 新增

    /**
     * 是否逾期完成（布尔型）
     */
    private Boolean isOverdue;

    /**
     * 检查表id
     */
    private Long metaTableId;

    /**
     * 检查表名称
     */
    private String metaTableName;

    /**
     * 合格检查表数量
     */
    private Integer passColumnCount;

    /**
     * 巡店类型
     */
    private String patrolType;

    /**
     * 得分率
     */
    private BigDecimal percent;

    /**
     * 区域id
     */
    private Long regionId;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 奖罚金额
     */
    private BigDecimal rewardPenaltMoney;      // num → BigDecimal

    /**
     * 得分
     */
    private BigDecimal score;                  // num → BigDecimal

    /**
     * 巡店结束地址
     */
    private String signEndAddress;

    /**
     * 签到状态：1正常，2异常
     */
    private Integer signInStatus;

    /**
     * 签退状态：1正常，2异常
     */
    private Integer signOutStatus;

    /**
     * 巡店开始地址
     */
    private String signStartAddress;

    /**
     * 签到时间（毫秒时间戳）
     */
    private Long signStartTime;            // <- String → Long

    /**
     * 签退时间（毫秒时间戳）
     */
    private Long signEndTime;              // <- String → Long

    /**
     * 签到/签退方式(gps)
     */
    private String signWay;

    /**
     * 状态 0:待处理 1:已完成 2:待审批
     */
    private Integer status;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 任务开始时间（毫秒时间戳）
     */
    private Long subBeginTime;             // <- String → Long

    /**
     * 任务结束时间（毫秒时间戳）
     */
    private Long subEndTime;               // <- String → Long

    /**
     * 巡店总结
     */
    private String summary;

    /**
     * 巡店总结图片
     */
    private String summaryPicture;

    /**
     * 巡店总结视频
     */
    private String summaryVideo;

    /**
     * 巡店人id
     */
    private String supervisorId;

    /**
     * 巡店人名称
     */
    private String supervisorName;

    /**
     * 巡店签名
     */
    private String supervisorSignature;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 总项数
     */
    private Integer totalColumnCount;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 巡店时长(单位毫秒)
     */
    private String tourTime;
}
