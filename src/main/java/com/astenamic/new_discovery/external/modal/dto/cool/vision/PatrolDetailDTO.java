package com.astenamic.new_discovery.external.modal.dto.cool.vision;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 巡店详情 DTO
 */
@Data
public class PatrolDetailDTO {

    /**
     * 奖罚金额（标准）
     */
    private String awardPunish;

    /**
     * 奖罚金额（实际）
     */
    private BigDecimal awardMoney;

    /**
     * 巡店记录id
     */
    private Long businessId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 奖罚金额（实际）
     */
    private String checkAwardPunish;

    /**
     * 检查图片
     */
    private String checkPics;

    /**
     * 检查结果：PASS / FAIL / INAPPLICABLE
     */
    private String checkResult;

    /**
     * 检查结果名称
     */
    private String checkResultName;

    /**
     * 得分
     */
    private BigDecimal checkScore;

    /**
     * 检查内容
     */
    private String checkText;

    /**
     * 检查视频
     */
    private String checkVideo;

    /**
     * 检查项名称
     */
    private String columnName;

    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 检查表名称
     */
    private String metaTableName;

    /**
     * 检查表id
     */
    private String metaTableId;

    /**
     * 标准图
     */
    private String standardPic;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 任务开始时间（毫秒时间戳）
     */
    private Long subBeginTime;

    /**
     * 任务结束时间（毫秒时间戳）
     */
    private Long subEndTime;

    /**
     * 巡店人id
     */
    private String supervisorId;

    /**
     * 巡店人名称
     */
    private String supervisorName;

    /**
     * 标准分
     */
    private BigDecimal supportScore;

    /**
     * 检查项属性
     */
    private Integer tableProperty;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 自定义结果
     */
    private String value;

    /**
     * 自定义格式
     */
    private String format;

    /**
     * 所属区域id
     */
    private Long regionId;
}
