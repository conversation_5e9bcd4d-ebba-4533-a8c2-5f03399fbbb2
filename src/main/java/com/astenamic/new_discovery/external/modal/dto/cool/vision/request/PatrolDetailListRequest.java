package com.astenamic.new_discovery.external.modal.dto.cool.vision.request;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询基础详情列表请求
 * URL：/open/patrol/detail/list
 * Method：POST
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PatrolDetailListRequest extends BaseRequest {

    /**
     * 企业id（必填）
     */
    private String enterpriseId;

    /**
     * 巡店记录id（可选）
     */
    private Long recordId;

    /**
     * 区域id（可选）
     */
    private String regionId;

    /**
     * 门店id（可选）
     */
    private String storeId;

    /**
     * 统计维度（可选）
     * 合格:PASS 
     * 不合格:FAIL 
     * 不适用:INAPPLICABLE
     */
    private String checkResult;

    /**
     * 创建时间开始（必填）
     * 时间范围限制为一天
     * 示例：1658474114672
     */
    private Long beginTime;

    /**
     * 创建时间结束（必填）
     * 示例：1658474114672
     */
    private Long endTime;

    /**
     * 检查表类型（必填）
     * 自定义检查表 DEFINE
     * 标准检查表  STANDARD
     */
    private String tableType;

    /**
     * 是否已完成（可选）
     * 1：是  0：否
     */
    private Integer isComplete;

    /**
     * 第几页（必填）
     */
    private Integer pageNum;

    /**
     * 分页大小（必填）
     */
    private Integer pageSize;

    @Override
    @JsonIgnore
    public String getUri() {
        return "/open/patrol/detail/list";
    }

    @Override
    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{CoolVisionPageDTO.class, PatrolDetailDTO.class};
    }
}
