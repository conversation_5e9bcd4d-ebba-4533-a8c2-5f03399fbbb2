package com.astenamic.new_discovery.external.modal.dto.shuhai;

import lombok.Data;

@Data
public class StockInfoDTO {
    /**
     * 客户仓库编码
     */
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    private String customerWarehouse;

    /**
     * 物料名称
     */
    private String skuName;

    /**
     * 可用库存
     */
    private String actualQty;

    /**
     * 不可用库存
     */
    private String unavailableQty;

    /**
     * 待上架数量
     */
    private String showPutPendingQty;

    /**
     * 待检品数量
     */
    private String showQualityPendingQty;

    /**
     * 残品数量
     */
    private String showQualityRejectQty;

    /**
     * 待拣库存
     */
    private String showPickPendingQty;

    /**
     * 待分拣库存
     */
    private String showSortPendingQty;

    /**
     * 待发货库存
     */
    private String showShipPendingQty;

    /**
     * 盘点锁定数量
     */
    private String showInventoryLockQty;

    /**
     * 库存冻结数量
     */
    private String showFreezeSignQty;

    /**
     * 移位锁定数量
     */
    private String showMoveLockQty;

    /**
     * 补货锁定数量
     */
    private String showReplenishLockQty;

    /**
     * 报缺锁定数量
     */
    private String showVacancyLockQty;

    /**
     * 盲收锁定数量
     */
    private String showBlindLockQty;

    /**
     * 补录锁定数量
     */
    private String showSupplementLockQty;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 客户物料编码
     */
    private String customerMaterialNumber;
}
