package com.astenamic.new_discovery.external.modal.dto.wlift;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 单条消费记录 DTO
 */
@Data
public class ConsumeRecordDTO {
    /**
     * 消费流水 ID
     */
    @JsonProperty("deal_id")
    private String dealId;

    /**
     * 消费用户卡号
     */
    @JsonProperty("cno")
    private String cno;

    /**
     * 消费门店 ID
     */
    @JsonProperty("sid")
    private String sid;

    /**
     * 被撤销消费的流水 ID，如果未撤销则为 0
     */
    @JsonProperty("related_id")
    private String relatedId;

    /**
     * 消费总金额（分）
     */
    @JsonProperty("total_fee")
    private Long totalFee;

    /**
     * 实收金额（分）
     */
    @JsonProperty("fee")
    private Long fee;

    /**
     * 使用储值支付金额（分）
     */
    @JsonProperty("stored_pay")
    private Long storedPay;

    /**
     * 使用实际储值支付金额（分）
     */
    @JsonProperty("stored_sale_pay")
    private Long storedSalePay;

    /**
     * 使用代金券抵扣金额（分）
     */
    @JsonProperty("cash_coupon_pay")
    private Long cashCouponPay;

    /**
     * 使用礼品券抵扣金额（分）
     */
    @JsonProperty("gift_coupon_pay")
    private Long giftCouponPay;

    /**
     * 使用积分数量
     */
    @JsonProperty("credit_num")
    private Long creditNum;

    /**
     * 使用积分抵扣金额（分）
     */
    @JsonProperty("credit_pay")
    private Long creditPay;

    /**
     * 奖励积分数量
     */
    @JsonProperty("credit_award")
    private Long creditAward;

    /**
     * 交易类型，1:充值，2:消费，3:撤销消费...
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 支付类型，1:现金，2:银行卡，3:店内微信...
     */
    @JsonProperty("pay_type")
    private Integer payType;

    /**
     * 交易时间，格式 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 第三方交易号
     */
    @JsonProperty("biz_id")
    private String bizId;

    /**
     * 消费使用券名称
     */
    @JsonProperty("user_coupon_name")
    private String userCouponName;

    /**
     * 消费使用券明细
     */
    @JsonProperty("user_coupon_list")
    private List<UserCouponDTO> userCouponList;

    /**
     * 会员等级名称
     */
    @JsonProperty("gradeName")
    private String gradeName;

    /**
     * 会员等级编码
     */
    @JsonProperty("gradeId")
    private String gradeId;

    /**
     * 消费一级来源
     */
    @JsonProperty("source")
    private String source;

    /**
     * 消费二级来源
     */
    @JsonProperty("channel")
    private String channel;


    private String phoneNumber;

    /**
     * 券明细 DTO
     */
    @Data
    public static class UserCouponDTO {
        /**
         * 券数量
         */
        @JsonProperty("num")
        private Integer num;

        /**
         * 券名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 使用券金额（元）
         */
        @JsonProperty("amount")
        private String amount;

        /**
         * 券 ID
         */
        @JsonProperty("couponId")
        private String couponId;

        /**
         * 券类型，1:代金券，2:礼品券，4:券包
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 券流水号
         */
        @JsonProperty("c2uId")
        private String c2uId;
    }

}

