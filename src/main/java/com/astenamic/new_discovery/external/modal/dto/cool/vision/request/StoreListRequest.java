package com.astenamic.new_discovery.external.modal.dto.cool.vision.request;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.StoreDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询门店列表请求
 * URL：/open/store/list
 * Method：POST
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StoreListRequest extends BaseRequest {

    /**
     * 区域id（必填）
     */
    private Long regionId;

    /**
     * 门店名称（可选）
     */
    private String storeName;

    /**
     * 大小（必填）
     */
    private Integer pageSize;

    /**
     * 页码（必填）
     */
    private Integer pageNum;

    /**
     * 是否为直连门店（可选）
     * true：是 false：否
     */
    private Boolean currentRegionData;

    @Override
    @JsonIgnore
    public String getUri() {
        return "/open/store/list";
    }

    @Override
    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{CoolVisionPageDTO.class, StoreDTO.class};
    }
}
