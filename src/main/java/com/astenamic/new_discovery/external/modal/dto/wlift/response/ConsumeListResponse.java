package com.astenamic.new_discovery.external.modal.dto.wlift.response;

import com.astenamic.new_discovery.external.modal.dto.wlift.ConsumeRecordDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 返回 DTO：包装列表或分页
 */
@Data
public class ConsumeListResponse {
    /**
     * 不分页时列表数据
     */
    private List<ConsumeRecordDTO> data;

    /**
     * 分页时加一层 data
     */
    @JsonProperty("page_option")
    private PageOption pageOption;

    @Data
    public static class PageOption {

        /**
         * 分页时最大页数
         */
        @JsonProperty("max_page")
        private Integer max_page;

        /**
         * 分页时总条数
         */
        @JsonProperty("total_items")
        private Integer total_items;
    }
}