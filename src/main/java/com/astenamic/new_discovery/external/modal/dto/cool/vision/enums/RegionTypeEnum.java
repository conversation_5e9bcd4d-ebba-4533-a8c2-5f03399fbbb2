package com.astenamic.new_discovery.external.modal.dto.cool.vision.enums;

/**
 * 区域类型枚举
 */
public enum RegionTypeEnum {

    /**
     * 根目录
     */
    ROOT("root", "根目录"),

    /**
     * 区域
     */
    PATH("path", "区域"),

    /**
     * 门店
     */
    STORE("store", "门店");

    private final String code;
    private final String description;

    RegionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static RegionTypeEnum fromCode(String code) {
        for (RegionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
