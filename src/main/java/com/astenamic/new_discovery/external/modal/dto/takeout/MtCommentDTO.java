package com.astenamic.new_discovery.external.modal.dto.takeout;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MtCommentDTO {

    @JSONField(name = "add_comment")
    private String addComment;

    @JSONField(name = "add_comment_time", format = "yyyy-MM-dd")
    private LocalDateTime addCommentTime;

    @JSONField(name = "comment_content")
    private String commentContent;

    @JSONField(name = "comment_id")
    private String commentId;

    @JSONField(name = "comment_lables")
    private String commentLables;

    @JSONField(name = "comment_order_detail")
    private List<CommentOrderDetailDTO> commentOrderDetail;

    @JSONField(name = "comment_pictures")
    private String commentPictures;

    @JSONField(name = "comment_score_type")
    private int commentScoreType;

    @JSONField(name = "comment_time", format = "yyyy-MM-dd")
    private LocalDateTime commentTime;

    @JSONField(name = "comment_type")
    private int commentType;

    @JSONField(name = "critic_food_list")
    private String criticFoodList;

    @JSONField(name = "delivery_comment_score")
    private int deliveryCommentScore;

    @JSONField(name = "e_reply_content")
    private String eReplyContent;

    @JSONField(name = "e_reply_time", format = "yyyy-MM-dd")
    private LocalDateTime eReplyTime;

    @JSONField(name = "food_comment_score")
    private int foodCommentScore;

    @JSONField(name = "order_comment_score")
    private int orderCommentScore;

    @JSONField(name = "over_delivery_time_desc")
    private String overDeliveryTimeDesc;

    @JSONField(name = "packing_score")
    private int packingScore;

    @JSONField(name = "praise_food_list")
    private String praiseFoodList;

    @JSONField(name = "product_score")
    private int productScore;

    @JSONField(name = "reply_status")
    private String replyStatus;

    @JSONField(name = "result")
    private String result;

    @JSONField(name = "ship_time")
    private int shipTime;

    @JSONField(name = "valid")
    private int valid;

    @Data
    public static class CommentOrderDetailDTO {

        @JSONField(name = "count")
        private int count;

        @JSONField(name = "food_name")
        private String foodName;
    }
}