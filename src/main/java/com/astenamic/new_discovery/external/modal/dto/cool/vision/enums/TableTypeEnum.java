package com.astenamic.new_discovery.external.modal.dto.cool.vision.enums;

/**
 * 检查表类型枚举
 */
public enum TableTypeEnum {

    /**
     * 自定义检查表
     */
    DEFINE("DEFINE", "自定义检查表"),

    /**
     * 标准检查表
     */
    STANDARD("STANDARD", "标准检查表");

    private final String code;
    private final String description;

    TableTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static TableTypeEnum fromCode(String code) {
        for (TableTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
