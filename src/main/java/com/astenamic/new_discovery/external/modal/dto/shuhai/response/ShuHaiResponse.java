package com.astenamic.new_discovery.external.modal.dto.shuhai.response;

import lombok.Data;

/**
 * 通用 API 响应封装
 *
 * @param <T> data 字段的类型
 */
@Data
public class ShuHaiResponse<T> {

    /**
     * 响应状态代码
     */
    private Integer status;

    /**
     * 响应信息
     */
    private String message;

    /**
     * 返回响应时间戳
     */
    private Long timestamp;

    /**
     * 接口返回的数据值
     */
    private T data;

    /**
     * 是否成功
     *
     * @return true if status is 200, false otherwise
     */
    public boolean isSuccess() {
        return status != null && status.equals(200);
    }

}
