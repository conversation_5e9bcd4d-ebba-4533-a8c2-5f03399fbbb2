package com.astenamic.new_discovery.external.modal.dto.takeout;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@Builder
public class GetCommentParam {

    private ShopMapping shopMapping;

    private TimeParam startTime;

    private TimeParam endTime;

    private int pageSize;

    @Data
    @Builder
    public static class ShopMapping {
        private String shopId;
        private String elmId;
        private String mtThirdsShopId;
    }

    @Data
    public static class TimeParam {
        private LocalDateTime dateTime;
        private String elmDateTime;
        private String mtDateTime;

        public TimeParam(LocalDateTime dateTime) {
            this.dateTime = dateTime;
            this.elmDateTime = dateTime.toString();
            this.mtDateTime = dateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
    }
}
