package com.astenamic.new_discovery.external.modal.dto.wlift.request;


import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.wlift.response.ConsumeListResponse;
import com.astenamic.new_discovery.external.modal.dto.wlift.ConsumeRecordDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class ConsumeListRequest extends WliftBaseRequest {

    /**
     * 起始时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("begin_date")
    private LocalDateTime beginDate;

    /**
     * 截止时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("end_date")
    private LocalDateTime endDate;

    /**
     * 查询第几页
     */
    private Integer page;

    /**
     * 门店id
     */
    @JsonProperty("shop_id")
    private Long shopId;

    /**
     * 是否整天查询 1：整天 2：指定时间段时分秒[默认]
     */
    @JsonProperty("is_allday")
    private Integer isAllday;

    /**
     * 是否全部流水 true 是；false 只查接口来源的流水
     */
    @JsonProperty("is_all")
    private Boolean isAll;

    /**
     * 是否包括分页信息 true 是；false 否
     * 不建议使用true，同时可能返回Object和List不像正常脑子写出来的
     */
    @JsonProperty("is_have_page")
    private Boolean isHavePage = false;


    @JsonIgnore
    @Override
    public Class<?>[] getResponseClass() {
        if (isHavePage) {
            return new Class<?>[]{ConsumeListResponse.class};
        } else {
            return new Class<?>[]{List.class, ConsumeRecordDTO.class};
        }
    }
}
