package com.astenamic.new_discovery.external.modal.dto.shuhai.request;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 【获取效期报表】请求体
 * <p>接口地址：platformApi/v1/order/purchase/report/query</p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExpiryReportRequest extends BaseRequest {

    /** ent 编码（必填） */
    private String ent;

    /** 客户仓库编码（选填） */
    private String customerWarehouseCode;

    private String purchaseOrderNo = "test";

    @Override
    public String getUri() {
        return "platformApi/v1/order/purchase/report/query";
    }

    @Override
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{ List.class, ExpiryReportDTO.class };
    }
}
