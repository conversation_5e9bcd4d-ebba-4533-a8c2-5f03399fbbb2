package com.astenamic.new_discovery.external.modal.dto.wlift;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AccountBasicsInfoDTO {
    private String uid;             // 用户id

    private String phone;           // 手机号

    private String name;            // 姓名

    private String cno;             // 卡号

    @JsonProperty("card_type")
    private String cardType;        // 会员卡类型

    private int grade;              // 当前等级id

    @JsonProperty("grade_name")
    private String gradeName;       // 当前等级名称

    @JsonProperty("grade_end_time")
    private String gradeEndTime;    // 等级到期时间 (yyyy-MM-dd)

    @JsonProperty("user_growth")
    private int userGrowth;         // 当前成长值

    private int balance;            // 储值余额 (分)

    private int credit;             // 用户积分

    @JsonProperty("user_type")
    private int userType;           // 来源渠道

    @JsonProperty("is_lock")
    private int isLock;             // 是否锁卡（1 锁卡，0 未锁）
}
