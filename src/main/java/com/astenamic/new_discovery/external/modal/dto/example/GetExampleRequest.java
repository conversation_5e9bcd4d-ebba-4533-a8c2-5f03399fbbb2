package com.astenamic.new_discovery.external.modal.dto.example;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * GET请求示例
 * 演示如何使用新的GET请求功能
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetExampleRequest extends BaseRequest {

    /**
     * 查询参数：页码
     */
    private Integer pageNum;

    /**
     * 查询参数：页面大小
     */
    private Integer pageSize;

    /**
     * 查询参数：关键词
     */
    private String keyword;

    /**
     * 查询参数：状态
     */
    private String status;

    @Override
    @JsonIgnore
    public String getUri() {
        return "/api/example/list";
    }

    @Override
    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{Object.class};
    }

    /**
     * 重写此方法来提供GET请求的查询参数
     */
    @Override
    @JsonIgnore
    public Map<String, Object> getQueryParams() {
        Map<String, Object> params = new HashMap<>();
        
        if (pageNum != null) {
            params.put("pageNum", pageNum);
        }
        if (pageSize != null) {
            params.put("pageSize", pageSize);
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            params.put("keyword", keyword);
        }
        if (status != null && !status.trim().isEmpty()) {
            params.put("status", status);
        }
        
        return params;
    }
}
