package com.astenamic.new_discovery.external.modal.dto.cool.vision.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Cool Vision 通用分页响应
 *
 * @param <T> 列表数据类型
 */
@Data
public class CoolVisionPageDTO<T> {

    /**
     * 总数
     */
    private Long total;

    /**
     * 第几页
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 数据列表
     */
    private List<T> list = new ArrayList<>();
}
