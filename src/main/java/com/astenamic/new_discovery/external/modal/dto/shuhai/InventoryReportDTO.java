package com.astenamic.new_discovery.external.modal.dto.shuhai;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 【获取进销存报表】单条记录
 */
@Data
public class InventoryReportDTO {

    /**
     * 物料编码
     */
    private String materialNumber;

    /**
     * 物料名称
     */
    private String skuName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 当日库存
     */
    private Float stockQty;

    /**
     * 前一天库存
     */
    private Float beforeStockQty;

    /**
     * 前一天入库
     */
    private Float beforeInnum;

    /**
     * 前一天出库
     */
    private Float beforeOutnum;

    /**
     * 差异
     */
    private Float difQty;

    /**
     * 客户物料编码
     */
    private String customerMaterialNumber;

    /**
     * 客户仓库编码
     */
    private String customerWarehouseCode;

    /**
     * 客户仓库名称
     */
    private String customerWarehouse;
}
