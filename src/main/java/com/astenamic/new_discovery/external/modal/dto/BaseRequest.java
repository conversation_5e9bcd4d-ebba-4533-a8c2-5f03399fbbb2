package com.astenamic.new_discovery.external.modal.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class BaseRequest {

    @JsonIgnore
    long timestamp = System.currentTimeMillis();

    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{Object.class};
    }

    @JsonIgnore
    public String getUri() {
        return "";
    }

}
