package com.astenamic.new_discovery.external.modal.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Map;

@Data
public class BaseRequest {

    @JsonIgnore
    long timestamp = System.currentTimeMillis();

    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{Object.class};
    }

    @JsonIgnore
    public String getUri() {
        return "";
    }

    /**
     * 获取GET请求的查询参数
     * 子类可以重写此方法来提供具体的查询参数
     *
     * @return 查询参数的Map，key为参数名，value为参数值
     */
    @JsonIgnore
    public Map<String, Object> getQueryParams() {
        return null;
    }

}
