package com.astenamic.new_discovery.external.modal.dto.cool.vision.request;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.RegionDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询组织架构列表(分层)请求
 * URL：/open/region/list
 * Method：POST
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RegionListRequest extends BaseRequest {

    /**
     * 上级区域id（可选）
     * 根节点id=1，不传返回根区域
     */
    private Long parentId;

    /**
     * 页码（必填）
     */
    private Integer pageNum;

    /**
     * 大小（必填）
     * 最小1 最大100
     */
    private Integer pageSize;

    @Override
    @JsonIgnore
    public String getUri() {
        return "/open/region/list";
    }

    @Override
    @JsonIgnore
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{CoolVisionPageDTO.class, RegionDTO.class};
    }
}
