package com.astenamic.new_discovery.external.modal.dto.cool.vision.enums;

/**
 * 检查结果枚举
 */
public enum CheckResultEnum {

    /**
     * 合格
     */
    PASS("PASS", "合格"),

    /**
     * 不合格
     */
    FAIL("FAIL", "不合格"),

    /**
     * 不适用
     */
    INAPPLICABLE("INAPPLICABLE", "不适用");

    private final String code;
    private final String description;

    CheckResultEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static CheckResultEnum fromCode(String code) {
        for (CheckResultEnum result : values()) {
            if (result.getCode().equals(code)) {
                return result;
            }
        }
        return null;
    }
}
