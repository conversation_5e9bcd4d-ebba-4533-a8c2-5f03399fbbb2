package com.astenamic.new_discovery.external.modal.dto.shuhai.request;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 调用 “platformApi/v1/stock/get” 的请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockGetRequest extends BaseRequest {

    /**
     * 必填：仓库（ent）编码
     */
    private String ent;

    /**
     * 选填：客户仓库编码
     */
    private String customerWarehouseCode;

    /**
     * 接口 uri
     */
    @Override
    public String getUri() {
        return "platformApi/v1/stock/get";
    }

    @Override
    public Class<?>[] getResponseClass() {
        return new Class<?>[]{List.class, StockInfoDTO.class};
    }
}
