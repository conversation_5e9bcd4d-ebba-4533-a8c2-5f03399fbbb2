package com.astenamic.new_discovery.external.modal.dto.cool.college.response;

import lombok.Data;

/**
 * Cool College API 响应封装
 *
 * @param <T> data 字段的类型
 */
@Data
public class CoolCollegeResponse<T> {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 响应数据
     */
    private T data;

    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }
}
