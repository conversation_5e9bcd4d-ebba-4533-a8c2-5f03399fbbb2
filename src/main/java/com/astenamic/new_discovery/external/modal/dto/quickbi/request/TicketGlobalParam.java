package com.astenamic.new_discovery.external.modal.dto.quickbi.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.alibaba.fastjson2.JSON;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketGlobalParam {

    private String paramKey;

    private String joinType;

    private List<Condition> conditionList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Condition {

        private String operate;

        private String value;

    }

    /**
     * 创建一个等于条件的参数
     *
     * @param paramKey 参数键
     * @param value    参数值
     * @return 条件参数
     */
    public static TicketGlobalParam createEqualCondition(String paramKey, String value) {
        Condition condition = Condition.builder()
                .operate("=")
                .value(value)
                .build();

        return TicketGlobalParam.builder()
                .paramKey(paramKey)
                .joinType("and")
                .conditionList(List.of(condition))
                .build();
    }

    /**
     * 将当前对象转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJson() {
        return JSON.toJSONString(this);
    }

    /**
     * 将对象列表转换为JSON字符串
     *
     * @param params 参数列表
     * @return JSON字符串
     */
    public static String toJson(List<TicketGlobalParam> params) {
        return JSON.toJSONString(params);
    }
}
