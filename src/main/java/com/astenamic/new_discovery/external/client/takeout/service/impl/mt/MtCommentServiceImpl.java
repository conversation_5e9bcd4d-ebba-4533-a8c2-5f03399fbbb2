package com.astenamic.new_discovery.external.client.takeout.service.impl.mt;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.external.client.takeout.factory.MtSystemParamFactory;
import com.astenamic.new_discovery.external.modal.dto.takeout.CommentDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetCommentParam;
import com.astenamic.new_discovery.external.modal.dto.takeout.MtCommentDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.MtSystemParam;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.CommentService;
import com.astenamic.new_discovery.external.utils.MtSignUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Instant;
import java.util.*;

@Service
@AllArgsConstructor
public class MtCommentServiceImpl implements CommentService {

    private static final Logger logger = LoggerFactory.getLogger(MtCommentServiceImpl.class);

    private final MtSystemParamFactory mtSystemParamFactory;

    private final RestTemplate restTemplate;


    @Override
    public List<CommentDTO> getCommentList(BrandEnum brand, GetCommentParam param) {
        if (param.getShopMapping() == null || StringUtils.isEmpty(param.getShopMapping().getMtThirdsShopId())) {
            logger.info("店铺映射关系不存在，brand={},param={}", brand, param);
            return List.of();
        }
        return getCommentByShopId(brand.getCode(), param).stream().map(c -> CommentDTO.createByMeituan(brand.getCode(), c)).toList();
    }


    /**
     * 获取美团评论列表
     * 根据门店id批量查询评价信息（新版）
     */
    private List<MtCommentDTO> getCommentByShopId(String brand, GetCommentParam param) {
        String BASE_URL = "https://waimaiopen.meituan.com/api/v1/comment/query";
        MtSystemParam systemParam = mtSystemParamFactory.createSystemParam(brand);

        List<MtCommentDTO> result = new ArrayList<>();
        int offset = 0;
        int size = 0;

        do {
            long timestamp = Instant.now().getEpochSecond();

            // 构建签名参数
            SortedMap<String, String> signParams = new TreeMap<>();
            signParams.put("app_id", systemParam.appId());
            signParams.put("timestamp", String.valueOf(timestamp));
            signParams.put("app_poi_code", param.getShopMapping().getMtThirdsShopId());
            signParams.put("start_time", param.getStartTime().getMtDateTime());
            signParams.put("end_time", param.getEndTime().getMtDateTime());
            signParams.put("pageoffset", String.valueOf(offset));
            signParams.put("pagesize", String.valueOf(param.getPageSize()));
            signParams.put("replyStatus", "-1"); // -1表示全部

            // 生成签名
            String sig = MtSignUtil.genSig(BASE_URL, signParams, systemParam.appSecret());

            // 构建请求URL（GET请求，所有参数都在query中）
            String url = UriComponentsBuilder
                    .fromHttpUrl(BASE_URL)
                    // 系统级参数
                    .queryParam("app_id", systemParam.appId())
                    .queryParam("timestamp", timestamp)
                    .queryParam("sig", sig)
                    // 业务参数
                    .queryParam("app_poi_code", param.getShopMapping().getMtThirdsShopId())
                    .queryParam("start_time", param.getStartTime().getMtDateTime())
                    .queryParam("end_time", param.getEndTime().getMtDateTime())
                    .queryParam("pageoffset", offset)
                    .queryParam("pagesize", param.getPageSize())
                    .queryParam("replyStatus", -1)
                    .toUriString();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> reqEntity = new HttpEntity<>(headers);

            // 发送GET请求
            ResponseEntity<String> resp = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    reqEntity,
                    String.class
            );

            List<MtCommentDTO> comments = new ArrayList<>();
            try {
                JSONObject res = JSONObject.parseObject(resp.getBody());
                if (res != null) {
                    if (res.containsKey("error")) {
                        JSONObject error = res.getJSONObject("error");
                        int errorCode = error.getInteger("code");
                        String errorMsg = error.getString("msg");
                        logger.error("查询评论失败，错误码：{}，错误信息：{}", errorCode, errorMsg);
                    } else if (res.containsKey("code")) {
                        String msg = res.getString("msg");
                        logger.error("查询评论失败，错误信息：{}", msg);
                    } else {
                        Object dataObj = res.get("data");
                        if (dataObj instanceof JSONArray dataArray) {
                            dataArray.forEach(item -> {
                                if (item instanceof JSONObject jsonObj) {
                                    comments.add(jsonObj.toJavaObject(MtCommentDTO.class));
                                } else {
                                    logger.error("转换异常：item 不是 JSONObject 类型，item：{}", item);
                                }
                            });
                        } else {
                            logger.error("返回结果异常，data 不是数组：{}", dataObj);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("解析评论数据异常", e);
            }

            offset += param.getPageSize();
            size = comments.size();
            result.addAll(comments);
        } while (size == param.getPageSize());

        return result;
    }
}
