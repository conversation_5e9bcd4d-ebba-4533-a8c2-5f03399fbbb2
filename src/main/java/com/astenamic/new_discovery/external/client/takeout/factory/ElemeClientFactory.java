package com.astenamic.new_discovery.external.client.takeout.factory;

import eleme.openapi.sdk.oauth.OAuthClient;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * 饿了么客户端工厂
 * 根据品牌名称获取对应的OAuthClient
 */
@RequiredArgsConstructor
public class ElemeClientFactory {

    private final Map<String, OAuthClient> clientMap;

    /**
     * 根据品牌名称获取OAuthClient
     *
     * @param brandName 品牌名称
     * @return 对应的OAuthClient
     */
    public OAuthClient getClient(String brandName) {
        if (!clientMap.containsKey(brandName)) {
            throw new IllegalArgumentException("未找到饿了么客户端: " + brandName);
        }
        return clientMap.get(brandName);
    }

    /**
     * 获取所有可用的品牌名称
     *
     * @return 品牌名称集合
     */
    public Iterable<String> getAllBrandNames() {
        return clientMap.keySet();
    }
}