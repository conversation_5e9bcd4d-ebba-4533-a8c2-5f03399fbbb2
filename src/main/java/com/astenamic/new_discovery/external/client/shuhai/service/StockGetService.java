package com.astenamic.new_discovery.external.client.shuhai.service;


import com.astenamic.new_discovery.external.client.shuhai.session.ShuHaiSession;
import com.astenamic.new_discovery.external.config.ShuHaiConfiguration;
import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.request.StockGetRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.response.ShuHaiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 【代仓实时库存查询】
 */
@Slf4j
@Component
public class StockGetService extends ShuHaiSession<StockGetRequest, List<StockInfoDTO>> {

    public StockGetService(ShuHaiConfiguration cfg,
                           RestTemplate restTemplate,
                           ObjectMapper mapper) {
        super(cfg, restTemplate, mapper);
    }

    public List<StockInfoDTO> getStockList(String ent, String customerWarehouseCode) {
        StockGetRequest req = new StockGetRequest();
        req.setEnt(ent);
        req.setCustomerWarehouseCode(customerWarehouseCode);
        ShuHaiResponse<List<StockInfoDTO>> post = super.post(req);
        if (post.isSuccess() && post.getData() != null) {
            return post.getData();
        } else {
            log.error("获取蜀海库存失败: {}", post.getMessage());
            return List.of();
        }
    }

}

