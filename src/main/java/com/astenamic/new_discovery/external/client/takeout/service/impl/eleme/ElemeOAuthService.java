package com.astenamic.new_discovery.external.client.takeout.service.impl.eleme;

import com.astenamic.new_discovery.external.client.takeout.factory.ElemeClientFactory;
import eleme.openapi.sdk.oauth.OAuthClient;
import eleme.openapi.sdk.oauth.response.Token;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 饿了么OAuth服务
 * 支持多品牌账号的Token管理
 */
@Service
@RequiredArgsConstructor
public class ElemeOAuthService {

    private static final Logger logger = LoggerFactory.getLogger(ElemeOAuthService.class);

    // 当 token 有效期小于此秒数时认为即将过期，需要刷新
    private static final long ELM_REFRESH_BEFORE_EXPIRY = 5 * 60;

    private final ElemeClientFactory elemeClientFactory;

    // 每个品牌对应一个AccessToken和锁
    private final Map<String, AccessTokenWithLock> tokenMap = new ConcurrentHashMap<>();

    /**
     * 根据品牌名称获取Token
     *
     * @param brandName 品牌名称
     * @return 对应的Token
     */
    public Token getToken(String brandName) {
        AccessTokenWithLock tokenWithLock = tokenMap.computeIfAbsent(
                brandName, k -> new AccessTokenWithLock()
        );

        if (tokenWithLock.getToken() != null &&
                tokenWithLock.getExpireTime().isAfter(LocalDateTime.now().plusSeconds(ELM_REFRESH_BEFORE_EXPIRY))) {
            return tokenWithLock.getToken();
        }

        if (tokenWithLock.getLock().tryLock()) {
            try {
                // 双重检查
                if (tokenWithLock.getToken() == null ||
                        tokenWithLock.getExpireTime().isBefore(LocalDateTime.now().plusSeconds(ELM_REFRESH_BEFORE_EXPIRY))) {
                    refreshToken(brandName);
                }
                return tokenWithLock.getToken();
            } finally {
                tokenWithLock.getLock().unlock();
            }
        } else {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            // 再次读取缓存token
            if (tokenWithLock.getToken() == null) {
                refreshToken(brandName);
            }
            return tokenWithLock.getToken();
        }
    }

    /**
     * 刷新指定品牌的Token
     *
     * @param brandName 品牌名称
     */
    private void refreshToken(String brandName) {
        try {
            AccessTokenWithLock tokenWithLock = tokenMap.get(brandName);
            OAuthClient oAuthClient = elemeClientFactory.getClient(brandName);
            Token token = oAuthClient.getTokenInClientCredentials();
            long expireSeconds = token.getExpires();
            tokenWithLock.setToken(token);
            tokenWithLock.setExpireTime(LocalDateTime.now().plusSeconds(expireSeconds));
            logger.info("成功获取{}品牌的access token，有效期：{}秒", brandName, expireSeconds);
        } catch (Exception e) {
            logger.error("获取{}品牌的access token失败", brandName, e);
            throw new RuntimeException("获取饿了么access token失败:" + brandName, e);
        }
    }


    @Data
    public static class AccessTokenWithLock {
        private Token token;
        private LocalDateTime expireTime;
        private final ReentrantLock lock = new ReentrantLock();
    }
}