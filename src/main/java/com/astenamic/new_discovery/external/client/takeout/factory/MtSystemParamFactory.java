package com.astenamic.new_discovery.external.client.takeout.factory;


import com.astenamic.new_discovery.external.modal.dto.takeout.MtSystemParam;
import lombok.AllArgsConstructor;

import java.util.Map;

@AllArgsConstructor
public class MtSystemParamFactory {

    private final Map<String, MtSystemParam> systemParamMap;

    public MtSystemParam createSystemParam(String brandName) {
        if (!systemParamMap.containsKey(brandName)) {
            throw new IllegalArgumentException("未找到美团系统参数: " + brandName);
        }
        return systemParamMap.get(brandName);
    }
}
