package com.astenamic.new_discovery.external.client.takeout.service.impl.mt;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.external.client.takeout.factory.MtSystemParamFactory;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetProductParam;
import com.astenamic.new_discovery.external.modal.dto.takeout.MtProductDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.MtSystemParam;
import com.astenamic.new_discovery.external.modal.dto.takeout.ProductDTO;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.ShopGoodsService;
import com.astenamic.new_discovery.external.utils.MtSignUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


import java.time.Instant;
import java.util.*;

@Service
@AllArgsConstructor
public class MtShopGoodsServiceImpl implements ShopGoodsService {

    private static final Logger logger = LoggerFactory.getLogger(MtShopGoodsServiceImpl.class);

    private final MtSystemParamFactory mtSystemParamFactory;

    private final RestTemplate restTemplate;

    @Override
    public List<ProductDTO> getGoodsList(BrandEnum brand, GetProductParam param) {
        if (StringUtils.isEmpty(param.getShopMapping().getMtThirdsShopId())) {
            return Collections.emptyList();
        }
        return queryFoodListAll(brand.getCode(), param).stream().map(ProductDTO::from).toList();
    }


    private List<MtProductDTO> queryFoodListAll(String brand, GetProductParam param) {
        String BASE_URL = "https://waimaiopen.meituan.com/gw/api/v1/food/listAll";
        MtSystemParam systemParam = mtSystemParamFactory.createSystemParam(brand);
        int offset = 0;
        int size = 0;

        List<MtProductDTO> result = new ArrayList<>();

        do {
            long timestamp = Instant.now().getEpochSecond();
            SortedMap<String, String> signParams = new TreeMap<>();
            signParams.put("app_id", systemParam.appId());
            signParams.put("timestamp", String.valueOf(timestamp));
            signParams.put("app_poi_code", param.getShopMapping().getMtThirdsShopId());
            signParams.put("offset", String.valueOf(offset));
            signParams.put("limit", String.valueOf(param.getPageSize()));

            String sig = MtSignUtil.genSig(BASE_URL, signParams, systemParam.appSecret());

            MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
            form.add("app_poi_code", param.getShopMapping().getMtThirdsShopId());
            form.add("offset", String.valueOf(offset));
            form.add("limit", String.valueOf(param.getPageSize()));

            String url = UriComponentsBuilder
                    .fromHttpUrl(BASE_URL)
                    .queryParam("app_id", systemParam.appId())
                    .queryParam("timestamp", timestamp)
                    .queryParam("sig", sig)
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, String>> reqEntity = new HttpEntity<>(form, headers);

            ResponseEntity<String> resp = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    reqEntity,
                    String.class
            );
            List<MtProductDTO> products = new ArrayList<>();
            try {
                JSONObject res = JSONObject.parseObject(resp.getBody());
                if (res != null) {
                    if (res.containsKey("error")) {
                        JSONObject error = res.getJSONObject("error");
                        int errorCode = error.getInteger("code");
                        String errorMsg = error.getString("msg");
                        logger.error("请求失败，错误码：{}，错误信息：{}", errorCode, errorMsg);
                    } else if (res.containsKey("code")) {
                        String msg = res.getString("msg");
                        logger.error("请求失败，错误信息：{}", msg);
                    } else {
                        Object dataObj = res.get("data");
                        if (dataObj instanceof JSONArray dataArray) {
                            dataArray.forEach(item -> {
                                if (item instanceof JSONObject jsonObj) {
                                    products.add(jsonObj.toJavaObject(MtProductDTO.class));
                                } else {
                                    logger.error("转换异常：item 不是 JSONObject 类型，item：{}", item);
                                }
                            });
                        } else {
                            logger.error("返回结果异常，data 不是数组：{}", dataObj);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("解析返回数据异常", e);
            }

            offset += param.getPageSize();
            size = products.size();
            result.addAll(products);
        } while (size == param.getPageSize());
        return result;
    }
}
