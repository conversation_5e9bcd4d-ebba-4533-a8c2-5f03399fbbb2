package com.astenamic.new_discovery.external.client.cool.vision.service;

import com.astenamic.new_discovery.external.client.cool.vision.CoolVisionSession;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.RegionDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.request.RegionListRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionBizData;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询组织架构列表(分层)服务
 */
@Slf4j
@Service
public class RegionListService extends CoolVisionSession<RegionListRequest, CoolVisionPageDTO<RegionDTO>> {

    public RegionListService(CoolConfiguration configuration,
                             RestTemplate restTemplate,
                             ObjectMapper objectMapper) {
        super(configuration, restTemplate, objectMapper);
    }

    /**
     * 获取根区域列表（自动分页获取所有数据）
     *
     * @return 根区域列表
     */
    public List<RegionDTO> getRootRegionList() {
        return getRegionList(null);
    }

    /**
     * 获取指定父级区域的子区域列表（自动分页获取所有数据）
     *
     * @param parentId 父级区域ID，null表示获取根区域
     * @return 区域列表
     */
    public List<RegionDTO> getRegionList(Long parentId) {
        int size = 0;
        int page = 1;

        List<RegionDTO> result = new ArrayList<>();

        RegionListRequest request = new RegionListRequest();
        request.setPageSize(100);
        request.setParentId(parentId);

        do {
            request.setPageNum(page);
            CoolVisionResponse<CoolVisionPageDTO<RegionDTO>> response = super.post(request);
            if (!response.isSuccess()) {
                break;
            }
            CoolVisionBizData<CoolVisionPageDTO<RegionDTO>> bizData = response.getBizData();
            CoolVisionPageDTO<RegionDTO> data = bizData.getData();
            List<RegionDTO> list = data.getList();
            size = list.size();
            result.addAll(list);
            page++;
        } while (size == 100);

        return result;
    }

    /**
     * 获取区域列表（单页）
     *
     * @param request 请求参数
     * @return 区域列表分页数据
     */
    public CoolVisionPageDTO<RegionDTO> getRegionListPage(RegionListRequest request) {
        try {
            CoolVisionResponse<CoolVisionPageDTO<RegionDTO>> response = super.post(request);
            
            if (response.isSuccess() && response.getBizData() != null) {
                return response.getBizData().getData();
            } else {
                log.error("获取区域列表失败: 网关层code={}, message={}, bizData={}", 
                         response.getCode(), response.getMessage(), response.getBizData());
                return null;
            }
        } catch (Exception e) {
            log.error("调用区域列表接口异常", e);
            return null;
        }
    }

    /**
     * 递归获取完整的组织架构树
     *
     * @param parentId 起始父级ID，null表示从根开始
     * @return 完整的组织架构列表（包含所有层级）
     */
    public List<RegionDTO> getFullRegionTree(Long parentId) {
        List<RegionDTO> allRegions = new ArrayList<>();
        List<RegionDTO> currentLevel = getRegionList(parentId);
        
        allRegions.addAll(currentLevel);
        
        // 递归获取每个区域的子区域
        for (RegionDTO region : currentLevel) {
            if (!"store".equals(region.getRegionType())) { // 门店类型不再有子级
                List<RegionDTO> children = getFullRegionTree(region.getId());
                allRegions.addAll(children);
            }
        }
        
        return allRegions;
    }
}
