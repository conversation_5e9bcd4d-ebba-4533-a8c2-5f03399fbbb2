package com.astenamic.new_discovery.external.client.cool.vision.service;

import com.astenamic.new_discovery.external.client.cool.vision.CoolVisionSession;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.StoreDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.request.StoreListRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionBizData;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询门店列表服务
 */
@Slf4j
@Service
public class StoreListService extends CoolVisionSession<StoreListRequest, CoolVisionPageDTO<StoreDTO>> {

    public StoreListService(CoolConfiguration configuration,
                            RestTemplate restTemplate,
                            ObjectMapper objectMapper) {
        super(configuration, restTemplate, objectMapper);
    }

    /**
     * 获取指定区域的所有门店列表（自动分页获取所有数据）
     *
     * @param regionId 区域ID
     * @return 门店列表
     */
    public List<StoreDTO> getStoreList(Long regionId) {
        return getStoreList(regionId, null, null);
    }

    /**
     * 获取指定区域的门店列表（带条件筛选，自动分页获取所有数据）
     *
     * @param regionId 区域ID
     * @param storeName 门店名称（可选）
     * @param currentRegionData 是否为直连门店（可选）
     * @return 门店列表
     */
    public List<StoreDTO> getStoreList(Long regionId, String storeName, Boolean currentRegionData) {
        int size = 0;
        int page = 1;

        List<StoreDTO> result = new ArrayList<>();

        StoreListRequest request = new StoreListRequest();
        request.setPageSize(100);
        request.setRegionId(regionId);
        request.setStoreName(storeName);
        request.setCurrentRegionData(currentRegionData);

        do {
            request.setPageNum(page);
            CoolVisionResponse<CoolVisionPageDTO<StoreDTO>> response = super.post(request);
            if (!response.isSuccess()) {
                break;
            }
            CoolVisionBizData<CoolVisionPageDTO<StoreDTO>> bizData = response.getBizData();
            CoolVisionPageDTO<StoreDTO> data = bizData.getData();
            List<StoreDTO> list = data.getList();
            size = list.size();
            result.addAll(list);
            page++;
        } while (size == 100);

        return result;
    }

    /**
     * 获取门店列表（单页）
     *
     * @param request 请求参数
     * @return 门店列表分页数据
     */
    public CoolVisionPageDTO<StoreDTO> getStoreListPage(StoreListRequest request) {
        try {
            CoolVisionResponse<CoolVisionPageDTO<StoreDTO>> response = super.post(request);
            
            if (response.isSuccess() && response.getBizData() != null) {
                return response.getBizData().getData();
            } else {
                log.error("获取门店列表失败: 网关层code={}, message={}, bizData={}", 
                         response.getCode(), response.getMessage(), response.getBizData());
                return null;
            }
        } catch (Exception e) {
            log.error("调用门店列表接口异常", e);
            return null;
        }
    }

    /**
     * 根据门店名称搜索门店
     *
     * @param regionId 区域ID
     * @param storeName 门店名称关键字
     * @return 匹配的门店列表
     */
    public List<StoreDTO> searchStoresByName(Long regionId, String storeName) {
        return getStoreList(regionId, storeName, null);
    }

    /**
     * 获取直连门店列表
     *
     * @param regionId 区域ID
     * @return 直连门店列表
     */
    public List<StoreDTO> getDirectStores(Long regionId) {
        return getStoreList(regionId, null, true);
    }

    /**
     * 获取非直连门店列表
     *
     * @param regionId 区域ID
     * @return 非直连门店列表
     */
    public List<StoreDTO> getIndirectStores(Long regionId) {
        return getStoreList(regionId, null, false);
    }
}
