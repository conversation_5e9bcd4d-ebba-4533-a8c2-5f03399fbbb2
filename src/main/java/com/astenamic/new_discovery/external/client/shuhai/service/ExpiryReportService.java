package com.astenamic.new_discovery.external.client.shuhai.service;


import com.astenamic.new_discovery.external.client.shuhai.session.ShuHaiSession;
import com.astenamic.new_discovery.external.config.ShuHaiConfiguration;
import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.request.ExpiryReportRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.request.StockGetRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.response.ShuHaiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 【获取效期报表】
 */
@Slf4j
@Component
public class ExpiryReportService extends ShuHaiSession<ExpiryReportRequest, List<ExpiryReportDTO>> {

    public ExpiryReportService(ShuHaiConfiguration cfg,
                               RestTemplate restTemplate,
                               ObjectMapper mapper) {
        super(cfg, restTemplate, mapper);
    }

    public List<ExpiryReportDTO> getExpiryReportList(String ent, String customerWarehouseCode) {
        ExpiryReportRequest req = new ExpiryReportRequest();
        req.setEnt(ent);
        req.setCustomerWarehouseCode(customerWarehouseCode);
        ShuHaiResponse<List<ExpiryReportDTO>> post = super.post(req);
        if (post.isSuccess() && post.getData() != null) {
            return post.getData();
        } else {
            log.error("获取蜀海效期报表失败: {}", post.getMessage());
            return List.of();
        }
    }

}