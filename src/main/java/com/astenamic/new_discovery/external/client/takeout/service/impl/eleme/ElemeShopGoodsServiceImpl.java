package com.astenamic.new_discovery.external.client.takeout.service.impl.eleme;

import com.astenamic.new_discovery.external.config.ElemeConfiguration;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetProductParam;
import com.astenamic.new_discovery.external.modal.dto.takeout.ProductDTO;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.ShopGoodsService;
import eleme.openapi.sdk.api.entity.product.OItem;
import eleme.openapi.sdk.api.entity.product.QueryPage;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.ProductService;
import eleme.openapi.sdk.config.Config;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@AllArgsConstructor
public class ElemeShopGoodsServiceImpl implements ShopGoodsService {


    private static final Logger logger = LoggerFactory.getLogger(ElemeShopGoodsServiceImpl.class);

    private final ElemeConfiguration elemeConfiguration;

    private final ElemeOAuthService elemeOAuthService;

    @Override
    public List<ProductDTO> getGoodsList(BrandEnum brand, GetProductParam param) {
        if (param.getShopMapping().getElmId() == null) {
            return Collections.emptyList();
        }
        return queryItemByPage(brand.getCode(), param).stream().map(ProductDTO::from).toList();
    }

    private List<OItem> queryItemByPage(String brand, GetProductParam param) {
        try {
            Config config = elemeConfiguration.getConfig(brand);
            ProductService productService = new ProductService(config, elemeOAuthService.getToken(brand));
            List<OItem> result = new ArrayList<>();
            int offset = 0;
            int size = 0;
            QueryPage queryPage = new QueryPage();
            queryPage.setShopId(param.getShopMapping().getElmId());
            queryPage.setLimit((long) param.getPageSize());
            do {
                queryPage.setOffset((long) offset);
                List<OItem> products = new ArrayList<>();
                try {
                    products = productService.queryItemByPage(queryPage);
                } catch (ServiceException e) {
                    logger.error("请求失败，param={},code={},msg={}", param, e.getCode(), e.getMessage());
                    throw e;
                }
                offset += param.getPageSize();
                size = products.size();
                result.addAll(products);
            } while (size == param.getPageSize());
            return result;
        } catch (Exception e) {
            logger.error("未知异常", e);
        }
        throw new RuntimeException("获取店铺评价失败");
    }

}
