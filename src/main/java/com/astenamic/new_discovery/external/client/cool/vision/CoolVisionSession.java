package com.astenamic.new_discovery.external.client.cool.vision;

import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.astenamic.new_discovery.external.client.BaseClient;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public abstract class CoolVisionSession<Q extends BaseRequest, T> extends BaseClient<Q, CoolVisionResponse<T>> {


    public final String enterpriseId;

    private final String privateKey;

    private final String baseUrl;

    protected CoolVisionSession(CoolConfiguration configuration,
                                RestTemplate restTemplate,
                                ObjectMapper objectMapper) {

        super((Class<CoolVisionResponse<T>>) (Class<?>) CoolVisionResponse.class, restTemplate, objectMapper);

        this.enterpriseId = configuration.getEnterpriseId();
        this.privateKey = configuration.getVision().getPrivateKey();
        this.baseUrl = configuration.getVision().getBaseUrl();
    }

    @Override
    public String getClientName() {
        return "coolVision";
    }

    @Override
    public String buildUrl(String uri) {
        return UriComponentsBuilder.fromHttpUrl(baseUrl).path(uri).toUriString();
    }

    @Override
    public HttpEntity<?> buildEntity(Q bizParams) throws JsonProcessingException, AlipayApiException {
        long ts = bizParams.getTimestamp();
        String jsonBody = objectMapper.writeValueAsString(bizParams);

        Map<String, Object> params = new HashMap<>();
        params.put("bizContent", bizParams);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("enterpriseId", this.enterpriseId);
        headers.set("timestamp", String.valueOf(ts));
        headers.set("sign", buildSign(jsonBody, ts));
        return new HttpEntity<>(objectMapper.writeValueAsString(params), headers);
    }

    public String buildSign(String bizParams, long ts) throws AlipayApiException {
        String privateKey = this.privateKey;
        Map<String, String> params = new HashMap<>();
        params.put("bizContent", bizParams);
        params.put("timestamp", String.valueOf(ts));
        params.put("enterpriseId", enterpriseId);
        return AlipaySignature.rsaSign(params, privateKey, "UTF-8");
    }
}
