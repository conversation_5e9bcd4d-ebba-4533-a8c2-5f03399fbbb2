package com.astenamic.new_discovery.external.client.quickbi;

import com.aliyun.sdk.service.quickbi_public20220101.AsyncClient;
import com.aliyun.sdk.service.quickbi_public20220101.models.CreateTicketRequest;
import com.astenamic.new_discovery.external.config.AliyunConfiguration;
import com.astenamic.new_discovery.external.modal.dto.quickbi.request.TicketGlobalParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
@RequiredArgsConstructor
public class TicketService {

    private final AsyncClient asyncClient;

    private final AliyunConfiguration aliyunConfiguration;

    /**
     * 异步获取票据
     *
     * @param workspaceId  工作空间ID
     * @param globalParams 全局参数
     * @return CompletableFuture<String> 包含票据的Future
     */
    public CompletableFuture<String> getTicketAsync(String workspaceId, String watermarkParam, List<TicketGlobalParam> globalParams) {
        List<TicketGlobalParam> params = globalParams != null ? globalParams : List.of();
        String paramsJson = TicketGlobalParam.toJson(params);
        log.info("开始请求QuickBI票据, workspaceId: {}, params: {}", workspaceId, paramsJson);

        CreateTicketRequest request = CreateTicketRequest.builder()
                .worksId(workspaceId)
                .ticketNum(10)
                .watermarkParam(watermarkParam)
                .globalParam(paramsJson)
                .build();

        return asyncClient.createTicket(request)
                .thenApply(response -> {
                    if (response != null && response.getBody() != null) {
                        Boolean success = response.getBody().getSuccess();
                        String result = response.getBody().getResult();
                        String requestId = response.getBody().getRequestId();

                        if (Boolean.TRUE.equals(success) && result != null) {
                            log.info("获取QuickBI票据成功: {}, requestId: {}", result, requestId);
                            return result; // Result字段直接包含ticket值
                        } else {
                            log.error("获取QuickBI票据失败, success: {}, requestId: {}", success, requestId);
                            throw new RuntimeException("获取QuickBI票据失败, API返回失败状态");
                        }
                    }
                    log.error("获取QuickBI票据失败, 响应为空");
                    throw new RuntimeException("获取QuickBI票据失败, 响应为空");
                })
                .exceptionally(e -> {
                    log.error("获取QuickBI票据异常", e);
                    throw new RuntimeException("获取QuickBI票据异常: " + e.getMessage(), e);
                });
    }

    /**
     * 同步获取票据 (带超时)
     *
     * @param workspaceId  工作空间ID
     * @param globalParams 全局参数
     * @param timeout      超时时间(毫秒)
     * @return 票据字符串
     */
    public String getTicket(String workspaceId, String watermarkParam, List<TicketGlobalParam> globalParams, long timeout) {
        try {
            return getTicketAsync(workspaceId, watermarkParam, globalParams).get(timeout, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取票据被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("获取票据失败", e.getCause());
        } catch (TimeoutException e) {
            throw new RuntimeException("获取票据超时", e);
        }
    }

    /**
     * 同步获取票据 (默认5秒超时)
     *
     * @param workspaceId  工作空间ID
     * @param globalParams 全局参数
     * @return 票据字符串
     */
    public String getTicket(String workspaceId, String watermarkParam, List<TicketGlobalParam> globalParams) {
        return getTicket(workspaceId, watermarkParam, globalParams, 5000);
    }

}
