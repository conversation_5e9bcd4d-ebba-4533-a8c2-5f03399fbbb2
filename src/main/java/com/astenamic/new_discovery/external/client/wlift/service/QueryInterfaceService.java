package com.astenamic.new_discovery.external.client.wlift.service;

import com.astenamic.new_discovery.external.modal.dto.wlift.AccountBasicsInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.wlift.request.AccountBasicsInfoRequest;
import com.astenamic.new_discovery.external.modal.dto.wlift.response.AccountBasicsInfoResponse;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.config.WliftConfiguration;
import com.astenamic.new_discovery.external.client.wlift.constant.ApiConstant;
import com.astenamic.new_discovery.external.modal.dto.wlift.request.ConsumeListRequest;
import com.astenamic.new_discovery.external.modal.dto.wlift.ConsumeRecordDTO;
import com.astenamic.new_discovery.external.modal.dto.wlift.response.WliftResponse;
import com.astenamic.new_discovery.external.client.wlift.session.WliftSession;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class QueryInterfaceService extends WliftSession {

    private static final Logger logger = LoggerFactory.getLogger(QueryInterfaceService.class);

    public QueryInterfaceService(WliftConfiguration wliftConfiguration, RestTemplate restTemplate, ObjectMapper objectMapper) {
        super(wliftConfiguration, restTemplate, objectMapper);
    }

    public List<ConsumeRecordDTO> queryConsumeList(BrandEnum brand, LocalDateTime beginDate, LocalDateTime endDate, Long shopId) {
        if (!supports(brand.getCode())) {
            return Collections.emptyList();
        }
        if (beginDate == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        if (endDate == null) {
            throw new IllegalArgumentException("结束时间不能为空");
        }

        ConsumeListRequest param = ConsumeListRequest.builder()
                .beginDate(beginDate)
                .endDate(endDate)
                .shopId(shopId).isAllday(1).isHavePage(false).build();

        int page = 1;
        int size = 0;
        int lastSize = 0;

        List<ConsumeRecordDTO> result = new ArrayList<>();
        do {
            param.setPage(page);
            WliftResponse<List<ConsumeRecordDTO>> response = null;
            try {
                response = super.post(brand.getCode(), ApiConstant.CONSUME_RECORD_LIST_URL, param);
            } catch (Exception e) {
                logger.error("请求失败，param={},msg={}", param, e.getMessage());
                throw new RuntimeException("请求失败，param=" + param, e);
            }
            if (response == null || !response.isSuccess()) {
                break;
            }
            if (page == 1) {
                lastSize = response.getRes().size();
            }
            size = response.getRes().size();
            result.addAll(response.getRes());
            page++;
        } while (size == lastSize);

        return result;
    }

    public AccountBasicsInfoDTO queryAccountBasicsInfo(BrandEnum brand, String cno) {

        if (!supports(brand.getCode())) {
            return null;
        }
        if (StringUtils.isEmpty(cno)) {
            throw new IllegalArgumentException("会有卡号不能为空");
        }

        AccountBasicsInfoRequest params = AccountBasicsInfoRequest.builder().cno(cno).build();
        WliftResponse<AccountBasicsInfoResponse<AccountBasicsInfoDTO>> response = null;

        try {
            response = super.post(brand.getCode(), ApiConstant.ACCOUNT_BASICS_INFO, params);
        } catch (Exception e) {
            logger.error("请求失败，cno={},msg={}", cno, e.getMessage());
            throw new RuntimeException("请求失败，param=" + cno, e);
        }

        return Optional.ofNullable(response)
                .filter(WliftResponse::isSuccess)
                .map(WliftResponse::getRes)
                .map(AccountBasicsInfoResponse::getData)
                .flatMap(map -> map.values().stream()
                        .filter(dto -> cno.equals(dto.getCno()))
                        .findFirst())
                .orElse(null);

    }

    public boolean supports(String brand) {
        return super.supports(brand);
    }

}
