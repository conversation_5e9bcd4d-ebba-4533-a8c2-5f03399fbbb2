package com.astenamic.new_discovery.external.client.wlift.session;

import com.astenamic.new_discovery.external.config.WliftConfiguration;
import com.astenamic.new_discovery.external.modal.dto.wlift.request.WliftBaseRequest;
import com.astenamic.new_discovery.external.modal.dto.wlift.response.WliftResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

@Service
@AllArgsConstructor
public abstract class WliftSession {

    private static final Logger logger = LoggerFactory.getLogger(WliftSession.class);

    private final WliftConfiguration wliftConfiguration;

    private final RestTemplate restTemplate;

    private final ObjectMapper objectMapper;

    protected String getUrl(String uri) {
        return this.wliftConfiguration.getBaseUrl() + uri;
    }

    /**
     * 通用 POST 请求，支持任意 POJO 业务参数
     *
     * @param path      接口路径，如 "/consume/list"
     * @param bizParams 任意业务对象
     * @param brand     品牌枚举
     * @param <P>       业务参数类型
     * @param <T>       返回 res 字段类型
     */
    public <P extends WliftBaseRequest, T> WliftResponse<T> post(String brand, String path, P bizParams) throws JsonProcessingException {
        // 1. 获取品牌配置
        WliftConfiguration.WliftConfig cfg = wliftConfiguration.getConfigs().get(brand);
        if (cfg == null) {
            throw new IllegalArgumentException("未找到品牌配置: " + brand);
        }
        String appid = cfg.getAppId();
        String appkey = cfg.getAppKey();
        String version = wliftConfiguration.getV();
        String fmt = wliftConfiguration.getFmt();
        // 2. 将 bizParams 序列化成 Map 用于签名
        Map<String, Object> paramMap;
        try {
            String paramJson = objectMapper.writeValueAsString(bizParams);
            paramMap = objectMapper.readValue(paramJson, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("序列化业务参数失败", e);
        }
        long ts = System.currentTimeMillis() / 1000;
        String sig = genSign(paramMap, appid, appkey, version, ts);
        // 3. 构建 multipart/form-data 表单
        MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
        try {
            form.add("req", objectMapper.writeValueAsString(bizParams));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("序列化业务参数失败", e);
        }
        form.add("appid", appid);
        form.add("v", version);
        form.add("ts", String.valueOf(ts));
        form.add("sig", sig);
        form.add("fmt", fmt);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(form, headers);
        // 4. 发送请求并获取 JSON
        String url = getUrl(path);
        ResponseEntity<String> respEntity = restTemplate.postForEntity(url, request, String.class);
        String body = respEntity.getBody();
        if (body == null) {
            throw new RuntimeException("接口返回为空");
        }
        // 5. 解析并返回
        return this.parseBody(body, WliftResponse.class, bizParams.getResponseClass());
    }


    /**
     * 反序列化 JSON 到泛型对象 —— 支持任意深度嵌套与任意个数泛型参数
     *
     * @param json      JSON 字符串
     * @param mainClass 主类型（可是无泛型，也可带多个泛型参数）
     * @param flatTypes 按「每个泛型参数」的嵌套链平铺传入，
     *                  例如：
     *                  - List<User>           : List.class, User.class
     *                  - List<List<User>>     : List.class, List.class, User.class
     *                  - Map<String, User>    : String.class, User.class
     *                  - Map<List<X>, Set<Y>> : List.class, X.class, Set.class, Y.class
     * @param <T>       返回类型
     */
    protected <T> T parseBody(String json,
                              Class<T> mainClass,
                              Class<?>... flatTypes) throws JsonProcessingException {

        final Class<?>[] types = (flatTypes == null) ? new Class<?>[0] : flatTypes;
        final AtomicInteger cursor = new AtomicInteger(0);
        final TypeFactory tf = objectMapper.getTypeFactory();

        Function<Class<?>, JavaType> build = new Function<Class<?>, JavaType>() {
            @Override
            public JavaType apply(Class<?> raw) {
                int paramCnt = raw.getTypeParameters().length;
                if (paramCnt == 0) {                       // 原始类型
                    return tf.constructType(raw);
                }

                JavaType[] args = new JavaType[paramCnt];
                for (int i = 0; i < paramCnt; i++) {
                    if (cursor.get() >= types.length) {
                        throw new IllegalArgumentException(
                                "flatTypes 数量不足，无法匹配 " + raw.getName());
                    }
                    Class<?> next = types[cursor.getAndIncrement()];
                    args[i] = this.apply(next);           // 递归解析
                }
                return tf.constructParametricType(raw, args);
            }
        };

        JavaType root = build.apply(mainClass);

        if (cursor.get() != types.length) {
            throw new IllegalArgumentException("flatTypes 数量多余，与泛型结构不符");
        }
        return objectMapper.readValue(json, root);
    }

    // ----------------- 签名方法 -----------------
    private static String genSign(Map<String, Object> args, String appid, String appkey, String version, long ts) {
        List<Map.Entry<String, String>> params = new ArrayList<>();
        Deque<Object> valueStack = new ArrayDeque<>();
        Deque<String> keyStack = new ArrayDeque<>();
        valueStack.push(args);
        keyStack.push("");
        while (!valueStack.isEmpty()) {
            Object current = valueStack.pop();
            String prefix = keyStack.pop();
            if (current instanceof Map) {
                new TreeMap<>((Map<String, Object>) current).entrySet()
                        .stream()
                        .filter(e -> e != null && e.getValue() != null) // 新增空值过滤
                        .forEach(e -> {
                            valueStack.push(e.getValue());
                            keyStack.push(prefix.isEmpty() ?
                                    e.getKey() :
                                    prefix + "[" + e.getKey() + "]");
                        });
            } else if (current instanceof List<?> list) {
                for (int i = list.size() - 1; i >= 0; i--) {
                    valueStack.push(list.get(i));
                    keyStack.push(prefix + "[" + i + "]");
                }
            } else {
                String value = current instanceof Boolean ?
                        ((Boolean) current ? "1" : "0") :
                        current.toString();
                if (!value.isEmpty()) {
                    params.add(new AbstractMap.SimpleEntry<>(prefix, value));
                }
            }
        }
        params.sort(Map.Entry.comparingByKey());
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.size(); i++) {
            Map.Entry<String, String> e = params.get(i);
            sb.append(encode(e.getKey())).append('=').append(encode(e.getValue()));
            if (i < params.size() - 1) sb.append('&');
        }
        sb.append("&appid=").append(encode(appid))
                .append("&appkey=").append(encode(appkey))
                .append("&v=").append(encode(version))
                .append("&ts=").append(ts);
        try {
            byte[] digest = MessageDigest.getInstance("MD5")
                    .digest(sb.toString().getBytes(StandardCharsets.UTF_8));
            StringBuilder md5 = new StringBuilder();
            for (byte b : digest) {
                md5.append(String.format("%02x", b));
            }
            return md5.toString();
        } catch (Exception e) {
            throw new RuntimeException("签名计算失败", e);
        }
    }

    private static String encode(String s) {
        return URLEncoder.encode(s, StandardCharsets.UTF_8);
    }

    protected boolean supports(String brand) {
        return wliftConfiguration.getConfigs().containsKey(brand);
    }
}
