package com.astenamic.new_discovery.external.client.takeout.service.core;


import com.astenamic.new_discovery.external.modal.dto.takeout.CommentDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetCommentParam;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;

import java.util.List;

public interface CommentService {

    /**
     * 获取评论列表
     *
     * @param brand 品牌
     * @param param 参数
     * @return 评论列表
     */
    List<CommentDTO> getCommentList(BrandEnum brand, GetCommentParam param);

    /**
     * 是否支持该品牌
     *
     * @param brand 品牌
     * @return 是否支持
     */
    default boolean supports(BrandEnum brand) {
        return List.of(BrandEnum.XFX).contains(brand);
    }


}
