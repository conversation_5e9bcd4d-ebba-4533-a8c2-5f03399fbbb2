package com.astenamic.new_discovery.external.client;

import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;


@Slf4j
public abstract class BaseClient<Q extends BaseRequest, R> {

    protected final Class<R> responseClass;

    protected final RestTemplate restTemplate;

    protected final ObjectMapper objectMapper;

    public BaseClient(Class<R> responseClass, RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.responseClass = responseClass;
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * 执行post请求
     */
    public R post(Q bizParams) {
        if (bizParams == null) {
            throw new IllegalArgumentException("bizParams 不能为空");
        }
        /* ---------- 1. 组装 URL ---------- */
        String url = buildUrl(bizParams.getUri());
        /* ---------- 2. 组装请求体 ---------- */
        HttpEntity<?> entity;
        try {
            entity = buildEntity(bizParams);
        } catch (Exception e) {
            throw new IllegalStateException("构建请求实体失败", e);
        }
        log.info("发起请求，url={}, 请求体={}", url, entity);
        /* ---------- 3. 发起请求 ---------- */
        ResponseEntity<String> respEntity = restTemplate.postForEntity(url, entity, String.class);
        String body = respEntity.getBody();
        log.info("响应结果，url={}, 状态码={}, 响应体={}", url, respEntity.getStatusCode(), body);
        if (body == null || body.isEmpty()) {
            throw new IllegalStateException("接口返回为空，url=" + url);
        }
        /* ---------- 4. 反序列化 ---------- */
        try {
            return deserialize(body, responseClass, bizParams.getResponseClass());
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("反序列化响应失败，响应体：" + body, e);
        }
    }

    /**
     * 获取客户端名称
     *
     * @return 客户端名称
     */
    public abstract String getClientName();

    /**
     * 构建请求URL
     *
     * @param uri 接口uri
     * @return 完整的请求URL
     */
    public abstract String buildUrl(String uri);

    /**
     * 构建请求实体
     *
     * @param bizParams 业务参数
     * @return HttpEntity 包装的请求体
     * @throws Exception 如果构建失败
     */
    public abstract HttpEntity<?> buildEntity(Q bizParams) throws Exception;

    /**
     * 反序列化 JSON 字符串为指定类型的对象
     *
     * @param json      JSON 字符串
     * @param mainClass 主类类型
     * @param flatTypes 扁平化的泛型类型
     * @return 反序列化后的对象
     * @throws JsonProcessingException 如果 JSON 解析失败
     */
    public R deserialize(String json, Class<R> mainClass, Class<?>... flatTypes) throws JsonProcessingException {
        final Class<?>[] types = (flatTypes == null) ? new Class<?>[0] : flatTypes;
        final AtomicInteger cursor = new AtomicInteger(0);
        final TypeFactory tf = objectMapper.getTypeFactory();
        Function<Class<?>, JavaType> build = new Function<Class<?>, JavaType>() {
            @Override
            public JavaType apply(Class<?> raw) {
                int paramCnt = raw.getTypeParameters().length;
                if (paramCnt == 0) {                       // 原始类型
                    return tf.constructType(raw);
                }
                JavaType[] args = new JavaType[paramCnt];
                for (int i = 0; i < paramCnt; i++) {
                    if (cursor.get() >= types.length) {
                        throw new IllegalArgumentException("flatTypes 数量不足，无法匹配 " + raw.getName());
                    }
                    Class<?> next = types[cursor.getAndIncrement()];
                    args[i] = this.apply(next);           // 递归解析
                }
                return tf.constructParametricType(raw, args);
            }
        };
        JavaType root = build.apply(mainClass);
        if (cursor.get() != types.length) {
            throw new IllegalArgumentException("flatTypes 数量多余，与泛型结构不符");
        }
        return objectMapper.readValue(json, root);
    }
}
