package com.astenamic.new_discovery.external.client.takeout.service.impl.eleme;

import com.astenamic.new_discovery.external.config.ElemeConfiguration;
import com.astenamic.new_discovery.external.modal.dto.takeout.CommentDTO;
import com.astenamic.new_discovery.external.modal.dto.takeout.GetCommentParam;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import com.astenamic.new_discovery.external.client.takeout.service.core.CommentService;
import eleme.openapi.sdk.api.entity.ugc.ORateInfo;
import eleme.openapi.sdk.api.entity.ugc.ORateQuery;
import eleme.openapi.sdk.api.entity.ugc.ORateResult;
import eleme.openapi.sdk.api.entity.ugc.OpenapiOrderRate;
import eleme.openapi.sdk.api.exception.ServiceException;
import eleme.openapi.sdk.api.service.UgcService;
import eleme.openapi.sdk.config.Config;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class ElemeCommentServiceImpl implements CommentService {

    private static final Logger logger = LoggerFactory.getLogger(ElemeCommentServiceImpl.class);

    private final ElemeConfiguration elemeConfiguration;

    private final ElemeOAuthService elemeOAuthService;

    @Override
    public List<CommentDTO> getCommentList(BrandEnum brand, GetCommentParam param) {
        if (param.getShopMapping() == null || StringUtils.isEmpty(param.getShopMapping().getElmId())) {
            logger.info("店铺映射关系不存在，brand={},param={}", brand, param);
            return List.of();
        }
        return getOrderRatesByShopIdV2(brand.getCode(), param).stream().map(c->CommentDTO.createByElm(brand.getCode(),c)).flatMap(List::stream).toList();
    }


    private List<ORateInfo> getOrderRatesByShopIdV2(String brand, GetCommentParam param) {
        try {

            Config config = elemeConfiguration.getConfig(brand);
            UgcService ugcService = new UgcService(config, elemeOAuthService.getToken(brand));

            List<ORateInfo> result = new ArrayList<>();
            int offset = 0;
            int size = 0;
            ORateQuery rateQuery = new ORateQuery();
            rateQuery.setShopId(Long.valueOf(param.getShopMapping().getElmId()));
            rateQuery.setStartTime(param.getStartTime().getElmDateTime());
            rateQuery.setEndTime(param.getEndTime().getElmDateTime());
            rateQuery.setPageSize(param.getPageSize());
            do {
                rateQuery.setOffset(offset);
                ORateResult oRateResult = null;
                try {
                    oRateResult = ugcService.getORateResult(rateQuery);
                } catch (ServiceException e) {
                    logger.error("请求失败，param={},code={},msg={}", param, e.getCode(), e.getMessage());
                    throw e;
                }
                if (oRateResult == null || oRateResult.getRateInfo() == null) {
                    logger.warn("oRateResult is null");
                    break;
                }
                result.addAll(oRateResult.getRateInfo());
                size = oRateResult.getRateInfo().size();
                offset += param.getPageSize();
            } while (size == param.getPageSize());

            return result;
        } catch (Exception e) {
            logger.error("未知异常", e);
        }
        throw new RuntimeException("获取店铺评价失败");
    }
}