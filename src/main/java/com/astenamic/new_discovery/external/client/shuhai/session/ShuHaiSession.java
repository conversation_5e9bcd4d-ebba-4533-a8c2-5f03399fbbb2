package com.astenamic.new_discovery.external.client.shuhai.session;

import com.astenamic.new_discovery.external.client.BaseClient;
import com.astenamic.new_discovery.external.config.ShuHaiConfiguration;
import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.response.ShuHaiResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;


@Slf4j
public abstract class ShuHaiSession<Q extends BaseRequest, T> extends BaseClient<Q, ShuHaiResponse<T>> {

    private final String appKey;
    private final String appSecret;
    private final String baseUrl;


    protected ShuHaiSession(ShuHaiConfiguration configuration,
                            RestTemplate restTemplate,
                            ObjectMapper objectMapper) {

        super((Class<ShuHaiResponse<T>>) (Class<?>) ShuHaiResponse.class, restTemplate, objectMapper);

        this.appKey = configuration.getAppKey();
        this.appSecret = configuration.getAppSecret();
        this.baseUrl = configuration.getBaseUrl();
    }


    @Override
    public String getClientName() {
        return "ShuHai";
    }

    @Override
    public String buildUrl(String uri) {
        return baseUrl;
    }

    @Override
    public HttpEntity<?> buildEntity(Q bizParams) throws JsonProcessingException {
        long ts = bizParams.getTimestamp();
        String method = bizParams.getUri();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("method", method);
        headers.set("apiKey", appKey);
        headers.set("timestamp", String.valueOf(ts));
        headers.set("signType", "MD5");
        headers.set("sign", buildSign(method, ts));
        String jsonBody = objectMapper.writeValueAsString(bizParams);
        return new HttpEntity<>(jsonBody, headers);
    }

    /* ---------------- 内部工具 ---------------- */

    /**
     * 计算文档要求的 MD5 大写签名
     */
    private String buildSign(String method, long ts) {
        Map<String, String> map = new TreeMap<>();
        map.put("apiKey",    appKey);
        map.put("method",    method);
        map.put("secret",    appSecret);        // secret 必须参与签名
        map.put("timestamp", String.valueOf(ts));

        // apiKey=xxx&method=xxx&secret=xxx&timestamp=xxx
        String signSource = map.entrySet()
                .stream()
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("&"));

        return DigestUtils.md5DigestAsHex(signSource.getBytes(StandardCharsets.UTF_8))
                .toUpperCase();
    }
}
