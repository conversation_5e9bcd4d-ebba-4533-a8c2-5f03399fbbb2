package com.astenamic.new_discovery.external.client.cool.vision.service;

import com.astenamic.new_discovery.external.client.cool.vision.CoolVisionSession;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolRecordDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.request.PatrolListRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionBizData;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionResponse;
import com.astenamic.new_discovery.util.TimeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PatrolListService extends CoolVisionSession<PatrolListRequest, CoolVisionPageDTO<PatrolRecordDTO>> {

    public PatrolListService(CoolConfiguration configuration,
                             RestTemplate restTemplate,
                             ObjectMapper objectMapper) {
        super(configuration, restTemplate, objectMapper);
    }


    public List<PatrolRecordDTO> getPatrolList(LocalDateTime targetTime) {
        long[] dayRange = TimeUtils.getDayRange(targetTime);
        int size = 0;
        int page = 1;

        List<PatrolRecordDTO> result = new ArrayList<>();

        PatrolListRequest request = new PatrolListRequest();
        request.setEnterpriseId(enterpriseId);
        request.setPageSize(100);
        request.setBeginTime(dayRange[0]);
        request.setEndTime(dayRange[1]);

        do {
            request.setPageNum(page);
            CoolVisionResponse<CoolVisionPageDTO<PatrolRecordDTO>> response = super.post(request);
            if (!response.isSuccess()) {
                break;
            }
            CoolVisionBizData<CoolVisionPageDTO<PatrolRecordDTO>> bizData = response.getBizData();
            CoolVisionPageDTO<PatrolRecordDTO> data = bizData.getData();
            List<PatrolRecordDTO> list = data.getList();
            size = list.size();
            result.addAll(list);
            page++;
        } while (size == 100);

        return result;
    }
}
