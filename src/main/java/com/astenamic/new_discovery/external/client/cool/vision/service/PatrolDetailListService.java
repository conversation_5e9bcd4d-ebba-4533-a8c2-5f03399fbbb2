package com.astenamic.new_discovery.external.client.cool.vision.service;

import com.astenamic.new_discovery.external.client.cool.vision.CoolVisionSession;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.PatrolDetailDTO;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.request.PatrolDetailListRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionBizData;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionResponse;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.response.CoolVisionPageDTO;
import com.astenamic.new_discovery.util.TimeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 查询基础详情列表服务
 */
@Slf4j
@Service
public class PatrolDetailListService extends CoolVisionSession<PatrolDetailListRequest, CoolVisionPageDTO<PatrolDetailDTO>> {

    public PatrolDetailListService(CoolConfiguration configuration,
                                   RestTemplate restTemplate,
                                   ObjectMapper objectMapper) {
        super(configuration, restTemplate, objectMapper);
    }

    /**
     * 获取指定记录和时间的巡店详情列表（先获取DEFINE，再获取STANDARD）
     *
     * @param recordId 巡店记录id
     * @param targetTime 目标时间
     * @return 巡店详情列表
     */
    public List<PatrolDetailDTO> getPatrolDetailList(Long recordId, LocalDateTime targetTime) {
        List<PatrolDetailDTO> result = new ArrayList<>();

        // 先获取DEFINE类型的数据
//        result.addAll(getPatrolDetailListByType(recordId, targetTime, "DEFINE"));

        // 再获取STANDARD类型的数据
        result.addAll(getPatrolDetailListByType(recordId, targetTime, "STANDARD"));

        return result;
    }

    /**
     * 根据类型获取巡店详情列表（内部方法）
     *
     * @param recordId 巡店记录id
     * @param targetTime 目标时间
     * @param tableType 检查表类型
     * @return 巡店详情列表
     */
    private List<PatrolDetailDTO> getPatrolDetailListByType(Long recordId, LocalDateTime targetTime, String tableType) {
        long[] dayRange = TimeUtils.getDayRange(targetTime);
        int size = 0;
        int page = 1;

        List<PatrolDetailDTO> result = new ArrayList<>();

        PatrolDetailListRequest request = new PatrolDetailListRequest();
        request.setEnterpriseId(enterpriseId);
        request.setPageSize(100);
        request.setBeginTime(dayRange[0]);
        request.setEndTime(dayRange[1]);
        request.setTableType(tableType);
        request.setRecordId(recordId);

        do {
            request.setPageNum(page);
            CoolVisionResponse<CoolVisionPageDTO<PatrolDetailDTO>> response = super.post(request);
            if (!response.isSuccess()) {
                break;
            }
            CoolVisionBizData<CoolVisionPageDTO<PatrolDetailDTO>> bizData = response.getBizData();
            CoolVisionPageDTO<PatrolDetailDTO> data = bizData.getData();
            List<PatrolDetailDTO> list = data.getList();
            size = list.size();
            result.addAll(list);
            page++;
        } while (size == 100);

        return result;
    }


}
