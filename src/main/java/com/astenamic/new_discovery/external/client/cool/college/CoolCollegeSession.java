package com.astenamic.new_discovery.external.client.cool.college;

import com.alipay.api.AlipayApiException;
import com.astenamic.new_discovery.external.client.BaseClient;
import com.astenamic.new_discovery.external.config.CoolConfiguration;
import com.astenamic.new_discovery.external.modal.dto.BaseRequest;
import com.astenamic.new_discovery.external.modal.dto.cool.college.response.CoolCollegeResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;


@Slf4j
public abstract class CoolCollegeSession <Q extends BaseRequest, T> extends BaseClient<Q, CoolCollegeResponse<T>> {

    private String baseUrl;

    private String apiKey;

    private String apiSecret;

    private String enterpriseId;

    protected CoolCollegeSession(CoolConfiguration configuration,
                                RestTemplate restTemplate,
                                ObjectMapper objectMapper) {
        super((Class<CoolCollegeResponse<T>>) (Class<?>) CoolCollegeResponse.class, restTemplate, objectMapper);

        this.baseUrl = configuration.getCollege().getBaseUrl();
        this.apiKey = configuration.getCollege().getApiKey();
        this.apiSecret = configuration.getCollege().getApiSecret();
        this.enterpriseId = configuration.getCollege().getEnterpriseId();
    }

    @Override
    public String getClientName() {
        return "coolCollege";
    }

    @Override
    public String buildUrl(String uri) {
        return UriComponentsBuilder.fromHttpUrl(baseUrl).path(uri).toUriString();
    }

    @Override
    public HttpEntity<?> buildEntity(Q bizParams) throws JsonProcessingException, AlipayApiException {
        long ts = bizParams.getTimestamp();

        Map<String, Object> params = new HashMap<>();
        params.put("o-access-token", bizParams);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("enterpriseId", this.enterpriseId);
        headers.set("timestamp", String.valueOf(ts));
        return new HttpEntity<>(objectMapper.writeValueAsString(params), headers);
    }
}
