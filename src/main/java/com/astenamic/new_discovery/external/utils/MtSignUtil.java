package com.astenamic.new_discovery.external.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Map;

@Slf4j
public class MtSignUtil {

    /**
     * 生成签名
     *
     * @param pathUrl        请求路径
     * @param params         请求参数
     * @param consumerSecret 消费者密钥
     * @return 签名
     */
    public static String genSig(String pathUrl, Map<String, String> params,
                                String consumerSecret) {
        String str = concatParams(params);
        str = pathUrl + "?" + str + consumerSecret;
        log.info("baseString:" + str);
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            str = byte2hex(md.digest(str.getBytes()));
        } catch (NoSuchAlgorithmException e) {
            log.error("NoSuchAlgorithmException:", e);
        }
        log.info("sig:{}, sigInParam:{}", str, params.get("sig"));
        return str;
    }

    public static String concatParams(Map<String, String> params2) {
        String[] key_arr = params2.keySet().toArray(new String[0]);
        Arrays.sort(key_arr);
        StringBuilder str = new StringBuilder();
        for (String key : key_arr) {
            if (key.equals("sig")) {
                continue;
            }
            String val = params2.get(key);
            str.append("&").append(key).append("=").append(val);
        }
        return str.toString().replaceFirst("&", "");
    }

    private static String byte2hex(byte[] b) {
        StringBuilder buf = new StringBuilder();
        int i;
        for (byte value : b) {
            i = value;
            if (i < 0)
                i += 256;
            if (i < 16)
                buf.append("0");
            buf.append(Integer.toHexString(i));
        }

        return buf.toString();
    }

}
