package com.astenamic.new_discovery.configuration.cors;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CoresConfigure {


    @Bean
    public FilterRegistrationBean<CorsFilter> corsFilter() {
        FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new CorsFilter());
        registrationBean.addUrlPatterns("/*"); // 设置过滤的 URL 模式
        registrationBean.setOrder(1); // 设置过滤器的顺序，数字越小优先级越高
        return registrationBean;
    }
}
