package com.astenamic.new_discovery.configuration.cors;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public class CorsFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 设置 CORS 响应头
        httpResponse.setHeader("Access-Control-Allow-Origin", "*"); // 允许所有来源
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS"); // 允许的方法
        httpResponse.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization"); // 允许的请求头
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true"); // 允许携带凭证

        // 处理 OPTIONS 请求（CORS 预检请求）
        if ("OPTIONS".equalsIgnoreCase(((HttpServletRequest) request).getMethod())) {
            return; // 如果是预检请求，直接返回
        }

        // 继续过滤链
        chain.doFilter(request, response);
    }
}
