package com.astenamic.new_discovery.configuration.rest;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

public class LoggingInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        logRequestDetails(request, body);
        return execution.execute(request, body);
    }

    private void logRequestDetails(HttpRequest request, byte[] body) throws IOException {
//        System.out.println("URI: " + request.getURI());
//        System.out.println("Method: " + request.getMethod());
//        System.out.println("Headers: " + request.getHeaders());
//        System.out.println("Body: " + new String(body, StandardCharsets.UTF_8));
    }
}
