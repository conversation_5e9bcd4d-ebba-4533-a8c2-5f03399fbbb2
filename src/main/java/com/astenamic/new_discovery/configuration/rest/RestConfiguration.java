package com.astenamic.new_discovery.configuration.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;

@Configuration
public class RestConfiguration {
    private final ObjectMapper objectMapper;

    public RestConfiguration(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .additionalMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper))
                .additionalInterceptors(Collections.singletonList(new LoggingInterceptor()))
                .setConnectTimeout(Duration.ofMillis(60*60*1000))  // 连接超时
                .setReadTimeout(Duration.ofMillis(60*60*1000))    // 读取超时
                .build();
    }

}
