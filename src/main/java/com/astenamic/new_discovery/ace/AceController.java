package com.astenamic.new_discovery.ace;

import com.astenamic.new_discovery.ace.scm.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ace")
@AllArgsConstructor
public class AceController {

    private UserService userService;


    @GetMapping("/user")
    public Object user(){
        return this.userService.getUserList(0,100);
    }




}
