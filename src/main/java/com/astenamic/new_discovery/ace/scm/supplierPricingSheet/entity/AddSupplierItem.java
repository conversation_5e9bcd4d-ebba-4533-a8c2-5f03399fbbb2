package com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AddSupplierItem {

    // 货品编号
    // *
    private String good_sno;

    // 定价
    // *
    private Float uprice;

    // 价格生效开始日期
    // *
    private LocalDateTime startdate;

    // 价格生效结束日期
    private LocalDateTime enddate;

    // 市场价格一
    private Float marketpriceone;

    // 市场价格二
    private Float marketpricetwo;

    // 供应商价格
    private Float supplier_price;

    // 调价原因
    private String reason;
}
