package com.astenamic.new_discovery.ace.scm.manualAdjustment.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@FormEntity("tableField_m4hufj8f")
public class ManualAdjustmentSon {

    // 菜品
    @FormField("associationFormField_m4hufj8g")
    private List<String> foodSysId;

    // 系统预测量
    @FormField("numberField_m4hufj8i")
    private Integer systemPredict;

    // 实际销量
    @FormField("numberField_m4j6vx4y")
    private Integer actualSales;

    // 手动调整量
    @FormField("textField_m4hufj8j")
    private String manualAdjustmentNum;

    // 分工备货量
    @FormField("numberField_m4hufj8k")
    private Integer divisionNum;

    // 是否需要调整
    @FormField("selectField_m56kjqpf")
    private String is_Adjustment;

    // 菜品名称
    @FormField("textField_m4hufj8h")
    private String foodName;

    public void setFoodSysId(String foodSysId) {
        if(foodSysId != null) {
            this.foodSysId = List.of(foodSysId);
        }
    }
}
