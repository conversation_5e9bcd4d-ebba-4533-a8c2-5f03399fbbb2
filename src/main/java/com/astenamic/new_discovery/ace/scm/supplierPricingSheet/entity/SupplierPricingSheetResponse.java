package com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity;

import com.astenamic.new_discovery.biz.report.pricing.entity.data_base.YiDaBase;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
//@FormEntity(value = "FORM-D7A1D72936244A6291CCFCDDBD696F49C8KI", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class SupplierPricingSheetResponse {

    // 总记录数
    private Integer total;

    // 货品定价单ID
    private Integer lgpid;

    // 定价单单号
    private String code;

    // 定价单的当前状态 : 0:待确认 1:未完成 -2:已完成 -1 已废弃 -6 反审核
    private String status;

    // 定价单状态-映射后内容
    private String substatus;

    // 供应商确认状态:0:起草 1:供应商待确认 2:供应商已确认
    private Integer quotedstatus;

    // 供应商确认状态名称
    private String quotedstatusname;

    // 制单人ID
    private Integer cuser_id;

    // 制单人姓名
    private String cuser_name;

    // 制单人编码
    private String cuser_sno;

    // 制单人手机号
    private String cuser_mobile;

    // 创建时间
    private LocalDateTime ctime;

    // 单据备注
    private String common;

    // 定价类型:0:按供应商门店货品定价、1:按货品门店供应商定价、3:按供应商定价、4:按货品定价
    private Integer type;

    // 定价类型-名称
    private String typename;

    // 询价市场一
    private String pricemarketone;

    // 询价市场二
    private String pricemarkettwo;

    // 更新时间
    private LocalDateTime utime;

    // 供应商ID(多个以逗号连接)
    private String supplier_id;

    // 供应商名称
    private String supplier_name;

    // 供应商编码
    private String supplier_sno;

    // 价格执行门店
    private String shop_id;

    // 价格执行门店名称
    private String shop_name;

    // 价格执行门店编码
    private String shop_sno;

    // 审核人ID
    private String auser_id;

    // 审核人姓名
    private String auser_name;

    // 审核人编码
    private String auser_sno;

    // 审核人手机号
    private String auser_mobile;

    // 审核时间
    private LocalDateTime atime;

    // 环比影响金额合计
    private Float mommoneysum;

    // 同比影响金额合计
    private Float yoymoneysum;

    // 下月采购金额预估合计
    private Float estmoneysum;

    // 审核意见
    private String auditcommons;

    // 定价单项
    private List<YiDaBase> items;

}
