package com.astenamic.new_discovery.ace.scm.ProductCategory.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
@FormEntity(value = "FORM-539EB1E8FCD14C24B743C6FFEDFB6EECH02Z", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class ProductCategory extends BaseEntity {

    // 货品分类ID
    @FormField("numberField_m56z6y73")
    private Integer id;

    // 货品分类名称
    @FormField("textField_m57oxjkt")
    private String name;

    // 父级货品分类ID
    @FormField("numberField_m56z6y74")
    private Integer pid;

    // 状态(0-无效1-有效)
    @FormField("textField_m4s28vzb")
    private String status;

    // 货品分类缩写
    @FormField("textField_m57oxjku")
    private String sno;

    // 顺序
    @FormField("numberField_m57oxjkv")
    private Integer seq;

    // 备注
    @FormField("textField_m57oxjkw")
    private String memo;

    // 区别是原料组还是类别(0：货品类别，1：原料组)
    @FormField("textField_m57oxjkx")
    private String typestyle;

    // 配出利润最大比例值(百分比)
    @FormField("textField_m57oxjky")
    private Float disdisprofit_maxratio;

    // 配出利润最小比例值(百分比)
    @FormField("textField_m57oxjkz")
    private Float disprofit_minratio;

    // 类型(1-增值系数 2-增值金额)
    @FormField("textField_m57oxjl0")
    private String disprice_calculate_mode;

    // 增值系数
    @FormField("textField_m57oxjl1")
    private Float added_code;

    // 增值金额
    @FormField("textField_m57oxjl2")
    private Float added_money;

    // 是否参与盘点
    @FormField("textField_m57oxjl3")
    private Integer is_invoice_check;

    // 是否自动耗料
    @FormField("textField_m57oxjl4")
    private Integer is_auto_depotuse;

}
