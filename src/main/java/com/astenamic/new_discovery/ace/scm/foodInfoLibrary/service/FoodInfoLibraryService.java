package com.astenamic.new_discovery.ace.scm.foodInfoLibrary.service;

import com.astenamic.new_discovery.ace.scm.foodInfoLibrary.entity.FoodInfoLibrary;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class FoodInfoLibraryService {
    private final YiDaSession yiDaSession;

    public List<FoodInfoLibrary> getFoodInfoLibraryByYiDa() {
        int page = 1;
        int size = 0;

        List<FoodInfoLibrary> all = new ArrayList<>();
        do {
            List<FoodInfoLibrary> od = this.yiDaSession.searchFormDataConditionsEmbedded(FoodInfoLibrary.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
