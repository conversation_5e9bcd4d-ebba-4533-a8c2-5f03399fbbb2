package com.astenamic.new_discovery.ace.scm.kitchen.service;

import com.astenamic.new_discovery.ace.scm.kitchen.entity.Maintenance;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class MaintenanceService {

    private final YiDaSession yiDaSession;

    public List<Maintenance> getMaintenancesByYiDa() {
        int page = 1;
        int size = 0;

        List<Maintenance> all = new ArrayList<>();
        do {
            List<Maintenance> od = this.yiDaSession.searchFormDataConditionsEmbedded(Maintenance.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
