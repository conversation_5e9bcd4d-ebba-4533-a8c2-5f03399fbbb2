package com.astenamic.new_discovery.ace.scm.suppliermanage.api;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.SupplierInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class SupplierSession extends AceSession {

    private static final String QUERY_SUPPLIER_INFO = "/rechain/purchase/api/supplier/getSupplierData/";

    public List<SupplierInfo> getSupplierList(Integer limit) {

        int start = 0;
        int size = 0;
        List<SupplierInfo> suppliers = new ArrayList<>();
        do {
            JSONObject params = new JSONObject();
            params.put("start", start);
            params.put("limit", limit);
            List<SupplierInfo> supplierList = this.getListRequest(QUERY_SUPPLIER_INFO, params, SupplierInfo.class);
            size = supplierList.size();
            start = start + limit;
            supplierList.forEach(supplier -> {
                supplier.setValid(supplier.getValid());
                supplier.setInvoicetype(supplier.getInvoicetype());
                supplier.setAccountdatetype(supplier.getAccountdatetype());
                supplier.setType(supplier.getType());
                supplier.setBselfpur(supplier.getBselfpur());
            });
            suppliers.addAll(supplierList);
        } while (size == limit);
        return suppliers;

    }


}
