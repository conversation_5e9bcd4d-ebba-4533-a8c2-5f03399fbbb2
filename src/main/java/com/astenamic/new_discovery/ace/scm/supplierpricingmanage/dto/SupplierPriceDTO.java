package com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商定价单读取接口 DTO（/rechain/purchase/api/price/getPriceList）
 */
@Data
public class SupplierPriceDTO {

    /**
     * 定价单唯一 ID
     */
    private String lgpid;

    /**
     * 定价单状态 -2=已完成
     */
    private String status;

    /**
     * 制单人 ID
     */
    private String cuid;

    /**
     * 制单时间
     */
    private LocalDateTime ctime;

    /**
     * 单据备注
     */
    private String common;

    /**
     * 定价类型 0=按供应商门店货品定价
     */
    private String type;

    /**
     * 询价市场一（原字段）
     */
    private String pricemarket1;

    /**
     * 询价市场二（原字段）
     */
    private String pricemarket2;

    /**
     * 最后更新时间
     */
    private LocalDateTime utime;

    /**
     * 定价单号
     */
    private String code;

    /**
     * 供应商 ID（主表专用）
     */
    private String lspid;

    /**
     * 商品大类 ID
     */
    private String goodShoptypes;

    /**
     * 门店 ID 列表（逗号分隔）
     */
    private String lsid;

    /**
     * 报价类型
     */
    private String quotetype;

    /**
     * 审核人姓名
     */
    private String rname;

    /**
     * 制单人工号
     */
    private String cuser_sno;

    /**
     * 制单人手机
     */
    private String cuser_mobile;

    /**
     * 供应商确认状态 2=供应商已确认
     */
    private String quotedstatus;

    /**
     * 外部单号
     */
    private String outcode;

    /**
     * 审核完成时间
     */
    private LocalDateTime atime;

    /**
     * 定价区域 ID
     */
    private String laids;

    /**
     * 是否更新主档 0=否
     */
    private String bupdatemain;

    /**
     * 更新主档时间
     */
    private String bupdatemaintime;

    /**
     * 定价流程 ID
     */
    private String lfpid;

    /**
     * 采购部门 ID
     */
    private String purchase_dept_id;

    /**
     * 是否展示描述
     */
    private String isshowdesc;

    /**
     * 附件 URL
     */
    private String enclosure;

    /**
     * 状态名称 已完成
     */
    private String substatus;

    /**
     * 附件名称
     */
    private String enclosurename;

    /**
     * 环比影响金额合计
     */
    private BigDecimal mommoneysum;

    /**
     * 同比影响金额合计
     */
    private BigDecimal yoymoneysum;

    /**
     * 下月采购金额预估合计
     */
    private BigDecimal estmoneysum;

    /**
     * 商品大类名称
     */
    private String goodShoptypesComment;

    /**
     * 审核人 ID
     */
    private String auser_id;

    /**
     * 审核人姓名
     */
    private String auser_name;

    /**
     * 审核人电话
     */
    private String audit_mobile;

    /**
     * 审核人工号
     */
    private String auser_sno;

    /**
     * 审核意见
     */
    private String auditcommons;

    /**
     * 供应商名称
     */
    private String suppliername;

    /**
     * 供应商编码
     */
    private String supplier_sno;

    /**
     * 定价区域名称
     */
    private String laidsComment;

    /**
     * 是否更新主档文本
     */
    private String bupdatemainComment;

    /**
     * 定价类型名称
     */
    private String typeName;

    /**
     * 门店名称列表
     */
    private String shopname;

    /**
     * 门店编码列表
     */
    private String shop_sno;

    /**
     * 供应商确认状态名称
     */
    private String quotedstatusname;

    /**
     * 制单人 ID
     */
    private String cuser_id;

    /**
     * 制单人姓名
     */
    private String cuser_name;

    /**
     * 询价市场一（驼峰版本）
     */
    private String pricemarketone;

    /**
     * 询价市场二（驼峰版本）
     */
    private String pricemarkettwo;

    /**
     * 门店 ID 列表
     */
    private String shop_id;

    /**
     * 门店名称列表
     */
    private String shop_name;

    /**
     * 供应商 ID
     */
    private String supplier_id;

    /**
     * 供应商名称
     */
    private String supplier_name;

    /**
     * 单据明细（items 数组）
     */
    private List<SupplierPriceItemDTO> items;

}
