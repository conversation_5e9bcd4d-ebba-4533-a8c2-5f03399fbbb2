package com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity;

import lombok.Data;

import java.util.List;

@Data
public class AddSupplierPricingSheet {
    // 定价类型(0:按供应商门店货品定价、1:按货品门店供应商定价、3:按供应商定价、4:按货品定价)
    // *
    private Integer type;

    // 自动审核标志(0-不审核，1-审核)
    // *
    private Integer auto_audit;

    // 制单人编号
    private String cuser_sno;

    // 审核人编号
    private String auser_sno;

    // 供应商编号
    // *
    private String supplier_sno;

    // 门店编号
    private String shop_sno;

    // 第三方单号
    private String third_party_code;

    // 询价市场一
    private String pricemarketone;

    // 询价市场二
    private String pricemarkettwo;

    // 通用字段
    private String common;

//    // 货品信息
//    // *
    private List<AddSupplierItem> items;
}
