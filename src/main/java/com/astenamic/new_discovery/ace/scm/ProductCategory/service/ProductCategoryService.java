package com.astenamic.new_discovery.ace.scm.ProductCategory.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.ProductCategory.entity.ProductCategory;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class ProductCategoryService extends AceSession {
    private final String resource = "/rechain/stock/api/good/getGoodTypeV1/";
    private YiDaSession yiDaSession;


    public List<ProductCategory> getProductCategory(Integer start, Integer limit){

        JSONObject params = new JSONObject();
        params.put("start",start);
        params.put("limit", limit);

        return this.getListRequest(this.resource, params, ProductCategory.class);
    }

    public void syncToYiDa(Integer start, Integer limit) {

        int page = 1;
        int size = 0;

        // 宜搭数据
        List<ProductCategory> oldData = new ArrayList<>();
        do{
            List<ProductCategory> od = this.yiDaSession.searchFormDataConditionsRequest(ProductCategory.class, null, page);
            page++;
            size = od.size();
            oldData.addAll(od);

        }while (size == 100);

        // 数据库数据，需要进行更新
        List<ProductCategory> newData = this.getProductCategory(start, limit);
        for (ProductCategory newDatum : newData) {
            // 转换为中文传入
            if(StringUtils.equals("1", newDatum.getStatus())){
                newDatum.setStatus("有效");
            } else if(StringUtils.equals("2", newDatum.getStatus())){
                newDatum.setStatus("无效");
            }

            if(StringUtils.equals("1", newDatum.getTypestyle())){
                newDatum.setTypestyle("货品类别");
            } else if ("2".equals(newDatum.getTypestyle())) {
                newDatum.setTypestyle("原料组");
            }

            if(StringUtils.equals("1", newDatum.getDisprice_calculate_mode())){
                newDatum.setDisprice_calculate_mode("增值系数");
            } else if ("2".equals(newDatum.getDisprice_calculate_mode())) {
                newDatum.setDisprice_calculate_mode("增值金额");
            }

        }

        // 把旧的object_id赋值给新的数据
        for (ProductCategory oldDatum : oldData) {
            for (ProductCategory newDatum : newData) {
                if(oldDatum.getId().equals(newDatum.getId())) {
                    newDatum.setObjectId(oldDatum.getObjectId());
                }
            }
        }

        Map<Integer, List<ProductCategory>> map = new HashMap<>();
        for (ProductCategory newDatum : newData) {
            if(newDatum.getObjectId() == null) {
                // 新数据
                map.put(1, newData);
            }else {
                // 旧数据
                map.put(2, newData);
            }
        }

        map.forEach((k, v) -> {
            List<List<ProductCategory>> groups = ArrayUtils.splitCollection(newData, 20);
            for (List<ProductCategory> group : groups) {
                if(k == 1){
                    this.yiDaSession.batchSave(group, ProductCategory.class);
                }else {
                    this.yiDaSession.batchUpdateDataByObjectId(group, ProductCategory.class);
                }
            }
        });
    }
}
