package com.astenamic.new_discovery.ace.scm.supplierPricingSheet.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity.AddSupplierPricingSheet;
import com.astenamic.new_discovery.ace.scm.supplierPricingSheet.entity.SupplierPricingSheetResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SupplierPricingSheetService extends AceSession {

    private final String resource = "/rechain/purchase/api/price/getPriceList";
    private final String addResource = "/rechain/purchase/api/price/put";

    public List<SupplierPricingSheetResponse> getSupplierPricingSheetList(Integer start, Integer limit, LocalDateTime targetTime) {

        targetTime = targetTime.withHour(0).withMinute(0).withSecond(0).withNano(0);

        LocalDateTime endTime = targetTime.plusDays(1).minusSeconds(1);

        List<SupplierPricingSheetResponse> all = new ArrayList<>();

        Integer size = 0;
        do{
            JSONObject params = new JSONObject();
            params.put("start",start);
            params.put("limit", limit);
            params.put("stime", targetTime);
            params.put("etime", endTime);
//            params.put("timetype", 2);

            List<SupplierPricingSheetResponse> data = this.getListRequest(this.resource, params, SupplierPricingSheetResponse.class);
            size = data.size();
            start = start + limit;
            all.addAll(data);

        }while (limit.equals(size));

        return all;
    }

    public String addSupplierPricingSheet(AddSupplierPricingSheet addSupplierPricingSheet){
        return this.postMethods(this.addResource, addSupplierPricingSheet);
    }

}
