package com.astenamic.new_discovery.ace.scm.manualAdjustment.service;

import com.astenamic.new_discovery.ace.scm.manualAdjustment.entity.ManualAdjustment;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class ManualAdjustmentService {
    private final YiDaSession yiDaSession;

    public List<ManualAdjustment> getManualAdjustmentByYiDa() {
        int page = 1;
        int size = 0;

        List<ManualAdjustment> all = new ArrayList<>();
        do {
            List<ManualAdjustment> od = this.yiDaSession.searchFormDataConditionsEmbedded(ManualAdjustment.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
