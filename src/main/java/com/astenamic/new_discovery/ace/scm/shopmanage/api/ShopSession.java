package com.astenamic.new_discovery.ace.scm.shopmanage.api;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ShopInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class ShopSession extends AceSession {

    private static final String QUERY_SHOP = "/rechain/api/pos/v1/basic/shop/getList";

    public List<ShopInfo> getShopList(Integer limit) {

        int start = 0;
        int size = 0;
        List<ShopInfo> shops = new ArrayList<>();
        do {
            JSONObject params = new JSONObject();
            params.put("start", start);
            params.put("limit", limit);
            List<ShopInfo> listRequest = this.getListRequest(QUERY_SHOP, params, ShopInfo.class);
            listRequest.forEach(item -> {
                item.setStatus(item.getStatus());
                item.setStype(item.getStype());
            });
            size = listRequest.size();
            start = start + limit;
            shops.addAll(listRequest);
        } while (size == limit);

        return shops;

    }

}
