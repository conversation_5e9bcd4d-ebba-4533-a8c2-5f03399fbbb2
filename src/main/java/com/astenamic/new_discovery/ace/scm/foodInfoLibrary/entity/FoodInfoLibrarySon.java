package com.astenamic.new_discovery.ace.scm.foodInfoLibrary.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

// 分工
@FormEntity("tableField_m49hvadu")
@Getter
@Setter
public class FoodInfoLibrarySon {

    // 分工事项
    @FormField("textField_m49hvadv")
    private String divisionWorks;

    // 是否合并
    @FormField("radioField_m5kr4wi8")
    private String is_merge;

    // 物料名称
    @FormField("selectField_m4m2szma")
    private String materialName;

    // 成本单位
    @FormField("textField_m4m2szm9")
    private String costUnit;

    // 理论单位用量
    @FormField("numberField_m4hs665k")
    private Integer theoryDosage;

    // 备货单位
    @FormField("selectField_m4qch2rz")
    private String stockUnit;

    // 时段1
    @FormField("numberField_m4i10dpg")
    private Double timeOne;

    // 时段2
    @FormField("numberField_m4i10dph")
    private Double timeTwo;

    // 分组
    @FormField("selectField_m49jrz8x")
    private String group;
}
