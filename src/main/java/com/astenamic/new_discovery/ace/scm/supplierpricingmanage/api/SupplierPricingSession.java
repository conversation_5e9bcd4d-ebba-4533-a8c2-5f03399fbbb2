package com.astenamic.new_discovery.ace.scm.supplierpricingmanage.api;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.PriceCreateRequestDTO;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.PriceCreateResponseDTO;
import com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto.SupplierPriceDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class SupplierPricingSession extends AceSession {

    private static final String QUERY_SUPPLIER_PRICE = "/rechain/purchase/api/price/getPriceList";

    private static final String ADD_SUPPLIER_PRICE = "/rechain/purchase/api/price/put";


    /**
     * 获取供应商定价单列表
     *
     * @param limit     每次请求的数量
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param status    状态
     * @return 供应商价格列表
     */
    public List<SupplierPriceDTO> getPriceList(Integer limit, LocalDate startDate, LocalDate endDate, Integer status) {
        log.info("获取供应商定价单列表开始, limit: {}, startDate: {}, endDate: {}, status: {}", limit, startDate, endDate, status);
        int start = 0;
        int size = 0;
        List<SupplierPriceDTO> result = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        do {
            JSONObject params = new JSONObject();
            params.put("limit", limit);
            if (startDate == null) {
                startDate = LocalDate.now();
            }
            if (endDate == null) {
                endDate = LocalDate.now();
            }
            params.put("stime", formatter.format(startDate));
            params.put("etime", formatter.format(endDate));
            if (status != null) {
                params.put("status", status);
            }
            params.put("start", start);
            List<SupplierPriceDTO> listRequest = this.getListRequest(QUERY_SUPPLIER_PRICE, params, SupplierPriceDTO.class);
            result.addAll(listRequest);
            size = listRequest.size();
            start = start + limit;
        } while (size == limit);

        log.info("获取供应商定价单列表结束, size: {}", result.size());
        return result;
    }

    /**
     * 添加供应商定价单
     *
     * @param request 请求参数
     * @return 供应商定价单 ID
     */
    public PriceCreateResponseDTO addSupplierPrice(PriceCreateRequestDTO request) {
        return this.postRequest(ADD_SUPPLIER_PRICE, request, PriceCreateResponseDTO.class);
    }

}
