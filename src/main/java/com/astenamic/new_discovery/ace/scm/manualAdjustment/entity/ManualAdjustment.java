package com.astenamic.new_discovery.ace.scm.manualAdjustment.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@FormEntity(value = "FORM-5D15875ADAAB40BFAA76E2F9BDE853229TGQ", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class ManualAdjustment extends BaseEntity {

    // 品牌
    @FormField("departmentSelectField_m4w3540o")
    private List<String> brandSysId;

    // 门店
    @FormField("departmentSelectField_m4vd1plx")
    private List<String> shopSysId;

    // 类型
    @FormField("radioField_m56kjqpe")
    private String type;

    // 预估数据日期
    @FormField("dateField_m4vhkry1")
    private LocalDateTime estimatedData;

    // 销售对比数据日期
    @FormField("dateField_m56kjqpd")
    private LocalDateTime salesComparisonData;

    // 字表
    @FormField("tableField_m4hufj8f")
    private List<ManualAdjustmentSon> foodInfos;

    public void setBrandSysId(String brandSysId) {
        if(brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if(shopSysId != null) {
            this.shopSysId = List.of(shopSysId);
        }
    }
}
