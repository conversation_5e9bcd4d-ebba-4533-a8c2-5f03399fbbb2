package com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 供应商定价单创建接口 (/rechain/purchase/api/price/put)
 * 对应文档参数说明：
 * <a href="http://openapi.acescm.cn/api/basic/goodprice.html#%E4%BE%9B%E5%BA%94%E5%95%86%E5%AE%9A%E4%BB%B7%E5%8D%95%E5%86%99%E5%85%A5%E6%8E%A5%E5%8F%A3">...</a>
 * 字段全部保持原始下划线命名，方便与 Fastjson2 序列化/反序列化直接映射。
 */
@Data
public class PriceCreateRequestDTO {

    /**
     * 定价类型：0=按供应商门店货品定价，1=按货品门店供应商定价，3=按供应商定价，4=按货品定价
     */
    private Integer type = 0;

    /**
     * 自动审核标志：0=不审核，1=审核
     */
    private Integer auto_audit = 0;

    /**
     * 制单人编号（可选，默认管理员 1）
     */
    private String cuser_sno = "72";

    /**
     * 审核人编号（可选，默认管理员 1）
     */
    private String auser_sno = "72";

    /**
     * 供应商编号，多个用逗号分隔（按货品定价时可为空）
     */
    private String supplier_sno;

    /**
     * 门店编号，多个用逗号分隔（按供应商定价时可为空）
     */
    private String shop_sno;

    /**
     * 第三方单号（客户系统定价单号）
     */
    private String third_party_code;

    /**
     * 询价市场一（≤100 字）
     */
    private String pricemarketone;

    /**
     * 询价市场二（≤100 字）
     */
    private String pricemarkettwo;

    /**
     * 通用备注字段（≤100 字）
     */
    private String common;

    /**
     * 货品明细数组，必填
     */
    private List<ItemDTO> items;

    // ===================== 内部静态类：货品明细 =====================
    @Data
    public static class ItemDTO {
        /**
         * 货品编号
         */
        private String good_sno;

        /**
         * 定价
         */
        private Float uprice;

        /**
         * 价格生效开始日期（YYYY-MM-DD）
         */
        private LocalDate startdate;

        /**
         * 价格生效结束日期（YYYY-MM-DD）
         */
        private LocalDate enddate;

        /**
         * 市场价格一
         */
        private Float marketpriceone;

        /**
         * 市场价格二
         */
        private Float marketpricetwo;

        /**
         * 供应商价格
         */
        private Float supplier_price;

        /**
         * 调价原因（≤250 字，可选）
         */
        private String reason;
    }
}