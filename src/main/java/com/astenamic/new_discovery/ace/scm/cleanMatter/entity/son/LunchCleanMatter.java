package com.astenamic.new_discovery.ace.scm.cleanMatter.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m67luysp")
public class LunchCleanMatter {

    // 时段
    @FormField("textField_m67luysq")
    private String timePart;

    // 工作
    @FormField("textField_m67luysr")
    private String workContent;

    // 事项
    @FormField("textField_m67luyss")
    private String matter;
}
