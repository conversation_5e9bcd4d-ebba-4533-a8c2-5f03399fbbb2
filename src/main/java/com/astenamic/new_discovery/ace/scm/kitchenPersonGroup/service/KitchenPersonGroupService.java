package com.astenamic.new_discovery.ace.scm.kitchenPersonGroup.service;

import com.astenamic.new_discovery.ace.scm.kitchenPersonGroup.entity.KitchenPersonGroup;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class KitchenPersonGroupService {
    private final YiDaSession yiDaSession;

    public List<KitchenPersonGroup> getKitchenPersonGroupByYiDa() {
        int page = 1;
        int size = 0;

        List<KitchenPersonGroup> all = new ArrayList<>();
        do {
            List<KitchenPersonGroup> od = this.yiDaSession.searchFormDataConditionsRequest(KitchenPersonGroup.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
