package com.astenamic.new_discovery.ace.scm.cleanMatter.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.ace.scm.cleanMatter.entity.son.LunchCleanMatter;
import com.astenamic.new_discovery.ace.scm.cleanMatter.entity.son.NightCleanMatter;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@FormEntity(value = "FORM-4A0151AF5CF74DEBB501CA345714D9FBJJS3", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class CleanMatter extends BaseEntity {

    // 分组
    @FormField("associationFormField_m67luyso")
    private String group;

    // 午市清洁事项
    @FormField("tableField_m67luysp")
    private List<LunchCleanMatter> lunchCleanMatter;

    // 晚市清洁事项
    @FormField("tableField_m67luysw")
    private List<NightCleanMatter> nightCleanMatter;
}
