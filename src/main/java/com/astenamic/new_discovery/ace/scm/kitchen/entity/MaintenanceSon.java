package com.astenamic.new_discovery.ace.scm.kitchen.entity;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@FormEntity("tableField_m56kqo97")
@Getter
@Setter
public class MaintenanceSon {

    // 分工事项
    @FormField("textField_m56kqo98")
    private String matter;

    // 是否合并
    @FormField("radioField_m5m6salv")
    private String is_merge;

}
