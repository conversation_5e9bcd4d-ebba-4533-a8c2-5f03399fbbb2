package com.astenamic.new_discovery.ace.scm.prd.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import jakarta.persistence.Column;
import lombok.Data;

@Data
@FormEntity(value = "FORM-63171A138522470084A0BBE1E5A7826DIXB6", appType = "APP_VAWCFBK8UUNBTJINOCWQ", sysToken = "8R7668D1U7WQNUKFEAEQ2A86FJV33W8WBSA4MLN")
public class Prd extends BaseEntity {
    // 主键
    @FormField("numberField_m56z0sqc")
    private Integer id;
    // 货品名称
    @FormField("textField_m56z0sqd")
    private String name;
    // 货品状态 1-有效 0-无效 2-冻结
    @FormField("textField_m56z0sqe")
    private String status;
    // 货品规格
    @FormField("textField_m56z0sqf")
    private String std;
    // 货品编码
    @FormField("textField_m56z0sqg")
    private String sno;
    // 货品最小规格
    @FormField("numberField_m56z0sqh")
    private Float min_std;
    // 货品最大规格
    @FormField("numberField_m56z0sqi")
    private Float max_std;
    // 货品小类ID
    @FormField("numberField_m56z0sqk")
    private Integer goodtype_id;
    // 	货品大类ID
    @FormField("numberField_m56z0sqj")
    private Integer fgoodtype_id;
    // 货品大类
    @FormField("textField_m56z0sql")
    private String fgoodtype;
    // 货品小类
    @FormField("textField_m56z0sqm")
    private String goodtype;
    // 生鲜/采集
    @FormField("selectField_m5buxpkb")
    private String supplierType;

}
