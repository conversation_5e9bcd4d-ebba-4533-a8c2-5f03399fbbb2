package com.astenamic.new_discovery.ace.scm.supplierpricingmanage.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

// ———————————————— 明细 DTO ————————————————
@Data
public class SupplierPriceItemDTO {
    /**
     * 明细行 ID
     */
    private String lgpiid;

    /**
     * 定价单 ID
     */
    private String lgpid;

    /**
     * 供应商 ID
     */
    private String lspid;

    /**
     * 货品 ID
     */
    private String lgid;

    /**
     * 门店 ID 列表
     */
    private String lsid;

    /**
     * 复制门店 ID
     */
    private String copylsid;

    /**
     * 下次定价
     */
    private BigDecimal uprice;

    /**
     * 询价一
     */
    private String marketprice1;

    /**
     * 询价二
     */
    private String marketprice2;

    /**
     * 供应商报价
     */
    private String suprice;

    /**
     * 去年同期平均价
     */
    private String lastyprice;

    /**
     * 上月采购数量
     */
    private String applyamount;

    /**
     * 价格生效日期
     */
    private LocalDate startdate;

    /**
     * 价格失效日期
     */
    private LocalDate enddate;

    /**
     * 调价原因
     */
    private String reason;

    /**
     * 明细最后更新时间
     */
    private LocalDateTime utime;

    /**
     * 上期平均价
     */
    private String lastprice;

    /**
     * 当前执行价格
     */
    private String nowprice;

    /**
     * 是否自定义
     */
    private String defined;

    /**
     * 采购订单金额
     */
    private BigDecimal purchaseorderamount;

    /**
     * 定价区域 ID
     */
    private String laids;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 价格周期
     */
    private String pricecircle;

    /**
     * 货品名称
     */
    private String lgname;

    /**
     * 货品编码
     */
    private String sno;

    /**
     * 货品品牌 (lgbrand)
     */
    private String lgbrand;

    /**
     * 规格
     */
    private String std;

    /**
     * 报价浮动警戒
     */
    private String pricewarning;

    /**
     * 货品别名
     */
    private String galias;

    /**
     * 定价类型
     */
    private String type;

    /**
     * 商品大类名称
     */
    private String lgtname;

    /**
     * 商品大类 ID
     */
    private String lgtid;

    /**
     * 订货单位名称
     */
    private String applyguname;

    /**
     * 创建人 ID
     */
    private String cuid;

    /**
     * 审核时间
     */
    private LocalDateTime atime;

    /**
     * 备注
     */
    private String common;

    /**
     * 是否更新主档
     */
    private String bupdatemain;

    /**
     * 定价单号
     */
    private String code;

    /**
     * 商品小类 ID
     */
    private String plgtid;

    /**
     * 发票类型
     */
    private String invoicetypetext;

    /**
     * 税率
     */
    private BigDecimal tax;

    /**
     * 扣率
     */
    private BigDecimal deductrate;

    /**
     * 供应商 ID（字符串）
     */
    private String lspidStr;

    /**
     * 定价属性 10=长期定价
     */
    private String goodPriceType;

    /**
     * 定价属性名称
     */
    private String goodPriceTypeName;

    /**
     * 未报价供应商
     */
    private String noPriceSups;

    /**
     * 商品一级大类名称
     */
    private String flgtname;

    /**
     * 制单人姓名
     */
    private String cname;

    /**
     * 供应商名称
     */
    private String suppliername;

    /**
     * 供应商负责人
     */
    private String suppliercharge;

    /**
     * 供应商负责人手机号
     */
    private String suppliermobile;

    /**
     * 供应商编码
     */
    private String suppliersno;

    /**
     * 门店编码列表
     */
    private String shopsno;

    /**
     * 门店名称列表
     */
    private String shop_name;

    /**
     * 询价调价比率
     */
    private String marketpriceratio;

    /**
     * 供应商报价调价比率
     */
    private String supriceratio;

    /**
     * 环比调价比率
     */
    private String momratio;

    /**
     * 同比调价比率
     */
    private String yoyratio;

    /**
     * 预计采购金额
     */
    private String estmoney;

    /**
     * 环比影响金额
     */
    private String mommoney;

    /**
     * 同比影响金额
     */
    private String yoymoney;

    /**
     * 门店编码列表
     */
    private String shop_sno;

    /**
     * 门店 ID 列表
     */
    private String shop_id;

    /**
     * 供应商名称
     */
    private String supplier_name;

    /**
     * 供应商编码
     */
    private String supplier_sno;

    /**
     * 询价市场一
     */
    private String marketpriceone;

    /**
     * 询价市场二
     */
    private String marketpricettwo;

    /**
     * 供应商报价
     */
    private String supplier_price;

    /**
     * 货品名称
     */
    private String good_name;

    /**
     * 货品编码
     */
    private String good_sno;

    /**
     * 货品品牌
     */
    private String good_brand;

    /**
     * 货品小类名称
     */
    private String goodtype_name;

    /**
     * 货品大类名称
     */
    private String fgoodtype_name;
}
