package com.astenamic.new_discovery.ace.scm.supplier.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
//@FormEntity("FORM-91297B051E664218913F01680ED0917CVME9")
@FormEntity("FORM-89EF3C6A55864DA2A08DB73D4548AE585MSF")
public class Supplier extends BaseEntity {
    // 供应链-供应商ID
    @FormField("numberField_m3pde3sx")
    private Integer lspid;
    // 客户供应商ID
    @FormField("numberField_m3pde3sz")
    private String clspid;
    // 供应商名称
    @FormField("textField_m3pcmxps")
    private String name;
    // 供货单位编号
    @FormField("textField_m3pcmxpu")
    private String sno;
    // 别名
    @FormField("textField_m3pcmxpw")
    private String oname;
    // 供应链-供应商类别ID
    @FormField("textField_m5m3pqa8")
    private Integer lsptid;
    // 客户-供应商类别的对接唯一标识
    @FormField("textField_m5m3pqaa")
    private Integer clsptid;
    // 供应商状态, 1有效，0无效
    @FormField("textField_m3pgd2km")
    private String valid;
    // 供应商地址
    @FormField("textField_m3pcmxq4")
    private String addr;
    // 供应商类型， 1外部供应商 2 内部供应商
    @FormField("textField_m3pgd2ko")
    private String type;
    // 供应商属性， 0 普通供应商 1 门店自购供应商 2 总部代采供应商
    @FormField("textField_m3pgd2kq")
    private String bselfpur;
    // 联系人手机
    @FormField("textField_m5m3ntrg")
    private String phoneNumber;
    // 结算方式(1 货到付款 2 预付 7 周付 10 十天结算 14 半月付 28 月付 60 双月付 3 季度付 6 半年付 12 年付 29 压批结算)
    @FormField("textField_m5m3jlfm")
    private String accountdatetype;
    // 发票方式(1.增值税专用发票；2.农副产品收购发票；3.农产品增值税普通发票；4.增值税普通发票；5.无发票；)
    @FormField("textField_m5m3jlfn")
    private String invoicetype;

}
