package com.astenamic.new_discovery.ace.scm.user.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService extends AceSession {
    private final String resource = "/rechain/purchase/api/user/getUserList";

    public List<JSONObject> getUserList(Integer start, Integer limit){

        JSONObject params = new JSONObject();
        params.put("uid", 1111);
        params.put("start",start);
        params.put("limit", limit);

        return this.getListRequest(this.resource, params, JSONObject.class);
    }
}
