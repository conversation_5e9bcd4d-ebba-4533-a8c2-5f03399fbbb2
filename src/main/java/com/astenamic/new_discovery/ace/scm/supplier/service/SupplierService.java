package com.astenamic.new_discovery.ace.scm.supplier.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.supplier.entity.Supplier;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SupplierService extends AceSession {
    private final String resource = "/rechain/purchase/api/supplier/getSupplierData/";
    private YiDaSession yiDaSession;

    public List<Supplier> getSupplierList(Integer start, Integer limit){
        Integer size = 0;
        List<Supplier> suppliers = new ArrayList<>();

        do{
            JSONObject params = new JSONObject();
            params.put("start",start);
            params.put("limit", limit);
            List<Supplier> supplierList = this.getListRequest(this.resource, params, Supplier.class);
            size = supplierList.size();
            start = start + limit;
            suppliers.addAll(supplierList);
        }while (size.equals(limit));

        return suppliers;
    }

    public void syncToYiDa(Integer start, Integer limit) {

        List<Supplier> newData = this.getSupplierList(start, limit);

        // 宜搭上的数据
        List<Supplier> supplierList = getSupplierList();

        for (Supplier supplier : newData) {
            supplier.setCtime(null);
            supplier.setUtime(null);
            for (Supplier old : supplierList) {
                if(supplier.getLspid().equals(old.getLspid())) {
                    supplier.setObjectId(old.getObjectId());
                    break;
                }
            }
        }

        List<Supplier> saveData = new ArrayList<>();
        List<Supplier> updateData = new ArrayList<>();
        for (Supplier supplier : newData) {
            if(StringUtils.equals(supplier.getType(), "1")){
                supplier.setType("外部供应商");
            }else if(StringUtils.equals(supplier.getType(), "2")){
                supplier.setType("内部供应商");
            }

            if(StringUtils.equals(supplier.getValid(), "1")){
                supplier.setValid("有效");
            }else if(StringUtils.equals(supplier.getValid(), "0")){
                supplier.setValid("无效");
            }

            if(StringUtils.equals(supplier.getBselfpur(), "0")){
                supplier.setBselfpur("普通供应商");
            }else if(StringUtils.equals(supplier.getBselfpur(), "1")){
                supplier.setBselfpur("门店自购供应商");
            }else if(StringUtils.equals(supplier.getBselfpur(), "2")){
                supplier.setBselfpur("总部代采供应商");
            }

            if(StringUtils.equals(supplier.getAccountdatetype(), "1")){
                supplier.setAccountdatetype("货到付款");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "2")){
                supplier.setAccountdatetype("预付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "7")){
                supplier.setAccountdatetype("周付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "10")){
                supplier.setAccountdatetype("十天结算");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "14")){
                supplier.setAccountdatetype("半月付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "28")){
                supplier.setAccountdatetype("月付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "60")){
                supplier.setAccountdatetype("双月付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "3")){
                supplier.setAccountdatetype("季度付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "6")){
                supplier.setAccountdatetype("半年付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "12")){
                supplier.setAccountdatetype("年付");
            }else if(StringUtils.equals(supplier.getAccountdatetype(), "29")){
                supplier.setAccountdatetype("压批结算");
            }

            if(StringUtils.equals(supplier.getInvoicetype(), "1")){
                supplier.setInvoicetype("增值税专用发票");
            }else if(StringUtils.equals(supplier.getInvoicetype(), "2")){
                supplier.setInvoicetype("农副产品收购发票");
            }else if(StringUtils.equals(supplier.getInvoicetype(), "3")){
                supplier.setInvoicetype("农产品增值税普通发票");
            }else if(StringUtils.equals(supplier.getInvoicetype(), "4")){
                supplier.setInvoicetype("增值税普通发票");
            }else if(StringUtils.equals(supplier.getInvoicetype(), "5")){
                supplier.setInvoicetype("无发票");
            }

            if(supplier.getObjectId() == null) {
                saveData.add(supplier);
            }else {
                supplier.setPhoneNumber("测试");
                updateData.add(supplier);
            }
        }

        if(!saveData.isEmpty()){
            List<List<Supplier>> saveGroups = ArrayUtils.splitCollection(saveData, 50);
            for (List<Supplier> group : saveGroups) {
                this.yiDaSession.batchSave(group, Supplier.class);
            }
        }

        if(!updateData.isEmpty()){
            List<List<Supplier>> updateGroups = ArrayUtils.splitCollection(updateData, 10);
            for (List<Supplier> group : updateGroups) {
                this.yiDaSession.batchUpdateDataByObjectId(group, Supplier.class);
            }
        }

    }

    // 从宜搭获取数据
    public List<Supplier> getSupplierList(){
        List<Supplier> data = new ArrayList<>();
        int size = 0;
        int page = 1;

        do {
            List<Supplier> suppliers = this.yiDaSession.searchFormDataConditionsRequest(Supplier.class, null, page);
            size = suppliers.size();
            page++;
            data.addAll(suppliers);
        }while(size == 100);

        return data;
    }

}
