package com.astenamic.new_discovery.ace.scm.shop.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.shop.entity.Shop;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@AllArgsConstructor
public class ShopService extends AceSession {
    private final String resource = "/rechain/api/pos/v1/basic/shop/getList";
    private YiDaSession yiDaSession;
    public List<Shop> getShopList(Integer start, Integer limit) {

        Integer size = 0;

        List<Shop> all = new ArrayList<>();
        do {
            JSONObject params = new JSONObject();
            params.put("start", start);
            params.put("limit", limit);
            List<Shop> list = this.getListRequest(this.resource, params, Shop.class);
            size = list.size();
            start = start + size;
            all.addAll(list);
        }while (size.equals(limit));

        return all;
    }

    public void syncToYiDa(Integer start, Integer limit) {

        List<Shop> oldData = getShopList();

        List<Shop> shops = this.getShopList(start, limit);

        List<Shop> save = new ArrayList<>();
        List<Shop> update = new ArrayList<>();

        for (Shop shop : shops) {
            for (Shop shop1 : oldData) {
                if(StringUtils.equals(shop1.getId().toString(), shop.getId().toString())){
                    shop.setObjectId(shop1.getObjectId());
                }
            }
        }

        for (Shop shop : shops) {
            if(shop.getObjectId() == null){
                save.add(shop);
            }else {
                update.add(shop);
            }
        }

        List<List<Shop>> saveGroups = this.splitCollection(save, 100);
        for (List<Shop> group : saveGroups) {
            this.yiDaSession.batchSave(group, Shop.class);
        }

        List<List<Shop>> updateGroups = this.splitCollection(update, 100);
        for (List<Shop> group : updateGroups) {
            this.yiDaSession.batchSave(group, Shop.class);
        }

    }

    public List<Shop> getShopList() {
        List<Shop> data = new ArrayList<>();

        int size = 0;
        int page = 1;

        do {
            List<Shop> shops = this.yiDaSession.searchFormDataConditionsRequest(Shop.class, null, page);
            size = shops.size();
            page++;
            data.addAll(shops);
        }while (size == 100);

        return data;
    }

    private <T> List<List<T>> splitCollection(Collection<T> collection, int size) {
        List<List<T>> result = new ArrayList<>();
        List<T> currentList = new ArrayList<>(size);

        for (T element : collection) {
            currentList.add(element);
            if (currentList.size() == size) {
                result.add(new ArrayList<>(currentList));
                currentList.clear();
            }
        }

        // 如果最后的子集合不为空，将其加入结果列表
        if (!currentList.isEmpty()) {
            result.add(new ArrayList<>(currentList));
        }

        return result;
    }
}
