package com.astenamic.new_discovery.ace.scm.cleanMatter.service;

import com.astenamic.new_discovery.ace.scm.cleanMatter.entity.CleanMatter;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class CleanMatterService {

    private final YiDaSession yiDaSession;

    public List<CleanMatter> getFoodInfoLibraryByYiDa() {
        int page = 1;
        int size = 0;

        List<CleanMatter> all = new ArrayList<>();
        do {
            List<CleanMatter> od = this.yiDaSession.searchFormDataConditionsEmbedded(CleanMatter.class, null, page);
            page++;
            size = od.size();
            all.addAll(od);
        }while (size == 1);

        return all;
    }
}
