package com.astenamic.new_discovery.ace.scm.prd.service;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.ace.scm.ProductCategory.entity.ProductCategory;
import com.astenamic.new_discovery.ace.scm.prd.entity.Prd;
import com.astenamic.new_discovery.biz.report.shop.utils.ArrayUtils;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class PrdService extends AceSession {
    private final String resource = "/rechain/stock/api/good/getListV1";
    private YiDaSession yiDaSession;

    public List<Prd> getPrdList(Integer start, Integer limit) {
        return this.getPrdList(start, limit, null, null, null, null, null);
    }

    public void syncToYiDa(Integer start, Integer limit) {
        // 货品基础信息数据（有类别ID但是无类别）
        List<Prd> newData = this.getPrdList(start, limit);

        int page = 1;
        int size = 0;

        // 从宜搭获取数据
        List<Prd> oldData = new ArrayList<>();
        do {
            List<Prd> od = this.yiDaSession.searchFormDataConditionsRequest(Prd.class, null, page);
            size = od.size();
            page++;
            oldData.addAll(od);
        }while(size == 100);

        // 把旧数据的objectId赋值给新数据
        if (oldData != null && !oldData.isEmpty()){
            for (Prd newDatum : newData) {
                for (Prd oldDatum : oldData) {
                    if(newDatum.getId().equals(oldDatum.getId())) {
                        newDatum.setObjectId(oldDatum.getObjectId());
                    }
                }
            }
        }

        // 从宜搭获取所有的分类
        int productCategoryPage = 1;
        int productCategorySize = 0;
        List<ProductCategory> productCategoryList = new ArrayList<>();
        do {
            List<ProductCategory> od = this.yiDaSession.searchFormDataConditionsRequest(ProductCategory.class, null, productCategoryPage);
            productCategorySize = od.size();
            productCategoryPage++;
            productCategoryList.addAll(od);
        }while(productCategorySize == 100);

        for (Prd newDatum : newData) {
            // 修改状态显示为中文名称
            if(StringUtils.equals("1", newDatum.getStatus())) {
                newDatum.setStatus("原材料");
            }else if(StringUtils.equals("2", newDatum.getStatus())){
                newDatum.setStatus("半成品");
            }else if(StringUtils.equals("3", newDatum.getStatus())){
                newDatum.setStatus("粗加工");
            }else {
                throw new RuntimeException("type值错误");
            }

            //通过分类ID匹配分类名称
            for (ProductCategory productCategory : productCategoryList) {
                if(newDatum.getFgoodtype_id().equals(productCategory.getId())) {
                    newDatum.setFgoodtype(productCategory.getName());
                }
                if(newDatum.getGoodtype_id().equals(productCategory.getId())) {
                    newDatum.setGoodtype(productCategory.getName());
                }
            }
        }

        Map<Integer, List<Prd>> map = new HashMap<>();
        List<Prd> saveData = new ArrayList<>();
        List<Prd> updateData = new ArrayList<>();
        for (Prd newDatum : newData) {
            if(newDatum.getObjectId() == null) {
                // 新数据
                saveData.add(newDatum);
            }else {
                // 旧数据
                updateData.add(newDatum);
            }
        }
        map.put(1, saveData);
        map.put(2, updateData);

        map.forEach((k, v) -> {
            List<List<Prd>> groups = ArrayUtils.splitCollection(v, 100);
            for (List<Prd> group : groups) {
                if(k == 1){
                    this.yiDaSession.batchSave(group, Prd.class);
                }else {
                    this.yiDaSession.batchUpdateDataByObjectId(group, Prd.class);
                }
            }
        });
    }

    public List<Prd> getPrdList(Integer start, Integer limit, Integer status) {
        return this.getPrdList(start, limit, status, null, null, null, null);
    }

    public List<Prd> getPrdList(Integer start, Integer limit, Integer status, String type) {
        return this.getPrdList(start, limit, status, type, null, null, null);
    }

    public List<Prd> getPrdList(Integer start, Integer limit, Integer status, String type, Integer lfsid) {
        return this.getPrdList(start, limit, status, type, lfsid, null, null);
    }

    public List<Prd> getPrdList(Integer start, Integer limit, Integer status, String type, Integer lfsid, Integer lgtid) {
        return this.getPrdList(start, limit, status, type, lfsid, lgtid, null);
    }

    public List<Prd> getPrdList(Integer start, Integer limit, Integer status, String type, Integer lfsid, Integer lgtid, String text) {

        Integer size = 0;
        List<Prd> list = new ArrayList<>();

        do{
            JSONObject params = new JSONObject();
            params.put("start", start);
            params.put("limit", limit);

            if (status != null) {
                params.put("status", status);
            }

            if (type != null) {
                params.put("type", type);
            }

            if (lfsid != null) {
                params.put("lfsid", lfsid);
            }

            if (lgtid != null) {
                params.put("lgtid", lgtid);
            }

            if (text != null) {
                params.put("text", text);
            }
            List<Prd> prds = this.getListRequest(this.resource, params, Prd.class);
            size = prds.size();
            start = start + limit;
            list.addAll(prds);
        }while(size.equals(limit));

        return list;
    }
}
