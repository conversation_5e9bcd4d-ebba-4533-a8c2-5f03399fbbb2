package com.astenamic.new_discovery.ace.scm.material.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Material extends BaseEntity {

    private LocalDateTime baseDate;
    private String materialId;
    private String materialName;
    private String materialNo;
    private String materialTypeBig;
    private String materialTypeSmall;
    private String shopId;
    private String shopName;
    private String shopNo;
    private String unit;
}
