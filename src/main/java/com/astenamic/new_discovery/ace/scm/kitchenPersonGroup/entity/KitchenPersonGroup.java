package com.astenamic.new_discovery.ace.scm.kitchenPersonGroup.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@FormEntity(value = "FORM-72C8B25FACE24D47A0DC3E78D3EBC76C5XGJ", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class KitchenPersonGroup extends BaseEntity {

    // 品牌
    @FormField("departmentSelectField_m4w3xgjb")
    private List<String> brandSysId;

    // 分组名称
    @FormField("textField_m49hpbte")
    private String groupName;

    public void setBrandSysId(String brandSysId) {
        if(brandSysId != null){
            this.brandSysId = List.of(brandSysId);
        }
    }
}
