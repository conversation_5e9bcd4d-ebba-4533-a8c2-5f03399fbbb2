package com.astenamic.new_discovery.ace.scm.foodInfoLibrary.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@FormEntity(value = "FORM-A7A028FE309244C7B8D88F019FB0B5EFCX49", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
public class FoodInfoLibrary extends BaseEntity {

    // 品牌
    @FormField("departmentSelectField_m4w3yc8k")
    private List<String> brandSysId;

    // 门店
    @FormField("departmentSelectField_m4vd3086")
    private List<String> shopSysId;

    // 菜品名称
    @FormField("selectField_m52gua0n")
    private String foodName;

    // 类型
    @FormField("radioField_m56kf81y")
    private String foodType;

    // 子表单
    @FormField("tableField_m49hvadu")
    private List<FoodInfoLibrarySon> divisions;

    // 厨师长
    @FormField("employeeField_m4w31uzi")
    private List<String> chiefSysId;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null){
            this.brandSysId = List.of(brandSysId);
        }
    }

    public void setShopSysId(String shopSysId) {
        if (shopSysId != null){
            this.shopSysId = List.of(shopSysId);
        }
    }

    public void setChiefSysId(String chiefSysId) {
        if (chiefSysId != null){
            this.chiefSysId = List.of(chiefSysId);
        }
    }
}
