package com.astenamic.new_discovery.ace.scm.productmanage.api;

import com.alibaba.fastjson2.JSONObject;
import com.astenamic.new_discovery.ace.api.session.AceSession;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductCategory;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductInfo;
import com.astenamic.new_discovery.biz.purchase.domain.support.entity.ProductUnit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class ProductSession extends AceSession {

    private static final String QUERY_PRODUCT_UNIT = "/rechain/stock/api/good/getGoodUnit/";

    private static final String QUERY_PRODUCT_INFO = "/rechain/stock/api/good/getListV1";

    private static final String QUERY_PRODUCT_CATEGORY = "/rechain/stock/api/good/getGoodTypeV1/";

    public List<ProductUnit> getUnitList() {

        int start = 0;
        int limit = 25;
        int size = 0;
        List<ProductUnit> unitList = new ArrayList<>();
        do {
            JSONObject params = new JSONObject();
            params.put("start", start);
            params.put("limit", limit);
            List<ProductUnit> supplierList = this.getListRequest(QUERY_PRODUCT_UNIT, params, ProductUnit.class);
            supplierList.forEach(unit -> {
                unit.setIfbalance(unit.getIfbalance());
            });
            size = supplierList.size();
            start = start + limit;
            unitList.addAll(supplierList);
        } while (size == limit);

        return unitList;
    }


    public List<ProductInfo> getProductList(Integer limit) {
        log.info("获取货品列表开始, limit: {}", limit);
        int start = 0;
        int size = 0;
        List<ProductInfo> result = new ArrayList<>();

        do {
            JSONObject params = new JSONObject();
            params.put("limit", limit);
            params.put("start", start);
            List<ProductInfo> listRequest = this.getListRequest(QUERY_PRODUCT_INFO, params, ProductInfo.class);
            listRequest.forEach(item -> {
                item.setStatus(item.getStatus());
                item.setType(item.getType());
                item.setLogisticstype(item.getLogisticstype());
            });
            result.addAll(listRequest);
            size = listRequest.size();
            start = start + limit;
        } while (size == limit);

        log.info("获取货品列表结束, size: {}", result.size());
        return result;

    }

    public List<ProductCategory> getProductCategoryList(Integer limit){
        log.info("获取货品分类列表开始, limit: {}", limit);
        int start = 0;
        int size = 0;
        List<ProductCategory> result = new ArrayList<>();

        do {
            JSONObject params = new JSONObject();
            params.put("limit", limit);
            params.put("start", start);
            List<ProductCategory> listRequest = this.getListRequest(QUERY_PRODUCT_CATEGORY, params, ProductCategory.class);
            result.addAll(listRequest);
            size = listRequest.size();
            start = start + limit;
        } while (size == limit);

        log.info("获取货品分类列表开始, size: {}", result.size());
        return result;
    }

}
