package com.astenamic.new_discovery.ace.scm.cleanMatter.entity.son;

import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@FormEntity("tableField_m67luysw")
public class NightCleanMatter {
    // 时段
    @FormField("textField_m67luyst")
    private String timePart;

    // 工作
    @FormField("textField_m67luysu")
    private String workContent;

    // 事项
    @FormField("textField_m67luysv")
    private String matter;
}
