package com.astenamic.new_discovery.ace.scm.kitchen.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@FormEntity(value = "FORM-F79AA99DDD6A4DC28AFBCA11E5C96375G6GP", appType = "APP_JBVWLE7KF67XN1G8H5M4", sysToken = "LIC66BB188VQGRW869LBXBQ7J42R2UN5VA94M03")
@Setter
@Getter
public class Maintenance extends BaseEntity {

    // 品牌
    @FormField("departmentSelectField_m56kqo95")
    private List<String> brandSysId;

    // 菜品名称
    @FormField("selectField_m56kqo96")
    private String foodName;

    // 字表
    @FormField("tableField_m56kqo97")
    private List<MaintenanceSon> divisionInfo;

    public void setBrandSysId(String brandSysId) {
        if (brandSysId != null) {
            this.brandSysId = List.of(brandSysId);
        }
    }
}
