package com.astenamic.new_discovery.ace.scm.shop.entity;

import com.astenamic.new_discovery.ace.scm.base.entity.BaseEntity;
import com.astenamic.new_discovery.form.annotation.FormEntity;
import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

@Data
@FormEntity("FORM-92BB3D5E14A3446F954326B66F41F6FBBMSB")
public class Shop extends BaseEntity {
    //  唯一识别号
    @FormField("textField_m60bm48k")
    private Long id;
    // 组织中心id
    @FormField("textField_m3pc9gti")
    private String uuid;
    // 对接方id/唯一标识
    @FormField("textField_m3pc9gtk")
    private String csid;
    // 门店名称
    @FormField("textField_m3pc9gtm")
    private String name;
    // 门店编码
    @FormField("textField_m3pc9gto")
    private String sno;
    // 门店地址
    @FormField("textField_m3pntt32")
    private String shopadd;
    // 门店电话
    @FormField("textField_m3pc9gtq")
    private String shoptel;
    // 门店类型 --- 3-配送中心 4-直营店 5-加盟店 6-外销客户
    @FormField("textField_m3pfcg4t")
    private Integer stype;
    // 状态 --- 	1-有效 非1-无效
    @FormField("textField_m3pfcg4v")
    private Integer status;



}
