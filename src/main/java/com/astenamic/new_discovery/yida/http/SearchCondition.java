package com.astenamic.new_discovery.yida.http;

import com.astenamic.new_discovery.form.annotation.FormField;
import lombok.Data;

import java.beans.Introspector;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;

@Data
public class SearchCondition {
    private String key;
    private Object value;
    private String type;
    private String operator;
    private String componentName;

    public SearchCondition(String key, Object value, String type, String operator, String componentName) {
        this.key = key;
        this.value = value;
        this.type = type;
        this.operator = operator;
        this.componentName = componentName;
    }

    public static SearchConditionBuilder builder() {
        return new SearchConditionBuilder();
    }

    @Data
    public static class SearchConditionBuilder {
        private List<SearchCondition> conditions;
        private Map<String, String> orderMap;

        public SearchConditionBuilder() {
            this.conditions = new ArrayList<>();
            this.orderMap = new HashMap<>();
        }

        public SearchConditions get() {
            return new SearchConditions(this.conditions, this.orderMap);
        }

        public SearchConditionBuilder textEq(String key, String value, String sort) {
            SearchCondition condition = new SearchCondition(key, value, "TEXT", "eq", "TextField");
            this.conditions.add(condition);
            this.orderMap.put(key, sort);
            return this;
        }

        public <T, R> SearchConditionBuilder textEq(Class<T> clazz, SFunction<T, R> getter, String value, String sort) {
            LambdaUtils.getFormFieldValue(clazz, getter).ifPresentOrElse(key -> {
                SearchCondition condition = new SearchCondition(key, value, "TEXT", "eq", "TextField");
                this.conditions.add(condition);
                this.orderMap.put(key, sort);
            }, () -> {
                throw new RuntimeException("获取表单字段失败");
            });
            return this;
        }

        public SearchConditionBuilder textLike(String key, String value, String sort) {
            SearchCondition condition = new SearchCondition(key, value, "TEXT", "like", "TextField");
            this.conditions.add(condition);
            this.orderMap.put(key, sort);
            return this;
        }

        public <T, R> SearchConditionBuilder textLike(Class<T> clazz, SFunction<T, R> getter, String value, String sort) {
            LambdaUtils.getFormFieldValue(clazz, getter).ifPresentOrElse(key -> {
                SearchCondition condition = new SearchCondition(key, value, "TEXT", "like", "TextField");
                this.conditions.add(condition);
                this.orderMap.put(key, sort);
            }, () -> {
                throw new RuntimeException("获取表单字段失败");
            });
            return this;
        }

        public SearchConditionBuilder textIn(String key, List<String> value, String sort) {
            SearchCondition condition = new SearchCondition(key, value, "TEXT", "in", "TextField");
            this.conditions.add(condition);
            this.orderMap.put(key, sort);
            return this;
        }

        public SearchConditionBuilder dateBetween(String key, long[] value, String sort) {
            SearchCondition condition = new SearchCondition(key, value, "DOUBLE", "between", "DateField");
            this.conditions.add(condition);
            this.orderMap.put(key, sort);
            return this;
        }

        public <T, R> SearchConditionBuilder dateBetween(Class<T> clazz, SFunction<T, R> getter, long[] value, String sort) {
            LambdaUtils.getFormFieldValue(clazz, getter).ifPresentOrElse(key -> {
                SearchCondition condition = new SearchCondition(key, value, "DOUBLE", "between", "DateField");
                this.conditions.add(condition);
                this.orderMap.put(key, sort);
            }, () -> {
                throw new RuntimeException("获取表单字段失败");
            });
            return this;
        }

        public SearchConditionBuilder dateLe(String key, long value, String sort) {
            SearchCondition condition = new SearchCondition(key, value, "DOUBLE", "le", "DateField");
            this.conditions.add(condition);
            this.orderMap.put(key, sort);
            return this;
        }
    }


    @FunctionalInterface
    public interface SFunction<T, R> extends Function<T, R>, Serializable {
    }

    public static class LambdaUtils {

        public static SerializedLambda resolveLambda(Serializable lambda) {
            try {
                Method method = lambda.getClass().getDeclaredMethod("writeReplace");
                method.setAccessible(true);
                return (SerializedLambda) method.invoke(lambda);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }


        public static String methodToField(String methodName) {
            if (methodName.startsWith("get")) {
                return Introspector.decapitalize(methodName.substring(3));
            }
            throw new IllegalArgumentException("只支持getter方法");
        }


        public static <T, R> Optional<String> getFormFieldValue(Class<T> clazz, SFunction<T, R> getter) {
            SerializedLambda lambda = resolveLambda(getter);
            String fieldName = methodToField(lambda.getImplMethodName());
            try {
                Field field = clazz.getDeclaredField(fieldName);
                FormField annotation = field.getAnnotation(FormField.class);
                return annotation != null ? Optional.of(annotation.value()) : Optional.empty();
            } catch (Exception e) {
                return Optional.empty();
            }
        }
    }

}
