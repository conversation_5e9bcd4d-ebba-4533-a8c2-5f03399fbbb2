package com.astenamic.new_discovery.yida.http;

import com.alibaba.fastjson2.JSON;

import java.util.List;
import java.util.Map;

public class SearchConditions {
    private final List<SearchCondition> conditions;

    private final Map<String, String> orderMap;

    public SearchConditions(List<SearchCondition> conditions, Map<String, String> orderMap) {
        this.conditions = conditions;
        this.orderMap = orderMap;
    }

    public String getConditionsJson() {
        return JSON.toJSONString(this.conditions);
    }

    public String getOrderMapJson() {
        return JSON.toJSONString(this.orderMap);
    }
}
