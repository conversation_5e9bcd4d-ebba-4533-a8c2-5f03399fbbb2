package com.astenamic.new_discovery.yida.http;

import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.astenamic.new_discovery.form.FormDefinition;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class RequestBuilder {
    private final String userId;
    private final String appType;
    private final String sysToken;
    private final String appKey;
    private final String appSecret;

    public RequestBuilder(String userId, String appType, String sysToken, String appKey, String appSecret) {
        this.userId = userId;
        this.appType = appType;
        this.sysToken = sysToken;
        this.appKey = appKey;
        this.appSecret = appSecret;
    }

    public BatchUpdateFormDataByInstanceMapRequest batchUpdateRequest(FormDefinition fd) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;

        return new BatchUpdateFormDataByInstanceMapRequest()
                .setNoExecuteExpression(true)
                .setAsynchronousExecution(false)
                .setIgnoreEmpty(true)
                .setUseLatestFormSchemaVersion(true)
                .setFormUuid(formCode)
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId);
    }

    public SearchFormDataSecondGenerationRequest searchFormDataEmbeddedRequest(FormDefinition fd, SearchConditions conditions, Integer page) {
        if (page == null) {
            page = 1;
        }

        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;

        SearchFormDataSecondGenerationRequest request = new SearchFormDataSecondGenerationRequest()
                .setFormUuid(formCode)
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setPageNumber(page)
                .setPageSize(1);

        if (conditions != null) {
            request = request
                    .setSearchCondition(conditions.getConditionsJson())
                    .setOrderConfigJson(conditions.getOrderMapJson());
        }

        return request;
    }


    public SearchFormDataSecondGenerationNoTableFieldRequest searchFormDataRequest(FormDefinition fd, SearchConditions conditions, Integer page) {
        if (page == null) {
            page = 1;
        }

        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;

        SearchFormDataSecondGenerationNoTableFieldRequest request = new SearchFormDataSecondGenerationNoTableFieldRequest()
                .setFormUuid(formCode)
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setPageNumber(page)
                .setPageSize(100);

        if (conditions != null) {
            request = request
                    .setSearchCondition(conditions.getConditionsJson())
                    .setOrderConfigJson(conditions.getOrderMapJson());
        }

        return request;
    }

    public ListTableDataByFormInstanceIdTableIdRequest queryInnerTablesRequest(FormDefinition masterFd, FormDefinition innerFd, Integer pageNum, Integer pageSize) {
        if (pageNum == null) {
            pageNum = 1;
        }

        String formCode = masterFd.getCode();
        String appType = masterFd.getAppType() == null ? this.appType : masterFd.getAppType();
        String sysToken = masterFd.getSysToken() == null ? this.sysToken : masterFd.getSysToken();
        String userId = this.userId;

        String tableFieldId = innerFd.getCode();

        com.aliyun.dingtalkyida_1_0.models.ListTableDataByFormInstanceIdTableIdRequest request =
                new com.aliyun.dingtalkyida_1_0.models.ListTableDataByFormInstanceIdTableIdRequest()
                        .setFormUuid(formCode)
                        .setPageNumber(pageNum)
                        .setAppType(appType)
                        .setSystemToken(sysToken)
                        .setPageSize(pageSize)
                        .setTableFieldId(tableFieldId)
                        .setUserId(userId);


        return request;
    }

    public SearchFormDatasRequest searchFormDatasRequest(FormDefinition fd, String conditions) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;

        return new SearchFormDatasRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormUuid(formCode)
                .setSearchFieldJson(conditions)
                .setCurrentPage(1)
                .setPageSize(100);
    }

    public SaveFormDataRequest createSaveRequest(FormDefinition fd, String json) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;
        return new SaveFormDataRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormUuid(formCode)
                .setFormDataJson(json);
    }

    public BatchSaveFormDataRequest createBatchSaveRequest(FormDefinition fd, List<String> data) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;
        return new BatchSaveFormDataRequest()
                .setNoExecuteExpression(true)
                .setAsynchronousExecution(false)
                .setKeepRunningAfterException(false)
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setFormUuid(formCode)
                .setFormDataJsonList(data);
    }

    public BatchRemovalByFormInstanceIdListRequest createBatchDeleteByIdRequest(FormDefinition fd, List<String> data) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;
        return new BatchRemovalByFormInstanceIdListRequest()
                .setExecuteExpression(false)
                .setAsynchronousExecution(false)
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setFormUuid(formCode)
                .setFormInstanceIdList(data);
    }

    public GetAccessTokenRequest createAccessTokenRequest() {
        return new GetAccessTokenRequest()
                .setAppKey(this.appKey)
                .setAppSecret(this.appSecret);
    }


    private void setDefaultParam() {

    }

    public StartInstanceRequest createStartInstanceRequest(FormDefinition fd, String data, String processCode) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;
        return new StartInstanceRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setFormUuid(formCode)
                .setProcessCode(processCode)
                .setFormDataJson(data);
    }

    public StartInstanceRequest createStartInstanceRequest(FormDefinition fd, String data, String processCode, String userId) {
        return createStartInstanceRequest(fd, data, processCode).setUserId(StringUtils.isBlank(userId) ? this.userId : userId);
    }

    public ExecuteTaskRequest createExecuteTaskRequest(FormDefinition fd, String data, String processInstanceId, String userId, Long taskId, String outResult) {
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        return new ExecuteTaskRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setOutResult(outResult)
                .setFormDataJson(data)
                .setRemark("同意")
                .setProcessInstanceId(processInstanceId)
                .setUserId(userId)
                .setTaskId(taskId);
    }

    public GetOperationRecordsRequest createOperationRecordsRequest(FormDefinition fd, String processInstanceId, String userId) {
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        return new GetOperationRecordsRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(StringUtils.isBlank(userId) ? this.userId : userId)
                .setProcessInstanceId(processInstanceId);
    }

    public com.aliyun.dingtalkyida_2_0.models.GetInstancesRequest createGetInstancesRequest(FormDefinition fd, SearchConditions conditions, String userId, Integer page, Integer pageSize) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        return new com.aliyun.dingtalkyida_2_0.models.GetInstancesRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(StringUtils.isBlank(userId) ? this.userId : userId)
                .setFormUuid(formCode)
                .setSearchFieldJson(conditions.getConditionsJson())
                .setOrderConfigJson(conditions.getOrderMapJson())
                .setPageNumber(page)
                .setPageSize(pageSize);
    }

    public GetOpenUrlRequest getOpenUrlRequest(FormDefinition fd, String fileUrl) {
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        return new GetOpenUrlRequest()
                .setSystemToken(sysToken)
                .setTimeout(86400000L)
                .setUserId(this.userId)
                .setFileUrl(fileUrl);
    }

    public BatchGetFormDataByIdListRequest createBatchGetFormDataByIdListRequest(FormDefinition fd, List<String> ids) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        String userId = this.userId;
        return new BatchGetFormDataByIdListRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setUserId(userId)
                .setFormUuid(formCode)
                .setFormInstanceIdList(ids);
    }

    public com.aliyun.dingtalkyida_2_0.models.GetFormDataByIDRequest createGetInstanceByIdRequest(FormDefinition fd) {
        String formCode = fd.getCode();
        String appType = fd.getAppType() == null ? this.appType : fd.getAppType();
        String sysToken = fd.getSysToken() == null ? this.sysToken : fd.getSysToken();
        return new com.aliyun.dingtalkyida_2_0.models.GetFormDataByIDRequest()
                .setAppType(appType)
                .setSystemToken(sysToken)
                .setFormUuid(formCode)
                .setUserId(this.userId);
    }
}
