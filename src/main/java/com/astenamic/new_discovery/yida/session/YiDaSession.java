package com.astenamic.new_discovery.yida.session;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.astenamic.new_discovery.form.FormDefinition;
import com.astenamic.new_discovery.form.FormJsonData;
import com.astenamic.new_discovery.form.FormJsonDataList;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import com.astenamic.new_discovery.yida.http.RequestBuilder;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class YiDaSession {
    private final AuthSession authSession;
    private final RequestBuilder requestBuilder;
    private final FormManager formManager;
    private final Client client;
    private final RuntimeOptions runtimeOptions;

    private static final Logger logger = LoggerFactory.getLogger(YiDaSession.class);

    public YiDaSession(AuthSession authSession, RequestBuilder requestBuilder, Client client, FormManager formManager) {
        this.authSession = authSession;
        this.requestBuilder = requestBuilder;
        this.client = client;
        this.formManager = formManager;
        this.runtimeOptions = new RuntimeOptions();
    }

    public <T extends YidaObject> List<String> batchUpdateDataByObjectId(List<T> data, Class<T> tClass) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        BatchUpdateFormDataByInstanceMapRequest request = this.requestBuilder.batchUpdateRequest(formDefinition);

        BatchUpdateFormDataByInstanceMapHeaders headers = new BatchUpdateFormDataByInstanceMapHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        Map<String, String> updateJson = data.stream()
                .collect(Collectors.toMap(
                        T::getObjectId,
                        datum -> this.formManager.serialize(datum, tClass).toString()
                ));

        request.setUpdateFormDataJsonMap(updateJson);

        try {

            BatchUpdateFormDataByInstanceMapResponse response = executeYidaRequest(() -> this.client.batchUpdateFormDataByInstanceMapWithOptions(request, headers, new RuntimeOptions()));

            Integer statusCode = response.statusCode;

            if (statusCode == 200) {
                return response.getBody().getResult();
            } else {
                throw new IllegalStateException("更新失败");
            }

        } catch (Exception e) {
            throw new TeaException(e.getMessage(), e);
        }
    }

    public <T extends YidaFlowObject> List<String> batchUpdateDataByInstanceId(List<T> data, Class<T> tClass) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        BatchUpdateFormDataByInstanceMapRequest request = this.requestBuilder.batchUpdateRequest(formDefinition);

        BatchUpdateFormDataByInstanceMapHeaders headers = new BatchUpdateFormDataByInstanceMapHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        Map<String, String> updateJson = data.stream()
                .collect(Collectors.toMap(
                        T::getInstanceId,
                        datum -> this.formManager.serialize(datum, tClass).toString()
                ));

        request.setUpdateFormDataJsonMap(updateJson);

        try {

            BatchUpdateFormDataByInstanceMapResponse response = executeYidaRequest(() -> this.client.batchUpdateFormDataByInstanceMapWithOptions(request, headers, new RuntimeOptions()));

            Integer statusCode = response.statusCode;

            if (statusCode == 200) {
                return response.getBody().result;
            } else {
                throw new IllegalStateException("更新失败");
            }

        } catch (Exception e) {
            throw new TeaException(e.getMessage(), e);
        }
    }

    public <T extends YidaObject> Boolean batchDeleteByObjectId(List<T> data, Class<T> tClass) {
        List<String> ids = data
                .stream()
                .map(YidaObject::getObjectId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        return this.batchDeleteByIds(ids, tClass);
    }

    public <T extends YidaObject> Boolean batchDeleteByIds(List<String> ids, Class<T> tClass) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        BatchRemovalByFormInstanceIdListRequest request = this.requestBuilder.createBatchDeleteByIdRequest(formDefinition, ids);

        BatchRemovalByFormInstanceIdListHeaders headers = new BatchRemovalByFormInstanceIdListHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {

            BatchRemovalByFormInstanceIdListResponse response = executeYidaRequest(() -> this.client.batchRemovalByFormInstanceIdListWithOptions(request, headers, new RuntimeOptions()));

            Integer statusCode = response.statusCode;

            if (statusCode == 200) {
                return true;
            } else {
                throw new IllegalStateException("删除失败");
            }

        } catch (Exception e) {
            throw new TeaException(e.getMessage(), e);
        }


    }


    public <T> List<T> searchFormDataConditionsEmbedded(Class<T> tClass, SearchConditions conditions, Integer page) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        SearchFormDataSecondGenerationHeaders headers = new SearchFormDataSecondGenerationHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        SearchFormDataSecondGenerationRequest request = this.requestBuilder.searchFormDataEmbeddedRequest(formDefinition, conditions, page);

        try {

            SearchFormDataSecondGenerationResponse response = executeYidaRequest(() -> this.client.searchFormDataSecondGenerationWithOptions(request, headers, this.runtimeOptions));

            SearchFormDataSecondGenerationResponseBody body = response.getBody();

            List<SearchFormDataSecondGenerationResponseBody.SearchFormDataSecondGenerationResponseBodyData> data = body.getData();

            List<JSONObject> jsonData = data.stream().map(f -> {
                JSONObject jon = new JSONObject(f.formData);
                jon.put("objectId", f.getFormInstanceId());
                jon.put("createTime", f.getCreateTimeGMT());
                jon.put("updateTime", f.getModifiedTimeGMT());
                return jon;
            }).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();

                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, tClass);

        } catch (Exception err) {
            throw new TeaException(err.getMessage(), err);
        }


    }

    public <T> List<T> searchFormDataConditionsRequest(Class<T> tClass, SearchConditions conditions) {
        return this.searchFormDataConditionsRequest(tClass, conditions, 1);
    }

    public <T> List<T> searchFormDataConditionsRequest(Class<T> tClass, SearchConditions conditions, Integer page) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        SearchFormDataSecondGenerationNoTableFieldRequest request = this.requestBuilder.searchFormDataRequest(formDefinition, conditions, page);

        SearchFormDataSecondGenerationNoTableFieldHeaders headers = new SearchFormDataSecondGenerationNoTableFieldHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {

            SearchFormDataSecondGenerationNoTableFieldResponse response = executeYidaRequest(() -> this.client.searchFormDataSecondGenerationNoTableFieldWithOptions(request, headers, this.runtimeOptions));

            SearchFormDataSecondGenerationNoTableFieldResponseBody body = response.getBody();

            List<SearchFormDataSecondGenerationNoTableFieldResponseBody.SearchFormDataSecondGenerationNoTableFieldResponseBodyData> data = body.getData();

            List<JSONObject> jsonData = data.stream().map(f -> {
                JSONObject jon = new JSONObject(f.formData);
                jon.put("objectId", f.getFormInstanceId());
                jon.put("createTime", f.getCreateTimeGMT());
                jon.put("updateTime", f.getModifiedTimeGMT());
                return jon;
            }).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();

                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, tClass);

        } catch (Exception err) {
            throw new TeaException(err.getMessage(), err);
        }

    }


    public <T extends YidaObject> String saveForm(T data, Class<T> tClass) {

        FormJsonData formData = this.formManager.serialize(data, tClass);

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        SaveFormDataRequest request = this.requestBuilder.createSaveRequest(formDefinition, formData.toString());

        SaveFormDataHeaders headers = new SaveFormDataHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {
            SaveFormDataResponse response = executeYidaRequest(() -> this.client.saveFormDataWithOptions(request, headers, this.runtimeOptions));

            SaveFormDataResponseBody body = response.getBody();

            if (response.getStatusCode() != 200 || StringUtils.isBlank(body.getResult())) {
                throw new IllegalStateException("保存失败，返回结果为空");
            }

            return body.getResult();

        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }
    }


    public <T extends YidaObject> List<String> batchSave(List<T> data, Class<T> tClass) {
        return this.doBatchSave(data, tClass);
    }

    public <T> List<String> batchSaveJson(List<JSONObject> data, Class<T> tClass) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        List<String> json = data.stream().map(s -> s.toJSONString()).toList();

        return doBatchSave(formDefinition, json);

    }

    public <T> List<String> doBatchSave(List<T> data, Class<T> tClass) {

        FormJsonDataList formData = this.formManager.serialize(data, tClass);

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        List<String> json = formData.toStringList();

        return doBatchSave(formDefinition, json);
    }

    public List<String> doBatchSave(FormDefinition formDefinition, List<String> json) {

        BatchSaveFormDataRequest request = this.requestBuilder.createBatchSaveRequest(formDefinition, json);

        BatchSaveFormDataHeaders headers = new BatchSaveFormDataHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {
            BatchSaveFormDataResponse response = executeYidaRequest(() -> this.client.batchSaveFormDataWithOptions(request, headers, this.runtimeOptions));

            BatchSaveFormDataResponseBody body = response.getBody();

            if (response.getStatusCode() != 200 || body == null || body.getResult() == null || body.getResult().isEmpty()) {
                throw new IllegalStateException("批量保存失败，返回结果为空");
            }

            return body.getResult();
        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }
    }

    public <T extends YidaFlowObject> String processSave(T data, Class<T> tClass, String processCode, String userId) {

        // 获取到数据和表的标识
        FormJsonData formData = this.formManager.serialize(data, tClass);

        // 获取code,appType,sysToken,formFields(date, item)
        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        // 所有数据
        String json = formData.getData().toString();

        StartInstanceRequest request = this.requestBuilder.createStartInstanceRequest(formDefinition, json, processCode, userId);

        StartInstanceHeaders headers = new StartInstanceHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {

            StartInstanceResponse response = executeYidaRequest(() -> this.client.startInstanceWithOptions(request, headers, this.runtimeOptions));
            StartInstanceResponseBody body = response.getBody();

            if (response.getStatusCode() != 200 || body == null || StringUtils.isBlank(body.getResult())) {
                throw new IllegalStateException("流程发起失败，返回结果为空");
            }

            return body.getResult();
        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }
    }

    public <T> List<GetOperationRecordsResponseBody.GetOperationRecordsResponseBodyResult> operationRecords(Class<T> tClass, String processInstanceId, String userId) {
        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);
        GetOperationRecordsRequest request = this.requestBuilder.createOperationRecordsRequest(formDefinition, processInstanceId, userId);

        GetOperationRecordsHeaders headers = new GetOperationRecordsHeaders();
        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {
            GetOperationRecordsResponse response = executeYidaRequest(() -> this.client.getOperationRecordsWithOptions(request, headers, this.runtimeOptions));
            GetOperationRecordsResponseBody body = response.getBody();
            return body.getResult();
        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }

    }

    public <T> void executeTask(T data, Class<T> tClass, String processInstanceId, String userId, Long taskId, String outResult) {

        FormJsonData formData = this.formManager.serialize(data, tClass);
        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);
        String json = formData.getData().toString();

        ExecuteTaskRequest request = this.requestBuilder.createExecuteTaskRequest(formDefinition, json, processInstanceId, userId, taskId, outResult);
        ExecuteTaskHeaders headers = new ExecuteTaskHeaders();
        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();
        try {
            ExecuteTaskResponse executeTaskResponse = executeYidaRequest(() -> this.client.executeTaskWithOptions(request, headers, this.runtimeOptions));
            if (executeTaskResponse.getStatusCode().equals(200)) {
                logger.info("执行任务成功");
            } else {
                throw new IllegalStateException("执行任务失败！");
            }
        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }

    }


    public <T> String temporaryUrls(String url, Class<T> tClass) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        GetOpenUrlRequest openUrlRequest = this.requestBuilder.getOpenUrlRequest(formDefinition, url);

        GetOpenUrlHeaders getOpenUrlHeaders = new GetOpenUrlHeaders();
        getOpenUrlHeaders.xAcsDingtalkAccessToken = this.authSession.getAccessToken();
        try {
            GetOpenUrlResponse openUrlWithOptions = executeYidaRequest(() -> this.client.getOpenUrlWithOptions(formDefinition.getAppType(), openUrlRequest, getOpenUrlHeaders, this.runtimeOptions));
            if (openUrlWithOptions.getStatusCode().equals(200)) {
                GetOpenUrlResponseBody body = openUrlWithOptions.getBody();
                return body.getResult();
            } else {
                throw new IllegalStateException("获取临时链接失败！");
            }
        } catch (Exception err) {
            throw new IllegalStateException(err.getMessage(), err);
        }
    }


    public <T, R> List<R> queryInnerTables(Class<T> masterClass, Class<R> innerClass, String formInstanceId, Integer page, Integer pageSize) {

        FormDefinition masterFd = this.formManager.getFormDefinition(masterClass);

        FormDefinition innerFd = this.formManager.getFormDefinition(innerClass);

        ListTableDataByFormInstanceIdTableIdRequest request = this.requestBuilder.queryInnerTablesRequest(masterFd, innerFd, page, pageSize);

        ListTableDataByFormInstanceIdTableIdHeaders headers = new ListTableDataByFormInstanceIdTableIdHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        try {

            ListTableDataByFormInstanceIdTableIdResponse response = executeYidaRequest(() -> this.client.listTableDataByFormInstanceIdTableIdWithOptions(formInstanceId, request, headers, this.runtimeOptions));

            ListTableDataByFormInstanceIdTableIdResponseBody body = response.getBody();

            if (body.getTotalCount() == 0) {
                return List.of();
            }

            List<JSONObject> jsonData = body.getData().stream().map(JSONObject::new).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();
                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, innerClass);

        } catch (Exception err) {
            throw new TeaException(err.getMessage(), err);
        }

    }


    public <T> List<T> searchFormDataByIds(Class<T> tClass, List<String> ids) {
        FormDefinition fd = this.formManager.getFormDefinition(tClass);
        BatchGetFormDataByIdListHeaders headers = new BatchGetFormDataByIdListHeaders();
        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();
        BatchGetFormDataByIdListRequest request = this.requestBuilder.createBatchGetFormDataByIdListRequest(fd, ids);

        try {
            BatchGetFormDataByIdListResponse response = executeYidaRequest(() -> this.client.batchGetFormDataByIdListWithOptions(request, headers, this.runtimeOptions));


            BatchGetFormDataByIdListResponseBody body = response.getBody();

            List<BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult> data = body.result;

            List<JSONObject> jsonData = data.stream().map(f -> {
                JSONObject jon = new JSONObject(f.formData);
                jon.put("objectId", f.getFormInstanceId());
                jon.put("createTime", f.getCreateTimeGMT());
                jon.put("updateTime", f.getModifiedTimeGMT());
                return jon;
            }).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();

                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, tClass);

        } catch (Exception err) {
            throw new TeaException(err.getMessage(), err);
        }
    }


    private <T> T executeYidaRequest(YidaRequest<T> request) {
        int maxRetries = 3;
        int retryCount = 0;
        long waitTimeMs = 1000; // 初始等待时间1秒

        while (true) {
            try {
                return request.request();
            } catch (Exception e) {
                retryCount++;
                if (retryCount > maxRetries) {
                    logger.error("请求失败，超过最大重试次数: {}", maxRetries, e);
                    throw new RuntimeException("请求失败", e);
                }

                // 计算下一次等待时间（指数退避 + 随机因素）
                waitTimeMs = Math.min(waitTimeMs * 2, 10000) + (long) (Math.random() * 1000);

                logger.warn("请求失败，正在进行第 {} 次重试，等待 {} 毫秒", retryCount, waitTimeMs, e);

                try {
                    Thread.sleep(waitTimeMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new TeaException("重试过程被中断", ie);
                }
            }
        }
    }


    @FunctionalInterface
    interface YidaRequest<T> {
        T request() throws Exception;
    }

    private boolean isArrays(String k){
        return k.contains("table") || k.contains("Table") || k.contains("multiSelectField") || k.contains("cascadeDateField");
    }

}
