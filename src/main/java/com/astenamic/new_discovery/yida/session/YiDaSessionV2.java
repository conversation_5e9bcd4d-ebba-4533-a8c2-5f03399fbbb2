package com.astenamic.new_discovery.yida.session;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dingtalkyida_2_0.Client;
import com.aliyun.dingtalkyida_2_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.astenamic.new_discovery.form.FormDefinition;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.yida.http.RequestBuilder;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;


public class YiDaSessionV2 {
    private final AuthSession authSession;
    private final RequestBuilder requestBuilder;
    private final FormManager formManager;
    private final Client client;
    private final RuntimeOptions runtimeOptions;

    private static final Logger logger = LoggerFactory.getLogger(YiDaSession.class);

    public YiDaSessionV2(AuthSession authSession, RequestBuilder requestBuilder, Client client, FormManager formManager) {
        this.authSession = authSession;
        this.requestBuilder = requestBuilder;
        this.client = client;
        this.formManager = formManager;
        this.runtimeOptions = new RuntimeOptions();
    }

    /**
     * 查询流程表单详情
     *
     * @param tClass     实体类
     * @param conditions 查询条件
     * @param page       页码
     * @return 实体类列表
     */
    public <T> List<T> searchProcessesInstances(Class<T> tClass, SearchConditions conditions, String userId, Integer page, Integer pageSize) {

        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        com.aliyun.dingtalkyida_2_0.models.GetInstancesHeaders headers = new com.aliyun.dingtalkyida_2_0.models.GetInstancesHeaders();

        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        GetInstancesRequest request = this.requestBuilder.createGetInstancesRequest(formDefinition, conditions, userId, page, pageSize);

        try {

            GetInstancesResponse response = executeYidaRequest(() -> this.client.getInstancesWithOptions(request, headers, this.runtimeOptions));

            GetInstancesResponseBody body = response.getBody();

            List<GetInstancesResponseBody.GetInstancesResponseBodyData> data = body.getData();

            List<JSONObject> jsonData = data.stream().map(f -> {
                JSONObject jon = new JSONObject(f.data);
                jon.put("instanceId", f.getProcessInstanceId());
                jon.put("instanceStatus", f.getInstanceStatus());
                jon.put("approvedResult", f.getApprovedResult() == null ? "" : f.getApprovedResult());
                jon.put("executorTable", f.getActionExecutor() == null ? new JSONArray() :
                        f.getActionExecutor().stream()
                                .map(executor -> {
                                    JSONObject executorJson = new JSONObject();
                                    executorJson.put("executorId", executor.getUserId());
                                    try {
                                        executorJson.put("executorName", executor.getName() != null ? executor.getName().getNameInChinese() : "");
                                    } catch (Exception ex) {
                                        executorJson.put("executorName", "");
                                        logger.error("获取执行人名称失败", ex);
                                    }
                                    return executorJson;
                                })
                                .toList());
                return jon;
            }).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();
                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, tClass);

        } catch (Exception err) {
            logger.error("获取宜搭报表数据失败，{}{}", err.getMessage(), System.lineSeparator(), err);
            throw new TeaException(err.getMessage(), err);
        }
    }

    public <T> T getInstanceById(Class<T> tClass, String instanceId) {
        FormDefinition formDefinition = this.formManager.getFormDefinition(tClass);

        com.aliyun.dingtalkyida_2_0.models.GetFormDataByIDHeaders headers = new com.aliyun.dingtalkyida_2_0.models.GetFormDataByIDHeaders();
        headers.xAcsDingtalkAccessToken = this.authSession.getAccessToken();

        GetFormDataByIDRequest request = this.requestBuilder.createGetInstanceByIdRequest(formDefinition);

        try {
            GetFormDataByIDResponse response = executeYidaRequest(() -> this.client.getFormDataByIDWithOptions(instanceId, request, headers, this.runtimeOptions));

            GetFormDataByIDResponseBody data = response.getBody();
            if (data.formData == null || data.formData.isEmpty()) {
                logger.warn("未找到实例ID为 {} 的数据", instanceId);
                return null; // 或者抛出异常
            }
            JSONObject jon = new JSONObject(data.formData);
            jon.put("objectId", data.getFormInstId());
            jon.put("updateTime", data.getModifiedTimeGMT());

            List<JSONObject> jsonData = Stream.of(jon).peek(j -> {
                        String k;
                        Object value;
                        for (Map.Entry<String, Object> en : j.entrySet()) {
                            k = en.getKey();
                            value = en.getValue();

                            if (List.class.isAssignableFrom(value.getClass()) && !isArrays(k)) {
                                value = ((List<?>) value).get(0);
                                if (value != null) {
                                    j.replace(k, value);
                                }
                            }
                        }
                    }
            ).toList();

            return formManager.deserialize(jsonData, tClass).get(0);

        } catch (Exception err) {
            logger.error("获取宜搭报表数据失败，{}{}", err.getMessage(), System.lineSeparator(), err);
            throw new TeaException(err.getMessage(), err);
        }
    }


    private <T> T executeYidaRequest(YiDaSession.YidaRequest<T> request) {
        int maxRetries = 3;
        int retryCount = 0;
        long waitTimeMs = 1000; // 初始等待时间1秒

        while (true) {
            try {
                return request.request();
            } catch (Exception e) {
                retryCount++;
                if (retryCount > maxRetries) {
                    logger.error("请求失败，超过最大重试次数: {}", maxRetries, e);
                    throw new RuntimeException("请求失败", e);
                }

                // 计算下一次等待时间（指数退避 + 随机因素）
                waitTimeMs = Math.min(waitTimeMs * 2, 10000) + (long) (Math.random() * 1000);

                logger.warn("请求失败，正在进行第 {} 次重试，等待 {} 毫秒", retryCount, waitTimeMs, e);

                try {
                    Thread.sleep(waitTimeMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new TeaException("重试过程被中断", ie);
                }
            }
        }
    }

    @FunctionalInterface
    interface YidaRequest<T> {
        T request() throws Exception;
    }

    private boolean isArrays(String k){
        return k.contains("table") || k.contains("Table") || k.contains("multiSelectField") || k.contains("cascadeDateField");
    }

}
