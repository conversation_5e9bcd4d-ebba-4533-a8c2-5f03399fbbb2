package com.astenamic.new_discovery.yida.session;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponseBody;
import com.aliyun.tea.TeaException;
import com.astenamic.new_discovery.yida.http.RequestBuilder;
import lombok.Data;

import java.util.Date;

public class AuthSession {
    private final Client client;
    private final RequestBuilder requestBuilder;
    private AccessToken accessToken;

    public AuthSession(Client client, RequestBuilder requestBuilder) {
        this.client = client;
        this.requestBuilder = requestBuilder;
    }

    public String getAccessToken() {

        if (this.accessToken == null || this.accessToken.isExpire()) {

            GetAccessTokenRequest request = this.requestBuilder.createAccessTokenRequest();

            try {
                GetAccessTokenResponse response = client.getAccessToken(request);
                GetAccessTokenResponseBody body = response.getBody();
                String accessToken = body.getAccessToken();
                Long expireIn = body.getExpireIn();
                this.accessToken = new AccessToken(accessToken, expireIn);
            } catch (Exception _err) {
                throw new TeaException(_err.getMessage(), _err);
            }
        }
        return this.accessToken.accessToken;
    }

    @Data
    private static class AccessToken {
        private String accessToken;
        private Long expireIn;

        public AccessToken(String accessToken, Long expireIn) {
            this.accessToken = accessToken;
            this.expireIn = expireIn;
        }

        public boolean isExpire() {
            return this.expireIn < new Date().getTime();
        }
    }
}
