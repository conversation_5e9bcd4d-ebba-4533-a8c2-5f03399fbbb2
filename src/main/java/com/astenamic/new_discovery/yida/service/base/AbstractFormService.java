package com.astenamic.new_discovery.yida.service.base;

import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.modal.YidaObject;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
public abstract class AbstractFormService<T extends YidaObject> {

    protected final Class<T> clazz;

    protected final YiDaSession yiDaSession;

    protected final YiDaSessionV2 yiDaSessionV2;

    protected final YidaConfigProperties yidaConfigProperties;

    /**
     * 查询列表
     *
     * @param cond 查询条件
     */
    public List<T> getFormsByCond(SearchConditions cond) {
        if (cond == null) {
            log.warn("查询表单失败，条件为空");
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>();
        int page = 1;
        int size = 0;
        do {
            List<T> list;
            try {
                list = yiDaSession.searchFormDataConditionsRequest(
                        this.clazz,
                        cond,
                        page
                );
            } catch (Exception e) {
                log.error("查询表单失败，cond：{}，错误信息：{}", cond, e.getMessage(), e);
                throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
            }
            page++;
            size = list.size();
            result.addAll(list);
        } while (size == 100);
        return result;
    }

    /**
     * 查询详情
     *
     * @param cond 查询条件
     */
    public T getFormByCond(SearchConditions cond) {
        if (cond == null) {
            log.warn("查询表单失败，条件为空");
            return null;
        }
        List<T> list = new ArrayList<>();
        try {
            list = yiDaSession.searchFormDataConditionsEmbedded(
                    this.clazz,
                    cond,
                    1
            );
        } catch (Exception e) {
            log.error("查询表单失败，cond：{}，错误信息：{}", cond, e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据 objectId 查询表单
     *
     * @param objectId 表单的 objectId
     */
    public T getFormByObjectId(String objectId) {
        if (StringUtils.isBlank(objectId)) {
            log.warn("查询表单失败：objectId 为空");
            return null;
        }
        return yiDaSessionV2.getInstanceById(this.clazz, objectId);
    }

    /**
     * 保存表单
     *
     * @param review 待保存的表单
     */

    public String save(T review) {
        if (review == null) {
            log.warn("保存表单调用失败：review 为空");
            throw new BizException("保存表单失败：review 为 null");
        }
        try {
            if (StringUtils.isBlank(review.getObjectId())) {
                log.info("保存新表单");
                return yiDaSession.saveForm(review, this.clazz);
            } else {
                log.info("更新表单，objectId: {}", review.getObjectId());
                yiDaSession.batchUpdateDataByObjectId(List.of(review), this.clazz);
                return review.getObjectId();
            }
        } catch (Exception e) {
            log.error("保存表单失败，review: {}，错误信息：{}", review, e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
    }

    /**
     * 批量保存或更新表单
     *
     * @param reviewList 待保存的表单列表
     */
    public List<String> batchSave(List<T> reviewList) {
        if (reviewList == null || reviewList.isEmpty()) {
            log.warn("批量保存调用失败：reviewList 为空");
            throw new BizException("批量保存失败：reviewList 为空");
        }
        List<String> results = new ArrayList<>();
        reviewList.stream()
                .collect(Collectors.groupingBy(d -> StringUtils.isBlank(d.getObjectId()) ? 1 : 2))
                .forEach((k, rows) -> {
                    log.info("数据分组完成，{}组，条数：{}", k, rows.size());
                    for (int start = 0; start < rows.size(); start += 10) {
                        int end = Math.min(start + 10, rows.size());
                        List<T> batch = rows.subList(start, end);
                        List<String> ts = new ArrayList<>();
                        if (k == 1) {
                            ts = yiDaSession.batchSave(batch, this.clazz);
                        } else {
                            ts = yiDaSession.batchUpdateDataByObjectId(batch, this.clazz);
                        }
                        if (ts != null && !ts.isEmpty()) {
                            results.addAll(ts);
                        }
                    }
                });
        log.info("批量保存完成，总条数：{}", reviewList.size());
        return results;
    }

    /**
     * 删除表单
     *
     * @param objectId 表单的 objectId
     */
    public String deleteFormByObjectId(String objectId) {
        if (StringUtils.isBlank(objectId)) {
            log.warn("删除表单失败：objectId 为空");
            return "";
        }
        try {
            deleteFormByObjectIds(List.of(objectId));
            return objectId;
        } catch (Exception e) {
            log.error("删除表单失败，objectId: {}，错误信息：{}", objectId, e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
    }

    /**
     * 删除表单
     *
     * @param ids 表单的 ids
     */
    public List<String> deleteFormByObjectIds(List<String> ids) {
        if (ids.isEmpty()) {
            log.warn("删除表单失败：ids 为空");
            return List.of();
        }
        try {
            yiDaSession.batchDeleteByIds(ids, this.clazz);
            return ids;
        } catch (Exception e) {
            log.error("删除表单失败，ids: {}，错误信息：{}", ids, e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
    }


    /**
     * 查询子表单数据
     *
     * @param innerClass 子表实体类
     * @param objectId   主表对象ID
     * @param <R>        子表实体类型
     * @return 子表数据列表
     */
    public <R> List<R> queryInnerList(Class<R> innerClass, String objectId) {
        List<R> result = new ArrayList<>();
        int page = 1;
        int pageSize = 50;
        int size = 0;

        do {
            List<R> list = new ArrayList<>();
            try {
                list = this.yiDaSession.queryInnerTables(
                        this.clazz,
                        innerClass,
                        objectId,
                        page,
                        pageSize
                );
            } catch (Exception e) {
                log.error("查询子表失败: {}", e.getMessage(), e);
                throw e;
            }
            result.addAll(list);
            size = list.size();
            page++;
        } while (size == pageSize);

        return result;
    }


}
