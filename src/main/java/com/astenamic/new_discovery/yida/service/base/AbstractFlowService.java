package com.astenamic.new_discovery.yida.service.base;


import com.astenamic.new_discovery.common.exception.BizException;
import com.astenamic.new_discovery.common.modal.Result;
import com.astenamic.new_discovery.yida.config.YidaConfigProperties;
import com.astenamic.new_discovery.yida.http.SearchConditions;
import com.astenamic.new_discovery.yida.modal.YidaFlowObject;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
public abstract class AbstractFlowService<T extends YidaFlowObject> {

    private final Class<T> clazz;

    protected final YiDaSession yiDaSession;

    protected final YiDaSessionV2 yiDaSessionV2;

    protected final YidaConfigProperties yidaConfigProperties;

    /**
     * 查询流程实例
     *
     * @param cond 查询条件
     */
    public T getFlowByCond(SearchConditions cond) {
        List<T> flows = new ArrayList<>();
        try {
            flows = yiDaSessionV2.searchProcessesInstances(
                    this.clazz,
                    cond,
                    yidaConfigProperties.getYidaBot().getFlowBot(),
                    1,
                    1
            );
        } catch (Exception e) {
            log.error("查询流程失败，条件：{}，错误信息：{}", cond, e.getMessage(), e);
            throw new BizException(Result.ErrorCode.YIDA_SESSION_ERROR);
        }
        return flows.isEmpty() ? null : flows.get(0);
    }

    /**
     * 查询流程实例列表
     *
     * @param cond 查询条件
     */
    public List<T> getFlowsByCond(SearchConditions cond) {
        List<T> result = new ArrayList<>();
        int page = 1;
        int pageSize = 10;
        int size;
        do {
            List<T> list;
            try {
                list = yiDaSessionV2.searchProcessesInstances(
                        this.clazz,
                        cond,
                        this.yidaConfigProperties.getYidaBot().getFlowBot(),
                        page,
                        pageSize
                );
            } catch (Exception e) {
                log.error("查询供应商报价流程失败: {}", e.getMessage(), e);
                throw e;
            }
            result.addAll(list);
            size = list.size();
            page++;
        } while (size == pageSize);
        log.info("查询供应商报价流程完成, code={}, total={}.", cond, result.size());
        return result;
    }

    /**
     * 发起流程
     * * @param flow 流程实例
     */
    protected String launchProcess(T flow) {
        if (flow == null) {
            log.info("没有需要处理的流程");
            throw new BizException("没有需要处理的流程,flow is null");
        }
        if (StringUtils.isNotBlank(flow.getInstanceId())) {
            log.info("流程已存在，instanceId: {}", flow.getInstanceId());
            return flow.getInstanceId();
        }
        return this.yiDaSession.processSave(flow, this.clazz, "", this.yidaConfigProperties.getYidaBot().getFlowBot());

    }

    /**
     * 批量发起流程
     *
     * @param flows 流程实例列表
     */
    protected List<String> batchLaunchProcess(List<T> flows) {
        if (flows == null || flows.isEmpty()) {
            log.info("没有需要处理的流程");
            throw new BizException("没有需要处理的流程,flows is empty");
        }
        List<String> result = flows.stream()
                .map(flow -> {
                    if (StringUtils.isBlank(flow.getInstanceId())) {
                        return this.yiDaSession.processSave(flow, this.clazz, "", this.yidaConfigProperties.getYidaBot().getFlowBot());
                    } else {
                        log.info("流程已存在，instanceId: {}", flow.getInstanceId());
                        return flow.getInstanceId();
                    }
                })
                .toList();
        log.info("流程批量发起完成，条数：{}", flows.size());
        return result;
    }

}
