package com.astenamic.new_discovery.yida.enums;

import lombok.Getter;

@Getter
public enum InstanceStatusEnum {

    RUNNING("运行中", "RUNNING"),
    TERMINATED("已终止", "TERMINATED"),
    COMPLETED("已完成", "COMPLETED"),
    ERROR("异常", "ERROR");


    private final String label;
    private final String value;

    InstanceStatusEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static InstanceStatusEnum getByValue(String value) {
        for (InstanceStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

}
