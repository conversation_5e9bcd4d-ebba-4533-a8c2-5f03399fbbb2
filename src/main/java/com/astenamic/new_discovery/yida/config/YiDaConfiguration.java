package com.astenamic.new_discovery.yida.config;

import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.teaopenapi.models.Config;
import com.astenamic.new_discovery.form.manage.FormManager;
import com.astenamic.new_discovery.yida.http.RequestBuilder;
import com.astenamic.new_discovery.yida.session.AuthSession;
import com.astenamic.new_discovery.yida.session.YiDaSession;
import com.astenamic.new_discovery.yida.session.YiDaSessionV2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class YiDaConfiguration {
    private String appType = "APP_SW1NM01KIYJPSUV9CFTN";
    private String sysToken = "S0A66QD1L8FQTKJ9DWW2ECNOXRPP23ZZDVM3M3K";
    private String appKey = "dingpuqekuek0vjz8fi4";
    private String appSecret = "6WDjW-uuD37xbiIrTuwZFX9Vjr70AD_4jIRSH7T7OPZTZABtJDm0Y_3qJn0DzNMY";

    @Bean
    public Client createYiDaClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    @Bean
    public com.aliyun.dingtalkyida_2_0.Client createYiDaClient2() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkyida_2_0.Client(config);
    }

    @Bean
    public RequestBuilder createRequestBuilder(YidaConfigProperties yidaConfigProperties) {
        return new RequestBuilder(yidaConfigProperties.getYidaBot().getFlowBot(), this.appType, this.sysToken, this.appKey, this.appSecret);
    }

    @Bean
    public YiDaSession createYiDaSession(AuthSession authSession, RequestBuilder requestBuilder, Client client, FormManager formManager) {
        return new YiDaSession(authSession, requestBuilder, client, formManager);
    }

    @Bean
    public YiDaSessionV2 createYiDaSessionV2(AuthSession authSession, RequestBuilder requestBuilder, com.aliyun.dingtalkyida_2_0.Client client, FormManager formManager) {
        return new YiDaSessionV2(authSession, requestBuilder, client, formManager);
    }

}
