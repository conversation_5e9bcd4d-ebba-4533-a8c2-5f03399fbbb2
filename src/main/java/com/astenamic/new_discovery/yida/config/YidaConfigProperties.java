package com.astenamic.new_discovery.yida.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 钉钉宜搭全局配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yida.config")
public class YidaConfigProperties {

    /** 应用类型 */
    private String appType;
    /** 系统令牌 */
    private String sysToken;
    /** 应用Key */
    private String appKey;
    /** 应用Secret */
    private String appSecret;
    /** 机器人配置 */
    private YidaBotProperties yidaBot;

    @Data
    public static class YidaBotProperties {
        /** 流程机器人ID */
        private String flowBot;
    }
}
