package com.astenamic.new_discovery.yida.config;

import com.astenamic.new_discovery.yida.http.RequestBuilder;
import com.astenamic.new_discovery.yida.session.AuthSession;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.teaopenapi.models.Config;

@Configuration
public class AuthConfiguration {

    @Bean
    public Client createAuthClient() throws Exception {
        Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    @Bean
    public AuthSession createAuthSession(Client client, RequestBuilder requestBuilder) {
        return new AuthSession(client, requestBuilder);
    }

}
