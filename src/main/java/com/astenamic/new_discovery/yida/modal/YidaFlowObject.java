package com.astenamic.new_discovery.yida.modal;

import com.alibaba.fastjson2.annotation.JSONField;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;

import java.util.List;

@Data
@MappedSuperclass
public class YidaFlowObject {

    private String instanceId;

    private String instanceStatus;

    private List<Executor> executorTable;

    private String approvedResult;

    @Data
    public static class Executor {

        private String executorId;

        private String executorName;
    }

}
