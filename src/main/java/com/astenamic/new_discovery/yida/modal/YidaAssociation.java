package com.astenamic.new_discovery.yida.modal;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class YidaAssociation extends YidaComponent {

    String appType;

    String formUuid;

    String formType = "receipt";

    String instanceId;

    String title;

    String subTitle;

}
