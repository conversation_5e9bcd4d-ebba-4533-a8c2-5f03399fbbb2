WITH sa AS (SELECT a.shop_id
                 -- 日本期曝光人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.exposure_people_num END)     AS exposure_people_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.exposure_people_num END)       AS exposure_people_num_mt
                 -- 日环期曝光人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN la.exposure_people_num END)    AS exposure_people_num_el_l
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN la.exposure_people_num END)      AS exposure_people_num_mt_l
                 -- 进店转化率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.shop_conversion_rate END)    AS shop_conversion_rate_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.shop_conversion_rate END)      AS shop_conversion_rate_mt
                 -- 下单转化率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.conversion_rate_order END)   AS conversion_rate_order_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.conversion_rate_order END)     AS conversion_rate_order_mt
                 -- 下单新客
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.new_customers_order_num END) AS new_customers_order_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.new_customers_order_num END)   AS new_customers_order_num_mt
                 -- 下单人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.order_people_num END)        AS order_people_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.order_people_num END)          AS order_people_num_mt
                 -- 店铺星级评分
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.biz_rating END)              AS biz_rating_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.biz_rating END)                AS biz_rating_mt
                 -- 出餐上报率
                 , max(CASE WHEN a.channel_name LIKE '%美团%' THEN a.ontime_delivery_rate END)      AS meal_report_rate
                 -- 出餐超时率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.merchant_overtime_rate END)  AS merchant_overtime_rate_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.merchant_overtime_rate END)    AS merchant_overtime_rate_mt
                 -- 菜品下架清单
                 , NULL                                                                             AS off_shelves
                 -- 外卖营业时长
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.basic_operating_hours END)   AS basic_operating_hours_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.basic_operating_hours END)     AS basic_operating_hours_mt
            FROM wm_shop_analysis AS a
                     LEFT JOIN wm_shop_analysis AS la ON la.shop_id = a.shop_id AND la.channel_id = a.channel_id
            WHERE a.snapshot_date = '${targetDay}'
              AND la.snapshot_date = '${lastDay}'
            GROUP BY a.shop_id),

     -- 饿了么昨日差评率
     review_el AS (SELECT b.shop_id
                        , a.门店名称
                        -- 饿了么昨天评价总数
                        , COUNT(1)                                                  AS total_review_el
                        -- 饿了么昨日差评数
                        , SUM(CASE WHEN a.总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_el
                   FROM xfx_comment_elm AS a
                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.elm_id
                        --替换昨日参数
                   WHERE a.评价时间 = '${targetDay}'
                   GROUP BY b.shop_id, a.门店名称),

     -- 美团外卖昨日差评率
     review_mt AS (SELECT b.shop_id
                        , a.门店名称
                        -- 美团昨天评价总数
                        , COUNT(1)                                                AS total_review_mt
                        -- 美团昨日差评数
                        , SUM(CASE WHEN 总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_mt
                   FROM xfx_comment_mt AS a
                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.mt_id
                        -- 替换昨日参数
                   WHERE SUBSTR(评价时间, 1, 10) = '${targetDay}'
                   GROUP BY b.shop_id, a.门店名称),

     -- 点评星级、昨日点评中差评率
     dp AS (SELECT b.shop_id
                 , a.门店名称
                 -- 点评店铺分
                 , a.美团星级            AS biz_rating_dp
                 , a.新评价数            AS total_review_dp
                 , a.新中差评数          AS neg_review_dp
                 -- 点评差评率
                 , CASE
                       WHEN a.新评价数 = 0 THEN 0
                       ELSE ROUND((cast(a.新中差评数 AS FLOAT) / cast(a.新评价数 AS FLOAT)):: NUMERIC,
                                  2) END AS neg_rate_dp
            FROM xfx_dianping AS a
                     LEFT JOIN xfx_shop_map b ON a.美团门店ID = b.dp_id
            WHERE TO_DATE(a.日期, 'YYYYMMDD') = '${targetDay}'),

     --会员券拉动金额
     member AS (SELECT b.shop_id
                     , a.shop_name
                     -- 券拉动金额
                     , SUM(CAST(a.pull_amount AS FLOAT)) AS member_drive_funds
                FROM xfx_coupon_summer AS a
                         LEFT JOIN xfx_shop_map b ON a.shop_name = b.crm_name
                WHERE base_date = '${targetDay}'
                GROUP BY b.shop_id, a.shop_name),

     --菜品出餐速度
     meal AS (SELECT b.shop_id
                   , a.shop_name
                   -- 菜品总出餐时长
                   , SUM(a.total_meal_time) AS total_meal_time
                   -- 出餐量
                   , SUM(a.meal_num)        AS meal_num
              FROM xfx_meal_speed AS a
                       LEFT JOIN xfx_shop_map AS b ON a.thirds_shop_id = b.pingzhi_id
              WHERE a.base_date = '${targetDay}'
              GROUP BY b.shop_id, a.shop_name),

     -- 会员注册率
     psg as (SELECT a.base_shop_id AS shop_id
                  , a.base_date
                  , a.base_shop_name
                  -- 客流
                  , CASE
                        WHEN base_field10 = '新发现'
                            AND base_field13 LIKE '%上海%' THEN
                            round(pos_field16 / 80, 0)
                        WHEN base_field10 = '新发现'
                            AND base_field13 NOT LIKE '%上海%' THEN
                            round(pos_field16 / 75, 0)
                        WHEN base_field10 = '烤匠' THEN
                            round(pos_field16 / 90, 0)
                        WHEN base_field10 = '蝴蝶里' THEN
                            pos_field7
             END                   AS psg_flow
                  -- 会员注册数
                  , b.mem_rsg
             FROM wt_shop AS a
                      LEFT JOIN (SELECT base_date, shop_name, COUNT(1) AS mem_rsg
                                 FROM xfx_member
                                 GROUP BY shop_name, base_date) AS b ON b.shop_name = a.base_shop_name
                 AND a.base_date :: DATE = b.base_date
             WHERE a.base_date = '${targetDay}')
        ,

-- 等位流失率
     churn_rate as (SELECT b.shop_id
--                    SUBSTRING(A.shop_name FROM '^(.*\))') meal_shop_name,
--                  , a.base_date
-- 流失人数
                         , a.ls_number
-- 取号人数
                         , a.qh_number
                    FROM xfx_churn_rate AS a
                             LEFT JOIN xfx_shop_map b
                                       ON SUBSTRING(a.shop_name FROM '^(.*\))') = b.meal_name
                    WHERE a.base_date = '${targetDay}')
        ,

-- 月累计营收（累计至当前日）
     acu AS (SELECT a.base_shop_id     AS shop_id
                  -- 门店累计总应收
                  , SUM(a.pos_field16) AS acu_revenue
                  -- 堂食累计总应收
                  , SUM(a.pos_field17) AS dine_in_acu_revenue
                  -- 外卖累计总应收
                  , SUM(a.pos_field18) AS take_out_acu_revenue
             FROM wt_shop AS a
             WHERE a.base_date LIKE '${targetMonth}%'
               AND a.base_date:: DATE >= '${firstDayOfMonth}':: DATE
               AND a.base_date:: DATE <= '${targetDay}':: DATE
             GROUP BY a.base_shop_id)

SELECT ROW_NUMBER() OVER ()                            AS id
     , NULL                                            AS object_id
     , NULL                                            AS group_id
     -- 门店id
     , a.base_shop_id                                  AS base_shop_id
     -- 门店编号
     , s.shop_no
     -- 时间
     , a.base_date                                     AS date
     -- 门店桌数
     , a.base_field19                                  AS table_num
     -- 本期总实收
     , a.pos_field16                                   AS revenue
     -- 环期总实收
     , la.pos_field16                                  AS revenue_l
     -- 同期总实收
     , pa.pos_field16                                  AS revenue_p
     -- 本期累计至当前日期的总营收
     , acu.acu_revenue
     -- 本期总应收
     , a.pos_field9                                    AS ar
     -- 环期总应收
     , la.pos_field9                                   AS ar_l
     -- 同期总应收
     , pa.pos_field9                                   AS ar_p
     -- 营收目标额
     , b.pos_field16                                   AS budget_revenue
     -- 退菜金额
     , ABS(ret.retreat_money)                          AS retreat_money
     -- 折扣总金额
     , a.pos_field21                                   AS discount_amount
     -- 堂食累计总实收
     , acu.dine_in_acu_revenue
     -- 本期堂食实收
     , a.pos_field17                                   AS dine_in_revenue
     -- 本期堂食实收（含自提）
     , a.pos_field17 + a.pos_field19                   AS dine_in_rec
     -- 环期堂食实收（含自提）
     , la.pos_field17 + la.pos_field19                 AS dine_in_rec_l
     -- 同期堂食实收（含自提）
     , pa.pos_field17 + pa.pos_field19                 AS dine_in_rec_p
     -- 本期堂食应收（含自提）
     , a.pos_field10 + a.pos_field12                   AS dine_in_ar
     -- 环期堂食应收（含自提）
     , la.pos_field10 + la.pos_field12                 AS dine_in_ar_l
     -- 同期堂食应收（含自提）
     , pa.pos_field10 + pa.pos_field12                 AS dine_in_ar_p
     -- 堂食折扣金额
     , a.pos_field22                                   AS dine_in_discount_amount
     -- 堂食营收目标额
     , b.pos_field44                                   AS budget_dine_in_revenue
     -- 本期开桌数
     , a.pos_field2                                    AS tables_open
     -- 环期开桌数
     , la.pos_field2                                   AS tables_open_l
     -- 同期开桌数
     , pa.pos_field2                                   AS tables_open_p
     -- 本期来客数
     , a.pos_field7                                    AS cus_num
     -- 环期来客数
     , la.pos_field7                                   AS cus_num_l
     -- 同期来客数
     , pa.pos_field7                                   AS cus_num_p
     -- 本期堂食客流（含自提）
     , a.pos_field8 + COALESCE(a.pos_field46, 0.0)     AS cus_flow
     -- 环期堂食客流（含自提）
     , la.pos_field8 + COALESCE(la.pos_field46, 0.0)   AS cus_flow_l
     -- 同期堂食客流（含自提）
     , pa.pos_field8 + COALESCE(pa.pos_field46, 0.0)   AS cus_flow_p
     -- 流失人数
     , churn_rate.ls_number
     -- 取号人数
     , churn_rate.qh_number
     -- 菜品总出餐时长
     , meal.total_meal_time
     -- 出餐量
     , meal.meal_num
     -- 客流
     , psg.psg_flow
     -- 注册会员数
     , psg.mem_rsg
     -- 会员拉动资金
     , member.member_drive_funds                       AS member_drive_funds
     -- 外卖累计总应收
     , acu.take_out_acu_revenue
     -- 本期外卖实收
     , a.pos_field18                                   AS take_out_revenue
     -- 环期外卖实收
     , la.pos_field18                                  AS take_out_revenue_l
     -- 同期外卖实收
     , pa.pos_field18                                  AS take_out_revenue_p
     -- 本期外卖应收
     , a.pos_field11                                   AS take_out_ar
     -- 环期外卖应收
     , la.pos_field11                                  AS take_out_ar_l
     -- 同期外卖应收
     , pa.pos_field11                                  AS take_out_ar_p
     -- 外卖折扣金额
     , a.pos_field23                                   AS take_out_discount_amount
     -- 外卖实收目标额
     , b.pos_field18                                   AS take_out_budget
     -- 本期外卖订单数
     , a.pos_field4                                    AS take_out_num
     -- 环期外卖订单数
     , la.pos_field4                                   AS take_out_num_l
     -- 同期外卖订单数
     , pa.pos_field4                                   AS take_out_num_p
     -- 本期曝光人数
     , sa.exposure_people_num_el
     , sa.exposure_people_num_mt
     -- 环期曝光人数
     , sa.exposure_people_num_el_l
     , sa.exposure_people_num_mt_l
     -- 进店转化率
     , sa.shop_conversion_rate_el * 100                AS shop_conversion_rate_el
     , sa.shop_conversion_rate_mt * 100                AS shop_conversion_rate_mt
     -- 下单转化率
     , sa.conversion_rate_order_el * 100               AS conversion_rate_order_el
     , sa.conversion_rate_order_mt * 100               AS conversion_rate_order_mt
     -- 下单新客
     , sa.new_customers_order_num_el
     , sa.new_customers_order_num_mt
     -- 下单人数
     , sa.order_people_num_el
     , sa.order_people_num_mt
     -- 店铺星级评分
     , sa.biz_rating_el
     , sa.biz_rating_mt
     -- 出餐上报率
     , REPLACE(sa.meal_report_rate, '%', '') ::NUMERIC AS meal_report_rate
     -- 外卖差评率
     , review_el.total_review_el
     , review_el.neg_review_el
     , review_mt.total_review_mt
     , review_mt.neg_review_mt
     -- 出餐超时率
     , sa.merchant_overtime_rate_el
     , sa.merchant_overtime_rate_mt
     -- 菜品下架清单
     , sa.off_shelves
     -- 外卖营业时长
     , sa.basic_operating_hours_el
     , sa.basic_operating_hours_mt
     -- 点评分
     , dp.biz_rating_dp
     -- 点评差评率
     , dp.neg_rate_dp * 100                            AS neg_rate_dp
     -- 点评差评数
     , dp.neg_review_dp
     -- 点评总评数
     , dp.total_review_dp
FROM wt_shop AS a
         LEFT JOIN base_shop AS s ON s.shop_id = a.base_shop_id
         LEFT JOIN data_budget_month AS b ON b.base_shop_id = a.base_shop_id AND b.base_month = '${targetMonth}'
         LEFT JOIN wt_shop AS la ON la.base_shop_id = a.base_shop_id AND la.base_date = '${lastDay}'
         LEFT JOIN wt_shop AS pa ON pa.base_shop_id = a.base_shop_id AND pa.base_date = '${sameDayLastYear}'
         LEFT JOIN retreat_statistics_shop_retreat_rate_report AS ret
                   ON ret.shop_id = a.base_shop_id AND ret.retreat_range = '5分钟以上' AND
                      ret.account_date = '${targetDay}'
         LEFT JOIN sa ON sa.shop_id = a.base_shop_id
         LEFT JOIN review_el ON review_el.shop_id = a.base_shop_id
         LEFT JOIN review_mt ON review_mt.shop_id = a.base_shop_id
         LEFT JOIN dp ON dp.shop_id = a.base_shop_id
         LEFT JOIN member ON member.shop_id = a.base_shop_id
         LEFT JOIN meal ON meal.shop_id = a.base_shop_id
         LEFT JOIN psg ON psg.shop_id = a.base_shop_id
         LEFT JOIN churn_rate ON churn_rate.shop_id = a.base_shop_id
         LEFT JOIN acu ON acu.shop_id = a.base_shop_id
WHERE a.base_date = '${targetDay}'
--   AND a.base_shop_id = '9323219540978471018';

