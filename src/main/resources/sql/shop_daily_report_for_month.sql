WITH sa AS (SELECT a.shop_id
                 -- 月本期曝光人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.exposure_people_num END)     AS exposure_people_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.exposure_people_num END)       AS exposure_people_num_mt
                 -- 月环期曝光人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN la.exposure_people_num END)    AS exposure_people_num_el_l
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN la.exposure_people_num END)      AS exposure_people_num_mt_l
                 -- 进店转化率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.shop_conversion_rate END)    AS shop_conversion_rate_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.shop_conversion_rate END)      AS shop_conversion_rate_mt
                 -- 下单转化率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.conversion_rate_order END)   AS conversion_rate_order_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.conversion_rate_order END)     AS conversion_rate_order_mt
                 -- 下单新客
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.new_customers_order_num END) AS new_customers_order_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.new_customers_order_num END)   AS new_customers_order_num_mt
                 -- 下单人数
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.order_people_num END)        AS order_people_num_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.order_people_num END)          AS order_people_num_mt
                 -- 店铺星级评分
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.biz_rating END)              AS biz_rating_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.biz_rating END)                AS biz_rating_mt
                 -- 出餐上报率
                 , max(CASE WHEN a.channel_name LIKE '%美团%' THEN a.ontime_delivery_rate END)      AS meal_report_rate
                 -- 出餐超时率
                 , AVG(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.merchant_overtime_rate END)  AS merchant_overtime_rate_el
                 , AVG(CASE WHEN a.channel_name LIKE '%美团%' THEN a.merchant_overtime_rate END)    AS merchant_overtime_rate_mt
                 -- 菜品下架清单
                 , NULL::NUMERIC                                                                    AS off_shelves
                 -- 外卖营业时长
                 , SUM(CASE WHEN a.channel_name LIKE '%饿了么%' THEN a.basic_operating_hours END)   AS basic_operating_hours_el
                 , SUM(CASE WHEN a.channel_name LIKE '%美团%' THEN a.basic_operating_hours END)     AS basic_operating_hours_mt
            FROM wm_shop_analysis AS a
                     LEFT JOIN wm_shop_analysis AS la ON la.shop_id = a.shop_id AND la.channel_id = a.channel_id
            WHERE a.snapshot_date LIKE '2024-12%'  --'${targetMonth}%'
              AND la.snapshot_date LIKE '2024-11%'--'${lastMonth}%'
            GROUP BY a.shop_id),
     -- 饿了么上月差评率
     review_el AS (SELECT b.shop_id
                        , a.门店名称
                        -- 饿了么昨天评价总数
                        , COUNT(1)                                                  AS total_review_el
                        -- 饿了么昨日差评数
                        , SUM(CASE WHEN a.总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_el
                   FROM xfx_comment_elm AS a
                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.elm_id
                        --替换昨日参数
                   WHERE a.评价时间 LIKE '${lastMonth}%'
                   GROUP BY b.shop_id, a.门店名称),

     -- 美团外卖上月差评率
     review_mt AS (SELECT b.shop_id
                        , a.门店名称
                        -- 美团昨天评价总数
                        , COUNT(1)                                                AS total_review_mt
                        -- 美团昨日差评数
                        , SUM(CASE WHEN 总体评分 IN ('1', '2') THEN 1 ELSE 0 END) AS neg_review_mt
                   FROM xfx_comment_mt AS a
                            LEFT JOIN xfx_shop_map b ON a.门店ID = b.mt_id
                        -- 替换昨日参数
                   WHERE SUBSTR(评价时间, 1, 10) LIKE '${lastMonth}%'
                   GROUP BY b.shop_id, a.门店名称),

     -- 点评星级、点评中差评率
     dp AS (SELECT b.shop_id
                  , ROW_NUMBER() OVER ()                                      AS id
                  , '2024-12'                                       AS date
                 -- 点评店铺分
                 , AVG(a.美团星级)   AS biz_rating_dp
                 , SUM(a.新评价数)   AS total_review_dp
                 , SUM(a.新中差评数) AS neg_review_dp
            FROM xfx_dianping AS a
                     LEFT JOIN xfx_shop_map b ON a.美团门店ID = b.dp_id
            WHERE TO_CHAR(TO_DATE(a.日期, 'YYYYMMDD'), 'YYYY-MM-DD') LIKE CONCAT('2024-12','%')
            GROUP BY b.shop_id),

     --会员券拉动金额
     member AS (SELECT b.shop_id
                     , a.shop_name
                     -- 券拉动金额
                     , SUM(CAST(a.pull_amount AS FLOAT)) AS member_drive_funds
                FROM xfx_coupon_summer AS a
                         LEFT JOIN xfx_shop_map b ON a.shop_name = b.crm_name
                WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') LIKE CONCAT('2024-12','%')--'${targetMonth}%'
                GROUP BY b.shop_id, a.shop_name),

     --菜品出餐速度
     meal AS (SELECT b.shop_id
                   , a.shop_name
                   -- 菜品总出餐时长
                   , SUM(a.total_meal_time) AS total_meal_time
                   -- 出餐量
                   , SUM(a.meal_num)        AS meal_num
              FROM xfx_meal_speed AS a
                       LEFT JOIN xfx_shop_map AS b ON a.thirds_shop_id = b.pingzhi_id
              WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') LIKE '${targetMonth}%'
              GROUP BY b.shop_id, a.shop_name),

     -- 会员注册率
     psg as (SELECT a.base_shop_id AS shop_id
                  , a.base_shop_name
                  -- 客流
                  , SUM(CASE
                            WHEN base_field10 = '新发现'
                                AND base_field13 LIKE '%上海%' THEN
                                round(pos_field16 / 80, 0)
                            WHEN base_field10 = '新发现'
                                AND base_field13 NOT LIKE '%上海%' THEN
                                round(pos_field16 / 75, 0)
                            WHEN base_field10 = '烤匠' THEN
                                round(pos_field16 / 90, 0)
                            WHEN base_field10 = '蝴蝶里' THEN
                                pos_field7
             END)                  AS psg_flow
                  -- 会员注册数
                  , SUM(b.mem_rsg) AS mem_rsg
             FROM wt_shop AS a
                      LEFT JOIN (SELECT base_date, shop_name, COUNT(1) AS mem_rsg
                                 FROM xfx_member
                                 GROUP BY shop_name, base_date) AS b ON b.shop_name = a.base_shop_name
                 AND a.base_date :: DATE = b.base_date
             WHERE a.base_date LIKE '${targetMonth}%'
             GROUP BY a.base_shop_id, a.base_shop_name)
        ,

     -- 等位流失率
     churn_rate as (SELECT b.shop_id
                         -- 流失人数
                         , SUM(a.ls_number) AS ls_number
                         -- 取号人数
                         , SUM(a.qh_number) AS qh_number
                    FROM xfx_churn_rate AS a
                             LEFT JOIN xfx_shop_map AS b ON SUBSTRING(a.shop_name FROM '^(.*\))') = b.meal_name
                    WHERE TO_CHAR(a.base_date, 'YYYY-MM-DD') LIKE '${targetMonth}%'
                    GROUP BY b.shop_id)

SELECT ROW_NUMBER() OVER ()                                 AS id
     , NULL                                                 AS object_id
     -- 门店id
     , a.base_shop_id                                       AS base_shop_id
     -- 时间
     , '${targetMonth}-01'                                  AS date
     -- 本期总应收
     , SUM(a.pos_field9)                                    AS ar
     -- 环期总应收
     , SUM(la.pos_field9)                                   AS ar_l
     -- 同期总应收
     , SUM(pa.pos_field9)                                   AS ar_p
     -- 营收目标额
     , SUM(b.pos_field16)                                   AS budget_revenue

FROM wt_shop AS a
         LEFT JOIN data_budget_month AS b ON b.base_shop_id = a.base_shop_id AND b.base_month LIKE concat('2024-12','%')
         LEFT JOIN wt_shop AS la ON la.base_shop_id = a.base_shop_id AND la.base_date LIKE concat('2024-11','%') --${lastMonth}'%'
         LEFT JOIN wt_shop AS pa ON pa.base_shop_id = a.base_shop_id AND pa.base_date LIKE concat('2023-12','%') --${sameMonthLastYear}'%'
WHERE a.base_date LIKE concat('2024-12','%') --${targetMonth}'%'
GROUP BY a.base_shop_id;




select *from wt_shop where base_date LIKE concat('2024-12','%');
--   AND a.base_shop_id = '9323219540978471018';


WITH last AS (SELECT a.base_shop_id                                   AS base_shop_id
                   -- 门店桌数
                   , SUM(a.base_field19)                              AS table_num
                   -- 本期总实收
                   , SUM(a.pos_field16)                               AS revenue
                   -- 本期累计至当前日期的总营收
                   , SUM(a.pos_field16)                               AS acu_revenue
                   -- 本期总应收
                   , SUM(a.pos_field9)                                AS ar
                   -- 折扣总金额
                   , SUM(a.pos_field21)                               AS discount_amount
                   -- 本期堂食实收
                   , SUM(a.pos_field17)                               AS dine_in_revenue
                   -- 本期堂食实收（含自提）
                   , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec
                   -- 本期堂食应收（含自提）
                   , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar
                   -- 堂食折扣金额
                   , SUM(a.pos_field22)                               AS dine_in_discount_amount
                   -- 本期开桌数
                   , SUM(a.pos_field2)                                AS tables_open
                   -- 本期来客数
                   , SUM(a.pos_field7)                                AS cus_num
                   -- 本期堂食客流（含自提）
                   , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow
                   -- 本期外卖实收
                   , SUM(a.pos_field18)                               AS take_out_revenue
                   -- 本期外卖应收
                   , SUM(a.pos_field11)                               AS take_out_ar
                   -- 外卖折扣金额
                   , SUM(a.pos_field23)                               AS take_out_discount_amount
                   -- 本期外卖订单数
                   , SUM(a.pos_field4)                                AS take_out_num
              FROM wt_shop AS a
              WHERE a.base_date ~ '^(2024-11|2024-12)'
              GROUP BY a.base_shop_id),

     pcp AS (SELECT a.base_shop_id                                   AS base_shop_id
                  -- 门店桌数
                  , SUM(a.base_field19)                              AS table_num
                  -- 本期总实收
                  , SUM(a.pos_field16)                               AS revenue
                  -- 本期累计至当前日期的总营收
                  , SUM(a.pos_field16)                               AS acu_revenue
                  -- 本期总应收
                  , SUM(a.pos_field9)                                AS ar
                  -- 折扣总金额
                  , SUM(a.pos_field21)                               AS discount_amount
                  -- 本期堂食实收
                  , SUM(a.pos_field17)                               AS dine_in_revenue
                  -- 本期堂食实收（含自提）
                  , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec
                  -- 本期堂食应收（含自提）
                  , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar
                  -- 堂食折扣金额
                  , SUM(a.pos_field22)                               AS dine_in_discount_amount
                  -- 本期开桌数
                  , SUM(a.pos_field2)                                AS tables_open
                  -- 本期来客数
                  , SUM(a.pos_field7)                                AS cus_num
                  -- 本期堂食客流（含自提）
                  , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow
                  -- 本期外卖实收
                  , SUM(a.pos_field18)                               AS take_out_revenue
                  -- 本期外卖应收
                  , SUM(a.pos_field11)                               AS take_out_ar
                  -- 外卖折扣金额
                  , SUM(a.pos_field23)                               AS take_out_discount_amount
                  -- 本期外卖订单数
                  , SUM(a.pos_field4)                                AS take_out_num
             FROM wt_shop AS a
             WHERE a.base_date ~ '^(2024-11|2024-12)'
             GROUP BY a.base_shop_id),

     ret AS (SELECT a.shop_id
                  -- 退菜金额
                  , SUM(ABS(a.retreat_money)) AS retreat_money
             FROM retreat_statistics_shop_retreat_rate_report AS a
             WHERE a.retreat_range = '5分钟以上'
               AND a.account_date ~ '^(2024-11|2024-12)'
             GROUP BY a.shop_id),

     bdg AS (SELECT a.base_shop_id
                  , SUM(a.pos_field16) AS budget_revenue
                  , SUM(a.pos_field44) AS budget_dine_in_revenue
                  , SUM(a.pos_field18) AS take_out_budget
             FROM data_budget_month AS a
             WHERE a.base_month ~ '^(2024-11|2024-12)'
             GROUP BY a.base_shop_id)

SELECT ROW_NUMBER() OVER ()                             AS id
     , NULL                                             AS object_id
     , NULL                                             AS group_id
     -- 门店id
     , a.base_shop_id                                   AS base_shop_id
     -- 时间
     , CONCAT(:targetMonth, '-01')                      AS date
     -- 门店桌数
     , SUM(a.base_field19)                              AS table_num
     -- 本期总实收
     , SUM(a.pos_field16)                               AS revenue
     -- 环期总实收
     , SUM(la.revenue)                                  AS revenue_l
     -- 同期总实收
     , SUM(pa.revenue)                                  AS revenue_p
     -- 本期累计至当前日期的总营收
     , SUM(a.pos_field16)                               AS acu_revenue
     -- 本期总应收
     , SUM(a.pos_field9)                                AS ar
     -- 环期总应收
     , SUM(la.ar)                                       AS ar_l
     -- 同期总应收
     , SUM(pa.ar)                                       AS ar_p
     -- 营收目标额
     , SUM(b.budget_revenue)                            AS budget_revenue
     -- 退菜金额
     , SUM(ret.retreat_money)                           AS retreat_money
     -- 折扣总金额
     , SUM(a.pos_field21)                               AS discount_amount
     -- 本期堂食实收
     , SUM(a.pos_field17)                               AS dine_in_revenue
     -- 本期堂食实收（含自提）
     , SUM(a.pos_field17 + a.pos_field19)               AS dine_in_rec
     -- 环期堂食实收（含自提）
     , SUM(la.dine_in_rec)                              AS dine_in_rec_l
     -- 同期堂食实收（含自提）
     , SUM(pa.dine_in_rec)                              AS dine_in_rec_p
     -- 本期堂食应收（含自提）
     , SUM(a.pos_field10 + a.pos_field12)               AS dine_in_ar
     -- 环期堂食应收（含自提）
     , SUM(la.dine_in_ar)                               AS dine_in_ar_l
     -- 同期堂食应收（含自提）
     , SUM(pa.dine_in_ar)                               AS dine_in_ar_p
     -- 堂食折扣金额
     , SUM(a.pos_field22)                               AS dine_in_discount_amount
     -- 堂食营收目标额
     , SUM(b.budget_dine_in_revenue)                    AS budget_dine_in_revenue
     -- 本期开桌数
     , SUM(a.pos_field2)                                AS tables_open
     -- 环期开桌数
     , SUM(la.tables_open)                              AS tables_open_l
     -- 同期开桌数
     , SUM(pa.tables_open)                              AS tables_open_p
     -- 本期来客数
     , SUM(a.pos_field7)                                AS cus_num
     -- 环期来客数
     , SUM(la.cus_num)                                  AS cus_num_l
     -- 同期来客数
     , SUM(pa.cus_num)                                  AS cus_num_p
     -- 本期堂食客流（含自提）
     , SUM(a.pos_field8 + COALESCE(a.pos_field46, 0.0)) AS cus_flow
     -- 环期堂食客流（含自提）
     , SUM(la.cus_flow)                                 AS cus_flow_l
     -- 同期堂食客流（含自提）
     , SUM(pa.cus_flow)                                 AS cus_flow_p
     -- 本期外卖实收
     , SUM(a.pos_field18)                               AS take_out_revenue
     -- 环期外卖实收
     , SUM(la.take_out_revenue)                         AS take_out_revenue_l
     -- 同期外卖实收
     , SUM(pa.take_out_revenue)                         AS take_out_revenue_p
     -- 本期外卖应收
     , SUM(a.pos_field11)                               AS take_out_ar
     -- 环期外卖应收
     , SUM(la.take_out_ar)                              AS take_out_ar_l
     -- 同期外卖应收
     , SUM(pa.take_out_ar)                              AS take_out_ar_p
     -- 外卖折扣金额
     , SUM(a.pos_field23)                               AS take_out_discount_amount
     -- 外卖实收目标额
     , SUM(b.take_out_budget)                           AS take_out_budget
     -- 本期外卖订单数
     , SUM(a.pos_field4)                                AS take_out_num
     -- 环期外卖订单数
     , SUM(la.take_out_num)                             AS take_out_num_l
     -- 同期外卖订单数
     , SUM(pa.take_out_num)                             AS take_out_num_p
FROM wt_shop AS a
         LEFT JOIN last AS la ON la.base_shop_id = a.base_shop_id
         LEFT JOIN pcp AS pa ON pa.base_shop_id = a.base_shop_id
         LEFT JOIN bdg AS b ON b.base_shop_id = a.base_shop_id
         LEFT JOIN ret ON ret.shop_id = a.base_shop_id
WHERE a.base_date ~ '^(2024-11)'
GROUP BY a.base_shop_id;

select a.pos_field16 AS day_revenue
     ,a.pos_field17 AS day_dine_in_revenue
     ,a.pos_field18 AS day_take_out_revenue
    from wt_shop AS a where a.base_date = '2024-07-01'