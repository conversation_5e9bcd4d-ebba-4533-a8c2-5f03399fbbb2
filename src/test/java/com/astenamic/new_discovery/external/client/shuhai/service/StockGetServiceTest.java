package com.astenamic.new_discovery.external.client.shuhai.service;

import com.astenamic.new_discovery.external.modal.dto.shuhai.StockInfoDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.request.StockGetRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class StockGetServiceTest {

    @Autowired
    private StockGetService stockGetService;

//    @Test
//    public void testGetStock() {
//        StockGetRequest req = new StockGetRequest();
//        req.setEnt("0W300003862");
//        req.setCustomerWarehouseCode("NBFX");
//        List<StockInfoDTO> stocks = stockGetService.post(req).getData();
//
//    }
}
