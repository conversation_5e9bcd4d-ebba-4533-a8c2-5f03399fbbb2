package com.astenamic.new_discovery.external.client.cool.vision.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class PatrolDetailListServiceTest {

    @Autowired
    private PatrolDetailListService patrolDetailListService;

    @Test
    public void test(){
//        patrolDetailListService.getPatrolDetailList(4256L, LocalDateTime.now().minusDays(1));
    }
}
