package com.astenamic.new_discovery.external.client.shuhai.service;

import com.astenamic.new_discovery.external.modal.dto.shuhai.ExpiryReportDTO;
import com.astenamic.new_discovery.external.modal.dto.shuhai.request.ExpiryReportRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class ExpiryReportServiceTest {

    @Autowired
    private ExpiryReportService expiryReportService;

//    @Test
//    void testGetExpiryReport() {
//        ExpiryReportRequest req = new ExpiryReportRequest();
//        req.setEnt("0W300003862");
//        req.setCustomerWarehouseCode("NBFX");
//
//        List<ExpiryReportDTO> report = expiryReportService.post(req).getData();
//
//        report.forEach(System.out::println);
//    }
}
