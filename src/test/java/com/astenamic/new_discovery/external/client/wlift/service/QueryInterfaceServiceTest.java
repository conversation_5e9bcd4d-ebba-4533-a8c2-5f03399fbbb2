package com.astenamic.new_discovery.external.client.wlift.service;

import com.astenamic.new_discovery.external.modal.dto.wlift.AccountBasicsInfoDTO;
import com.astenamic.new_discovery.external.modal.enums.BrandEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class QueryInterfaceServiceTest {

    @Autowired
    private QueryInterfaceService queryInterfaceService;

//    @Test
//    void queryAccountBasicsInfoTest(){
//        AccountBasicsInfoDTO accountBasicsInfoDTO = queryInterfaceService.queryAccountBasicsInfo(BrandEnum.XFX, "5283785");
//        System.out.println(accountBasicsInfoDTO);
//    }
}
