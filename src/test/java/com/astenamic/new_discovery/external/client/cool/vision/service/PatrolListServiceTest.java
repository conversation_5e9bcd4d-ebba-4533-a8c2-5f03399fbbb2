package com.astenamic.new_discovery.external.client.cool.vision.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class PatrolListServiceTest {

    @Autowired
    private PatrolListService patrolListService;

//    @Test
//    void testGetPatrolList() {
//        patrolListService.getPatrolList(LocalDateTime.now().minusDays(1));
//    }
}
