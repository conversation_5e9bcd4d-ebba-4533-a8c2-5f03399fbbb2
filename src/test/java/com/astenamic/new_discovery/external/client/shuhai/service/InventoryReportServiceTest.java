package com.astenamic.new_discovery.external.client.shuhai.service;

import com.astenamic.new_discovery.external.modal.dto.shuhai.request.InventoryReportRequest;
import com.astenamic.new_discovery.external.modal.dto.shuhai.InventoryReportDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class InventoryReportServiceTest {

    @Autowired
    private InventoryReportService inventoryReportService;

//    @Test
//    void testGetInventoryReport() {
//        InventoryReportRequest req = new InventoryReportRequest();
//        req.setEnt("0W300003862");
//        req.setCustomerWarehouseCode("HZQR");
//
//        List<InventoryReportDTO> reports = inventoryReportService.post(req).getData();
//
//        reports.forEach(System.out::println);
//    }
}
