package com.astenamic.new_discovery.biz.business.domain.service;


import com.astenamic.new_discovery.biz.business.application.service.MaterialShopReportAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MaterialShopReportServiceTest {

    @Autowired
    private MaterialShopReportAppService materialShopReportAppService;

//    @Test
//    public void testGetListByTimeFromDB() {
//        // Test with a specific date
//        materialShopReportService.getListByTimeFromDB(LocalDateTime.now().minusDays(1));
//
//        // Test with null dat
//    }
}
