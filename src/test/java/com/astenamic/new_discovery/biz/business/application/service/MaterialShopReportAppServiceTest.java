package com.astenamic.new_discovery.biz.business.application.service;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class MaterialShopReportAppServiceTest {

    @Autowired
    private MaterialShopReportAppService materialShopReportAppService;

    @Test
    public void testSyncMaterialDaily() {
//        materialShopReportAppService.syncMaterialDaily(LocalDateTime.now().minusDays(1));
    }
}
