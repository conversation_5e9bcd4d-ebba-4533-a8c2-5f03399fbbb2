package com.astenamic.new_discovery.biz.business.application.service;

import com.astenamic.new_discovery.biz.business.domain.entity.ShopRelation;
import com.astenamic.new_discovery.biz.business.domain.repository.ShopRelationRepository;
import com.astenamic.new_discovery.biz.takeout.entity.ShopRelationship;
import com.astenamic.new_discovery.biz.takeout.repository.ShopRelationshipRepository;
import com.astenamic.new_discovery.external.client.cool.vision.service.StoreListService;
import com.astenamic.new_discovery.external.modal.dto.cool.vision.StoreDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

@SpringBootTest
public class ShopRelationAppServiceTest {

    @Autowired
    private ShopRelationAppService shopRelationAppService;

    @Autowired
    private StoreListService storeListService;

    @Autowired
    private ShopRelationshipRepository shopRelationshipRepository;

    @Autowired
    private ShopRelationRepository shopRelationJpaAdapter;

    @Autowired
    private ShopRelationRepository shopRelationYidaAdapter;

//    @Test
//    public void test(){
//        shopRelationAppService.syncRelations();
//    }
//
//    @Test
//    public void testGetRelations() {
//
//        /**
//         * 1、获取数据
//         */
//        List<ShopRelation> shopRelations = shopRelationYidaAdapter.findAll();
//
//        List<StoreDTO> storeList = storeListService.getStoreList(1L);
//
//        List<ShopRelationship> shopRelationships = shopRelationshipRepository.findAll();
//
//        /**
//         * 酷巡店id
//         */
//        for (ShopRelation shopRelation : shopRelations) {
//            if (shopRelation.getShopSysId() == null || shopRelation.getShopSysId().isEmpty()) {
//                continue;
//            }
//            for (StoreDTO storeDTO : storeList) {
//                if (StringUtils.equals(shopRelation.getShopSysId().get(0), storeDTO.getSynDingDeptId())) {
//                    shopRelation.setKuxundianId(storeDTO.getStoreId());
//                    break;
//                }
//            }
//        }
//
//        /**
//         * 其它id
//         */
//        for (ShopRelation shopRelation : shopRelations) {
//            for (ShopRelationship shopRelationship : shopRelationships) {
//                if (StringUtils.equals(shopRelation.getShopId(), shopRelationship.getShopId())) {
//                    shopRelation.setDouyinId(shopRelationship.getDyId());
//                    shopRelation.setDazhongdianpingId(shopRelationship.getDpId());
//                    shopRelation.setElemeId(shopRelationship.getElmId());
//                    shopRelation.setMeituanId(shopRelationship.getMtThirdsShopId());
//                    shopRelation.setPinzhiId(shopRelationship.getPingzhiId());
//                    shopRelation.setWliftId(shopRelationship.getCrmId());
//                    shopRelation.setAoqiweiId(shopRelationship.getAcewillId());
//                    break;
//                }
//            }
//        }
//
//        shopRelationYidaAdapter.saveAll(shopRelations);
//
//
//    }
}
