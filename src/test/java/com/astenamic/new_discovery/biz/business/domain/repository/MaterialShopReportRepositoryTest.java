package com.astenamic.new_discovery.biz.business.domain.repository;


import com.astenamic.new_discovery.biz.kitchendivision.infrastructure.persistence.MaterialCostCardRepositoryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MaterialShopReportRepositoryTest {


    @Autowired
    private MaterialCostCardRepositoryImpl repository;

//    @Test
//    void testFindAllMaterialCostCard() {
//        List<MaterialCostCard> result = repository.findAllMaterialCostCard(LocalDateTime.now().minusDays(1));
//        assertNotNull(result, "Result should not be null");
//    }

}
