package com.astenamic.new_discovery.biz.business.application.service;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;

@SpringBootTest
public class PatrolRecordAppServiceTest {

    @Autowired
    private PatrolRecordAppService recordAppService;

    @Test
    public void test() {
//        LocalDate today = LocalDate.now();                  // 今天
//        LocalDate first = today.withDayOfMonth(1);          // 当月第一天
//        LocalDate last  = today;                            // 若只到昨天: today.minusDays(1)
//
//        // 迭代日期流：first ～ last
//        first.datesUntil(last.plusDays(1))                  // plusDays(1) 让 last 包含在内
//                .forEach(day -> {
//                    LocalDateTime startOfDay = day.atStartOfDay();
//                    recordAppService.syncPatrolRecord(startOfDay);
//                });
    }
}
