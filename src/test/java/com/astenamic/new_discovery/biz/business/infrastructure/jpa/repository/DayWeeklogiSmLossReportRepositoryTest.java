package com.astenamic.new_discovery.biz.business.infrastructure.jpa.repository;

import com.astenamic.new_discovery.biz.business.infrastructure.jpa.dao.DayWeeklogiSmLossReportJpaDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class DayWeeklogiSmLossReportRepositoryTest {

    @Autowired
    private DayWeeklogiSmLossReportJpaDao dayWeeklogiSmLossReportJpaDao;

//    @Test
//    public void testFindByReportDate() {
//        LocalDate startDate = LocalDate.of(2025, 5, 27);
//        LocalDate endDate = LocalDate.of(2025, 5, 27);
//        List<MaterialReportDTO> reports = dayWeeklogiSmLossReportRepository.weeklyTopLoss("2025-05-27", "2025-06-02");
//    }
}
