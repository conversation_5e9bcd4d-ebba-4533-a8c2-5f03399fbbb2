//package com.astenamic.new_discovery.biz.business.infrastructure.service;
//
//import com.astenamic.new_discovery.biz.business.application.service.WeeklyMaterialReportService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//@SpringBootTest
//public class WeeklyMaterialReportServiceTest {
//
//    @Autowired
//    private WeeklyMaterialReportService weeklyMaterialReportService;
//
////    @Test
////    public void testGenerateWeeklyReport() {
////        // 调用服务方法生成周报
////        weeklyMaterialReportService.weeklyMaterialReport(LocalDateTime.now().minusDays(9));
////    }
//}
