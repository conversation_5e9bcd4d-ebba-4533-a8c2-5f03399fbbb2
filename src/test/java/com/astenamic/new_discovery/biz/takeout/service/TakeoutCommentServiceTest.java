package com.astenamic.new_discovery.biz.takeout.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.LocalTime;

@SpringBootTest
public class TakeoutCommentServiceTest {

    @Autowired
    private TakeoutCommentService takeoutCommentService;

//    @Test
//    public void testSyncComments() {
//        takeoutCommentService.syncComments(LocalDateTime.now().minusDays(3).with(LocalTime.MIN), LocalDateTime.now().minusDays(1).with(LocalTime.MAX));
//    }
}
