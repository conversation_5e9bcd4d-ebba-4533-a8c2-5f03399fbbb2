package com.astenamic.new_discovery.biz.kitchendivision.domain.service;

import com.astenamic.new_discovery.biz.kitchendivision.application.service.MaterialCostCardAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MaterialCostCardServiceTest {

    @Autowired
    private MaterialCostCardAppService materialCostCardAppService;

//    @Test
//    public void testSyncToYida() {
//        materialCostCardService.syncToYida(LocalDateTime.now().minusDays(1));
//    }
}
