package com.astenamic.new_discovery.biz.kitchendivision.domain.service;


import com.astenamic.new_discovery.biz.kitchendivision.application.service.HuiyunbanService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class HuiyunbanServiceTest {

    @Autowired
    private HuiyunbanService huiyunbanService;

    @Test
    public void testBuildData() {
//        huiyunbanService.exportData("下沙龙湖","FINST-NLF66IA1KTQWGWNADI1X27LD7E73282FVNICMDL", "FINST-ES666GA1MUQWYEB08N9FI88FHQT532BG0OICMMS");
    }
}
