package com.astenamic.new_discovery.biz.report.shop.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class ShopMonthReportServiceTest {

    @Autowired
    private ShopMonthReportService shopMonthReportService;

//    @Test
//    public void testSaveMaterialDaily() {
//        shopMonthReportService.saveMaterialDaily(LocalDateTime.now().minusDays(1));
//    }
}
