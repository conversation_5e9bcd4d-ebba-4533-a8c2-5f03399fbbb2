package com.astenamic.new_discovery.biz.flow.warning.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class MemberConsumptionCountServiceTest {

    @Autowired
    private MemberConsumptionCountService memberConsumptionCountService;

//    @Test
//    public void testMemberConsumptionCount() {
//        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
//        memberConsumptionCountService.memberConsumptionWarning(yesterday);
//    }
}
