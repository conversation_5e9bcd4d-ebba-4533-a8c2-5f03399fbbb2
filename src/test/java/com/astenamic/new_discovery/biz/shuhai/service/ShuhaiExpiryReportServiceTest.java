package com.astenamic.new_discovery.biz.shuhai.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class ShuhaiExpiryReportServiceTest {

    @Autowired
    private ShuhaiExpiryReportService shuhaiExpiryReportService;

//    @Test
//    public void testGetStock() {
//        shuhaiExpiryReportService.syncExpiryReportToDb();
//    }
}
