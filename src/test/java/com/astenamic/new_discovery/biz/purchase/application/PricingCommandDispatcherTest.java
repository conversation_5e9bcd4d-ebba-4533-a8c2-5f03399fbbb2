package com.astenamic.new_discovery.biz.purchase.application;


import com.astenamic.new_discovery.biz.purchase.application.command.StartSupplierQuotationCmd;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PricingCommandDispatcherTest {

    @Autowired
    private PricingCommandDispatcher dispatcher;

//    @Test
//    public void startProductPricingFlowCmdTest(){
//        dispatcher.dispatch(new StartProductPricingFlowReq());
//    }

//    @Test
//    public void syncPricingToDbCmdTest(){
//        dispatcher.dispatch(new SyncPricingToDbReq());
//    }

//    @Test
//    public void createPricingReviewCmdTest() {
//         dispatcher.dispatch(new CreatePricingReviewReq("新品","FLOW-DJA1-0114","FLOW-NP01-0144"));
//    }
//
//    @Test
//    public void startSupplierQuotationCmdTest() {
//        dispatcher.dispatch(new StartSupplierQuotationReq("FLOW-DJA1-0110"));
//    }

//    @Test
//    public void startQuotationFlowCmdTest(){
//        dispatcher.dispatch(new StartQuotationReq("FLOW-DJA1-0127"));
//    }
}
