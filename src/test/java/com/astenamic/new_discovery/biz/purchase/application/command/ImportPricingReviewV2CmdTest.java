package com.astenamic.new_discovery.biz.purchase.application.command;

import com.astenamic.new_discovery.biz.purchase.application.command.dto.ImportPricingReviewV2Req;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class ImportPricingReviewV2CmdTest {

    @Autowired
    private ImportPricingReviewV2Cmd importPricingReviewV2Cmd;

    @Test
    public void testImportPricingReviewV2Cmd() {
//        importPricingReviewV2Cmd.execute(
//                new ImportPricingReviewV2Req("https://ohcqn6.aliwork.com/ossFileHandle?spm=a2q5o.26736372.0.0.454c6dearjB2wK&appType=APP_VAWCFBK8UUNBTJINOCWQ&fileName=APP_VAWCFBK8UUNBTJINOCWQ_Zjc2YTJlZjFiNmVkNDgzMmFlMDU3YzIwODU4OTA4MThfUzBFNjYwQTFXQ0tYMDdVSjZENlg0QVYwNjZZODNSVjU1MVJETUU2.xlsx&instId=1d5944d5-0d3d-40fe-ad08-115427ce2de6&type=download", "FLOW-DJA1-0322"));
    }

}
