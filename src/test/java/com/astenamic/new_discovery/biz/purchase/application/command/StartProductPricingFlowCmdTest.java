package com.astenamic.new_discovery.biz.purchase.application.command;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class StartProductPricingFlowCmdTest {

    @Autowired
    public StartProductPricingFlowCmd startProductPricingFlowCmd;

//    @Test
//    public void testStartProductPricingFlowCmd() {
//        // This method is intended to test the StartProductPricingFlowCmd functionality.
//        // You can add assertions and logic to verify the command's behavior.
//        startProductPricingFlowCmd.test("甜笋类");
//    }
}
