package com.astenamic.new_discovery.biz.purchase.application.command;


import com.astenamic.new_discovery.biz.purchase.application.command.dto.ExportPricingReviewReq;
import com.astenamic.new_discovery.biz.purchase.application.command.dto.ExportPricingReviewV2Req;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@SpringBootTest
public class ExportPricingReviewV2CmdTest {

    @Autowired
    private ExportPricingReviewV2Cmd exportPricingReviewV2Cmd;

    @Test
    public void testExportPricingReviewV2Cmd() throws Exception {
        Path outPath = Paths.get("target", "export", "定价评审_FLOW-DJA1-0317_"+System.currentTimeMillis()+".xlsx");
        Files.createDirectories(outPath.getParent());           // 2) 创建父目录
        try (OutputStream out = new BufferedOutputStream(Files.newOutputStream(outPath))) {
            exportPricingReviewV2Cmd.execute(
                    new ExportPricingReviewV2Req(out, "FLOW-DJA1-0317"));
        }
    }

}
