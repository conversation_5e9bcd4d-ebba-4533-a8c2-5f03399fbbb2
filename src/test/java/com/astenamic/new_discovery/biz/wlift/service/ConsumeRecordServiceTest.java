package com.astenamic.new_discovery.biz.wlift.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class ConsumeRecordServiceTest {

    @Autowired
    private ConsumeRecordService consumeRecordService;

//    @Test
//    public void testSyncConsumeRecord() {
//        consumeRecordService.syncConsumeRecord(LocalDateTime.now().minusDays(1));
//    }
}
